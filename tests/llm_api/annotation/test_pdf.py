import os

import pytest
from moto import mock_aws

from llm_api.annotation.pdf import AnnotatePDF, AnnotatePDFSettings, PDFSearch
from llm_api.blai_api.dtos import LTRB, PDFAnnotationRequest, PDFCoordinates
from tests.llm_api import PDF_TEST_PATHS


def test_settings_environment_var_load_for_annotate_pdf(tmp_path):
    bucket_name = "bucket-name"
    os.environ["S3_BUCKET"] = bucket_name
    settings = AnnotatePDFSettings(local_root=tmp_path)
    assert settings.s3_bucket == bucket_name


@pytest.mark.parametrize("sample_pdf_path", PDF_TEST_PATHS)
def test_local_annotate_pdf(tmp_path, sample_pdf_path):
    annotate = AnnotatePDF(
        settings=AnnotatePDFSettings(s3_bucket=None, local_root=tmp_path)
    )
    req = PDFAnnotationRequest(
        source=sample_pdf_path.as_posix(),
        coordinates=[
            PDFCoordinates(
                coord=LTRB(x0=200.3, y0=30.2, x1=220.3, y1=300.2),
                page_number=1,
                category="Text",
                layout_width=20,
            ),
            PDFCoordinates(
                coord=LTRB(x0=200.3, y0=30.2, x1=220.3, y1=300.2),
                page_number=1,
                category="Text",
                layout_width=20,
                excerpt="Lorem ipsum dolor sit amet",
            ),
        ],
    )
    resp = annotate(req)
    assert resp.exists()


params = list(map(list, zip(PDF_TEST_PATHS, PDF_TEST_PATHS)))


@pytest.mark.parametrize("mock_datastore, sample_req", params, indirect=True)
def test_s3_annotate_pdf(tmp_path, mock_bucket, mock_datastore, sample_req):
    settings = AnnotatePDFSettings(s3_bucket=mock_bucket, local_root=tmp_path)
    annotate = AnnotatePDF(settings=settings)
    with mock_aws():
        resp = annotate(sample_req)
    assert resp.exists()


@pytest.mark.asyncio
@pytest.mark.parametrize("mock_datastore, sample_req", params, indirect=True)
async def test_s3_async_annotate_pdf(tmp_path, mock_bucket, mock_datastore, sample_req):
    settings = AnnotatePDFSettings(s3_bucket=mock_bucket, local_root=tmp_path)
    annotate = AnnotatePDF(settings=settings)
    with mock_aws():
        resp = await annotate.acall(sample_req)
    assert resp.exists()


EXCERPTS = {
    "Bricklayer - Procedures API Endpoint Details.pdf": "This is an example of when the run is NOT over. The status is thus “AIAnalysis”. Complete runs will have the status “HumanReview” and must be marked as completed in the UI to change to “Completed”.",
    "Bricklayer Messages API Documentation.pdf": "Retrieves a list of enabled tools for the organization, filtered by type and configuration. The response provides minimal details, including tool ID, name, and description.",
    "Bricklayer Procedures API Step-by-Step Instructions.pdf": "4) Once the alert has been processed, the report can be fetched using the returning value from step 3. Detailed steps are in the other attached document.",
    "Globex Corporation Alert Escalation Policy.pdf": "This policy establishes standardized escalation procedures for the Security Operations Center (SOC) when responding to security alerts. It ensures proper notification, containment, and response based on asset criticality, network location, and user impact.",
    "Report (8).pdf": "A significant increase in phishing campaigns was observed, with attackers leveraging current events to craft convincing emails that tricked users into revealing sensitive information. A high-profile ransomware attack targeted a major healthcare provider, resulting in the encryption of patient data and prompting urgent response measures to mitigate the impact. New vulnerabilities were disclosed in widely used content management systems, underscoring the importance of timely updates and security patches to prevent exploitation. A coordinated effort by law enforcement agencies led to the takedown of a notorious cybercrime group responsible for multiple data breaches across various industries. Emerging trends indicate a rise in supply chain attacks, with threat actors increasingly targeting third-party vendors to gain access to larger networks.",
}


@pytest.mark.parametrize("sample_pdf_path", PDF_TEST_PATHS)
def test_pdfsearch_finds_text(sample_pdf_path):
    search = PDFSearch(case_sensitive=False, hit_max=1, category="TestCategory")

    search_string = EXCERPTS[sample_pdf_path.name]

    results = search(pdf=sample_pdf_path, search_string=search_string)

    assert len(results) == 1
    assert isinstance(results, list)
