import os
from pathlib import Path

import boto3
import pytest
from moto import mock_aws

from llm_api.blai_api.dtos import LTRB, PDFAnnotationRequest, PDFCoordinates


@pytest.fixture
def sample_pdf_path(request) -> Path:
    return Path(request.param)


@pytest.fixture
def sample_data_key(request) -> str:
    return Path(request.param).name


@pytest.fixture
def sample_req(request) -> PDFAnnotationRequest:
    req = PDFAnnotationRequest(
        source=request.param.as_posix(),
        coordinates=[
            PDFCoordinates(
                coord=LTRB(x0=200.3, y0=30.2, x1=220.3, y1=300.2),
                page_number=1,
                category="Text",
                layout_width=20,
            )
        ],
    )
    return req


@pytest.fixture(scope="function")
def aws_credentials():
    """Mocked AWS Credentials for moto."""
    os.environ["AWS_ACCESS_KEY_ID"] = "testing"
    os.environ["AWS_SECRET_ACCESS_KEY"] = "testing"
    os.environ["AWS_SECURITY_TOKEN"] = "testing"
    os.environ["AWS_SESSION_TOKEN"] = "testing"
    os.environ["AWS_DEFAULT_REGION"] = "us-east-1"
    current_aws_profile = os.environ.pop("AWS_PROFILE", None)

    yield
    # Clean up the environment variables after the test
    if current_aws_profile is not None:
        os.environ["AWS_PROFILE"] = current_aws_profile


@pytest.fixture(scope="function")
def mock_bucket(aws_credentials):
    with mock_aws():
        bucket_name = "bltest"
        s3 = boto3.client("s3", region_name="us-east-1")
        s3.create_bucket(Bucket=bucket_name)
        yield bucket_name


@pytest.fixture(scope="function")
def mock_datastore(request, mock_bucket, aws_credentials):
    with mock_aws():
        s3 = boto3.client("s3", region_name="us-east-1")
        s3.upload_file(request.param, mock_bucket, request.param.name)
        yield request.param.name
