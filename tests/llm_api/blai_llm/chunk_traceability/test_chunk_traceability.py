import pytest
from langchain_core.documents import Document
from langchain_text_splitters import RecursiveCharacterTextSplitter

from llm_api.blai_llm.chunk_traceability import (
    ChunkDocNameIDFunction,
    ChunkIndexHashMetaDataFunction,
    get_split_chunks_with_metadata,
    retry,
)
from tests.llm_api import PDF_TEST_PATHS, REC_SPLIT_LENS


def test_retry_calls():
    global calls
    calls = 0

    @retry(ValueError, 3, 0.01, 1)
    def f():
        global calls
        calls += 1
        if calls <= 2:
            raise ValueError()

    f()
    assert calls == 3


def test_retry_other():
    global calls
    calls = 0

    @retry(ValueError, 3, 0.01, 1)
    def f():
        global calls
        calls += 1
        raise NotImplementedError()

    with pytest.raises(NotImplementedError):
        f()
    assert calls == 1


def test_chunk_doc_name_id_function():
    cdnif = ChunkDocNameIDFunction(doc_name="test_name")
    x = cdnif(1, Document("This is page content."))
    # Same page content
    y = cdnif(1, Document("This is page content."))
    # Different page content
    z = cdnif(1, Document("This is page content"))
    # Different ID
    zi = cdnif(2, Document("This is page content."))
    assert x == y and x == z and y == z
    assert x != zi and y != zi and z != zi

    # Different doc_name
    cdnif2 = ChunkDocNameIDFunction(doc_name="other_test_name")
    a = cdnif2(1, Document("This is page content."))
    assert a != x


params = list(map(list, zip(PDF_TEST_PATHS, REC_SPLIT_LENS)))


@pytest.mark.parametrize(
    "sample_pdf_unstructured_loader, split_len", params, indirect=True
)
def test_get_split_chunks_with_metadata(sample_pdf_unstructured_loader, split_len):
    text_splitter = RecursiveCharacterTextSplitter()
    id_func = ChunkDocNameIDFunction(doc_name="test_name")
    metadata_funcs = [ChunkIndexHashMetaDataFunction(doc_name="test_name")]
    chunks = get_split_chunks_with_metadata(
        text_splitter, sample_pdf_unstructured_loader, id_func, metadata_funcs
    )
    assert len(chunks) == split_len

    doc = chunks[0].page_content
    small_doc = doc[: min(170, len(doc))]
    assert len(small_doc) == 170 or len(small_doc) == len(doc)
