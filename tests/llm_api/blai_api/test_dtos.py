import pytest

from llm_api.blai_api.dtos import LTRB, Evidence, PDFCoordinates

# Inject Traces
args = "sample_evidence"
params = [
    None,
    PDFCoordinates(
        coord=LTRB(x0=200.3, y0=30.2, x1=220.3, y1=300.2),
        page_number=10,
        category="Text",
        layout_width=20,
    ),
    PDFCoordinates(
        coord=LTRB(x0=200.3, y0=30.2, x1=220.3, y1=300.2),
        page_number=10,
        category="Text",
        layout_width=20,
        excerpt="Lorem ipsum dolor sit amet, consectetuer",
    ),
]


@pytest.mark.parametrize(args, params, indirect=True)
def test_construct_evidence(sample_evidence):
    match sample_evidence:
        case Evidence(trace=None):
            ...
        case Evidence(trace=trace):
            ...
        case _:
            raise
