from unittest.mock import MagicMock, patch

import pytest
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_unstructured import UnstructuredLoader

from llm_api.annotation.pdf import PDFSearch
from llm_api.blai_llm.chunk_traceability import (
    ChunkDocNameIDFunction,
    ChunkIndexHashMetaDataFunction,
    get_split_chunks_with_metadata,
)
from llm_api.html_to_pdf.html_to_pdf_converter import ArticleToPDF
from tests.llm_api import BLOGS_TEST_PATHS


@pytest.mark.parametrize("html_file_path", BLOGS_TEST_PATHS)
@patch("playwright.sync_api.sync_playwright")
def test_html_to_pdf_converter_with_real_html(
    mock_playwright, html_file_path, tmp_path
):
    # Read HTML from file
    html_content = html_file_path.read_text(encoding="utf-8")

    # <PERSON><PERSON>wright to avoid real browser launch and page calls
    mock_browser = MagicMock()
    mock_page = MagicMock()
    mock_playwright.return_value.__enter__.return_value.chromium.launch.return_value = (
        mock_browser
    )
    mock_browser.new_page.return_value = mock_page
    mock_page.content.return_value = html_content
    mock_page.pdf.return_value = b"%PDF-1.4 fake pdf content"

    class DummyFetcher:
        def get(self):
            return "Test Title", html_content

    fetcher = DummyFetcher()
    converter = ArticleToPDF()
    output_path = tmp_path / f"{html_file_path.stem}.pdf"

    converter(fetcher, output_path)

    assert output_path.exists()
    assert output_path.stat().st_size > 0


@pytest.mark.parametrize("html_file_path", BLOGS_TEST_PATHS)
@patch("playwright.sync_api.sync_playwright")
def test_split_chunks_searchable_via_pdfsearch(
    mock_playwright, html_file_path, tmp_path
):
    html_content = html_file_path.read_text(encoding="utf-8")

    mock_browser = MagicMock()
    mock_page = MagicMock()
    mock_playwright.return_value.__enter__.return_value.chromium.launch.return_value = (
        mock_browser
    )
    mock_browser.new_page.return_value = mock_page
    mock_page.content.return_value = html_content
    mock_page.pdf.return_value = b"%PDF-1.4 fake pdf content"

    class DummyFetcher:
        def get(self):
            return "Test Title", html_content

    fetcher = DummyFetcher()
    converter = ArticleToPDF()
    pdf_path = tmp_path / f"{html_file_path.stem}.pdf"

    converter(fetcher, pdf_path)

    assert pdf_path.exists() and pdf_path.stat().st_size > 0

    pdf_loader = UnstructuredLoader(file_path=pdf_path)

    splitter = RecursiveCharacterTextSplitter(chunk_size=2000, chunk_overlap=20)
    chunks = get_split_chunks_with_metadata(
        splitter,
        pdf_loader,
        id_function=ChunkDocNameIDFunction(doc_name="test_doc"),
        metadata_functions=[ChunkIndexHashMetaDataFunction(doc_name="test_doc")],
    )

    assert chunks, "No chunks were produced from the PDF"

    search = PDFSearch(case_sensitive=False, hit_max=1, category="TestSearch")

    failures = 0
    successes = 0
    for chunk in chunks:
        search_text = chunk.page_content

        results = search(pdf=pdf_path, search_string=search_text)
        if not results:
            failures += 1
        else:
            successes += 1

    assert successes / len(chunks) > 0.90
