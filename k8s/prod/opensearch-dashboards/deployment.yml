apiVersion: apps/v1
kind: Deployment
metadata:
    name: opensearch-dashboards
    namespace: production
spec:
    replicas: 1
    selector:
        matchLabels:
            app: opensearch-dashboards
    template:
        metadata:
            labels:
                app: opensearch-dashboards
        spec:
            serviceAccountName: prod-deploy
            volumes:
              - name: opense-prod-secrets-store-inline
                csi:
                  driver: secrets-store.csi.k8s.io
                  readOnly: true
                  volumeAttributes:
                    secretProviderClass: "aws-secrets-opense-prod"
            containers:
                - name: opensearch-dashboards
                  image: opensearchproject/opensearch-dashboards:2.15.0
                  ports:
                      - containerPort: 5601
                  volumeMounts:
                    - name: opense-prod-secrets-store-inline
                      mountPath: "/mnt/secrets-store"
                      readOnly: true
                  env:
                      - name: OPENSEARCH_HOSTS
                        value: '["https://vpc-blogs-z2ncapxxgmt3xnc37cxmc3mw5m.us-east-1.es.amazonaws.com:443"]'
                  command:
                      - /bin/sh
                      - -c
                      - |
                          curl -L -o ~/bin/jq https://github.com/stedolan/jq/releases/download/jq-1.6/jq-linux64
                          chmod +x ~/bin/jq
                          export PATH=~/bin:$PATH
                          export OPENSEARCH_USERNAME=$(jq -r '.username' /mnt/secrets-store/opense-secrets)
                          export OPENSEARCH_PASSWORD=$(jq -r '.password' /mnt/secrets-store/opense-secrets)
                          exec /usr/share/opensearch-dashboards/bin/opensearch-dashboards --opensearch.username "$OPENSEARCH_USERNAME" --opensearch.password "$OPENSEARCH_PASSWORD" --opensearch.hosts "$OPENSEARCH_HOSTS"
