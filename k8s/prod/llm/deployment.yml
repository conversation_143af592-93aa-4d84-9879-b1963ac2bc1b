apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: production
  name: llm-deployment-prod
spec:
  selector:
    matchLabels:
      app: llm
  template:
    metadata:
      labels:
        app: llm
    spec:
      serviceAccountName: prod-deploy
      containers:
        - name: llm-app
          image: ************.dkr.ecr.us-east-1.amazonaws.com/bricklayerai-llm:R1
          imagePullPolicy: Always
          ports:
            - containerPort: 9000
          command:
            - bash
            - -c
            - |
              export POSTGRES_USER=$(jq -r '.username' /mnt/rds-store/rds-secrets)
              export POSTGRES_PASS=$(jq -r '.password' /mnt/rds-store/rds-secrets)
              uvicorn llm_api.blai_api.main:get_app --factory --host 0.0.0.0 --limit-max-requests $UVICORN_WORKERS_MAX_REQUESTS --log-level $LLM__LOGGING__LEVEL --port $PORT --workers $WORKERS
          envFrom:
            - configMapRef:
                name: llm-configmap-prod
          volumeMounts:
            - name: llm-prod-secrets-store-inline
              mountPath: "/mnt/secrets-store"
              readOnly: true
            - name: rds-prod-secrets-store-inline
              mountPath: "/mnt/rds-store"
              readOnly: true 
          livenessProbe:
            httpGet:
              path: /healthcheck
              port: 9000
            initialDelaySeconds: 180
            periodSeconds: 15
            timeoutSeconds: 3
            failureThreshold: 5
          readinessProbe:
            httpGet:
              path: /healthcheck
              port: 9000
            initialDelaySeconds: 180
            periodSeconds: 15
            timeoutSeconds: 2
            failureThreshold: 5
      volumes:
        - name: llm-prod-secrets-store-inline
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: aws-secrets-llm-prod
        - name: rds-prod-secrets-store-inline
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: "aws-secrets-rds-prod"
      nodeSelector:
        nodes-group: llm-node-group
