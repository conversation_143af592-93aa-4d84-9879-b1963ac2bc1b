---
apiVersion: v1
kind: ConfigMap
metadata:
  namespace: production
  name: llm-configmap-prod
data:
  BLAI_ENV: prod
  BLOG_SAMPLES_PATH: /data/blogs
  CORS_ORIGINS: "*"
  CUSTOM_RAG_CHAIN_ENABLED: "True"
  DOCKER_HOST: "tcp://dind-service-prod:2376"
  FEEDLY_ARTICLES_COUNT: "250"
  FEEDLY_MAX_PAGES: "2"
  FEEDLY_NEWER_THAN: "1692086400"
  FEEDLY_UNREAD_ONLY: "True"
  FF_RAISE_SPECIFIC_CONTENT_FILTERING_ERROR: "1"
  FF_USE_MODIFIED_BLOGS_TOOL_PROMPT: "1"
  FF_CSV_DATASTORES_ENABLED: "True"  
  LLM_CHAT_MEMORY: "True"
  LLM__CONVERSATION__BUCKET__URL: "s3://blai-conversations-production-20250219044702079400000001"
  LLM__LOGGING__FORMAT: "%(message)s"
  LLM__LOGGING__LEVEL: "debug"
  LLM__OPENSEARCH__HOST: vpc-blogs-z2ncapxxgmt3xnc37cxmc3mw5m.us-east-1.es.amazonaws.com
  LLM__OPENSEARCH__INDEXNAME: "blogs_prod_25_03_2025"
  BLOGS_PGVECTOR_COLLECTION_NAME: "blogs_prod_25_03_2025"
  LLM__OPENSEARCH__PORT: "443"
  LLM__VECTORSTOREANDKEYWORDS__STRATEGY: seq
  PUBLIC_BACKEND_URL: http://backend-service-prod:8000/api/v1
  RATE_LIMIT_BUCKET_SIZE: "1"
  RATE_LIMIT_CHECK_FREQ: "1"
  RATE_LIMIT_RPS: "3"
  REDIS_PORT: "6379"
  REDIS_URL: "blai-prod-redis.xmnq28.ng.0001.use1.cache.amazonaws.com"
  SENTRY_DSN: https://<EMAIL>/4508738019393536
  SENTRY_PROFILES_SAMPLE_RATE: "0.5"
  SENTRY_TRACES_SAMPLE_RATE: "0.8"
  USE_OPENAI_FUNCTION_CALLING: "True"
  USE_REDIS_RATE_LIMITER: "False"
  USE_SEMANTIC_CHUNKING: "True"
  DATASTORE: '{"enable_chunk_traceability":true}'
  DATASTORE_TOOL_RETRIEVER_MODE: "VECTOR"
  ANNOTATE: '{"local_root":"/tmp/annotations/"}'
  WORKERS: "12"
