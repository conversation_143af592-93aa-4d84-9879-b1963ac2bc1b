apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: production
  name: dind-deployment-prod
spec:
  replicas: 1
  selector:
    matchLabels:
      app: dind
  template:
    metadata:
      labels:
        app: dind
    spec:
      containers:
        - name: dind
          image: 225989354984.dkr.ecr.us-east-1.amazonaws.com/dind:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 2376
          securityContext:
            privileged: true
          args: ["dockerd", "--host=tcp://0.0.0.0:2376", "--tlsverify", "--tlscacert=/dind-certs/ca.pem", "--tlscert=/dind-certs/server-cert.pem", "--tlskey=/dind-certs/server-key.pem"]
          envFrom:
            - configMapRef:
                name: dind-configmap-prod
          volumeMounts:
            - mountPath: /var/lib/docker
              name: dind-ephemeral-storage
            - mountPath: /sys/fs/cgroup
              name: sys-fs-cgroup
          readinessProbe:
            exec:
              command:
                - docker
                - --host=tcp://localhost:2376
                - --tlsverify
                - --tlscacert=/docker-certs/ca.pem
                - --tlscert=/docker-certs/client-cert.pem
                - --tlskey=/docker-certs/client-key.pem
                - info
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 5
          livenessProbe:
            exec:
              command:
                - docker
                - --host=tcp://localhost:2376
                - --tlsverify
                - --tlscacert=/docker-certs/ca.pem
                - --tlscert=/docker-certs/client-cert.pem
                - --tlskey=/docker-certs/client-key.pem
                - info
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 5
            failureThreshold: 5
      restartPolicy: Always
      volumes:
        - name: dind-ephemeral-storage
          emptyDir: {}
        - name: sys-fs-cgroup
          hostPath:
            path: /sys/fs/cgroup
            type: Directory
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 1
