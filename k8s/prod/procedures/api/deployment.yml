apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: production
  name: procedures-api-deployment-prod
spec:
  replicas: 1
  selector:
    matchLabels:
      app: procedures-api
  template:
    metadata:
      labels:
        app: procedures-api
    spec:
      serviceAccountName: prod-deploy
      containers:
        - name: procedures-api
          image: ************.dkr.ecr.us-east-1.amazonaws.com/procedures_api:R1
          imagePullPolicy: Always
          ports:
            - containerPort: 9001
          envFrom:
            - configMapRef:
                name: procedures-api-cm-prod
          command:
          - python
          - "-c"
          - 'from llm_api.cli.procedures import procedure_engine; procedure_engine(["api"]);'
          readinessProbe:
            exec:
              command:
              - /code/llm_api/procedures/celery/scripts/probe-readiness.py
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
          livenessProbe:
            exec:
              command:
              - /code/llm_api/procedures/celery/scripts/probe-liveness.py
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
      nodeSelector:
        nodes-group: llm-node-group
---
apiVersion: v1
kind: Service
metadata:
  namespace: production
  name: procedures-api-service-prod
  labels:
    app: procedures-api
spec:
  ports:
    - port: 9001 # Llm service listens on 9000 for internal traffic (coming from backend)
      targetPort: 9001
  selector:
    app: procedures-api
