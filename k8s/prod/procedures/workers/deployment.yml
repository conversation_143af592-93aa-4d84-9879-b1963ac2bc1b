apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: production
  name: procedures-workers-deployment-prod
spec:
  replicas: 3
  selector:
    matchLabels:
      app: procedures-workers
  template:
    metadata:
      labels:
        app: procedures-workers
    spec:
      serviceAccountName: prod-deploy
      containers:
        - name: procedures-api
          image: ************.dkr.ecr.us-east-1.amazonaws.com/procedures_workers:R1
          imagePullPolicy: Always
          envFrom:
            - configMapRef:
                name: procedures-workers-cm-prod
          command:
          - python
          - "-c"
          - 'from llm_api.cli.procedures import procedure_engine; procedure_engine(["workers"]);'
          readinessProbe:
            exec:
              command:
              - /code/llm_api/procedures/celery/scripts/probe-readiness.py
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
          livenessProbe:
            exec:
              command:
              - /code/llm_api/procedures/celery/scripts/probe-liveness.py
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
          volumeMounts:
            - name: llm-prod-secrets-store-inline
              mountPath: "/mnt/secrets-store"
              readOnly: true
      volumes:
        - name: llm-prod-secrets-store-inline
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: aws-secrets-llm-prod
      nodeSelector:
        nodes-group: llm-node-group
