---
apiVersion: v1
kind: ConfigMap
metadata:
  namespace: production
  name: bricklayer-api-configmap-prod
data:
  BLAI_ENV: prod
  BRICKLAYER_AWS_SES_IDENTITY_ARN: arn:aws:ses:us-east-1:225989354984:identity/bricklayer.ai
  BRICKLAYER_AWS_SHARED_SES_ROLE: arn:aws:iam::225989354984:role/BricklayerSESRole
  DJANGO_SETTINGS_MODULE: bricklayer_api.settings.prod
  INVITATION_EMAIL: <EMAIL>
  PUBLIC_APP_URL: https://app.bricklayer.ai
  PUBLIC_BACKEND_URL: http://backend-service-prod:8000/api/v1
  REDIS_HOST: redis-production.exhwrq.0001.use1.cache.amazonaws.com
  S3_BUCKET: blai-data-production-20250219123752578500000001
  SENTRY_DSN: https://<EMAIL>/4509128039792640
  SENTRY_PROFILES_SAMPLE_RATE: "0.5"
  SENTRY_TRACES_SAMPLE_RATE: "0.8"
