apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: production
  name: bricklayer-worker-deployment-prod
spec:
  replicas: 1
  selector:
    matchLabels:
      app: bricklayer-worker
  template:
    metadata:
      labels:
        app: bricklayer-worker
    spec:
      serviceAccountName: prod-deploy
      containers:
        - name: bricklayer-worker
          image: ************.dkr.ecr.us-east-1.amazonaws.com/bricklayer_api:R1
          imagePullPolicy: Always
          command:
              - bash
              - -c
              - |
                export POSTGRES_DB=$(jq -r '.POSTGRES_DB' /mnt/secrets-store/secrets)
                export POSTGRES_HOST=$(jq -r '.POSTGRES_HOST' /mnt/secrets-store/secrets)
                export POSTGRES_PORT=$(jq -r '.POSTGRES_PORT' /mnt/secrets-store/secrets)
                export POSTGRES_USER=$(jq -r '.username' /mnt/rds-store/rds-secrets)
                export POSTGRES_PASS=$(jq -r '.password' /mnt/rds-store/rds-secrets)
                export DJANGO_SECRET_KEY=$(jq -r '.DJANGO_SECRET_KEY' /mnt/secrets-store/secrets)
                celery -A bricklayer_api worker -l INFO
          envFrom:
            - configMapRef:
                name: bricklayer-worker-configmap-prod
          volumeMounts:
            - name: bricklayer-worker-prod-secrets-store-inline
              mountPath: "/mnt/secrets-store"
              readOnly: true
            - name: rds-prod-secrets-store-inline
              mountPath: "/mnt/rds-store"
              readOnly: true
          # TODO: As we progress, use https://github.com/revsys/django-health-check
          # livenessProbe:
          #   httpGet:
          #     path: /healthcheck
          #     port: 8001
          #   initialDelaySeconds: 30
          #   periodSeconds: 10
          #   timeoutSeconds: 3
          #   failureThreshold: 3
          # readinessProbe:
          #   httpGet:
          #     path: /healthcheck
          #     port: 8001
          #   initialDelaySeconds: 30
          #   periodSeconds: 10
          #   timeoutSeconds: 2
          #   failureThreshold: 5
      volumes:
        - name: bricklayer-worker-prod-secrets-store-inline
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: aws-secrets-bricklayer-worker-prod
        - name: rds-prod-secrets-store-inline
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: "aws-secrets-rds-prod"
      nodeSelector:
        nodes-group: llm-node-group
