apiVersion: batch/v1
kind: CronJob
metadata:
  name: managed-datastores-cronjob
  namespace: development
spec:
  schedule: "*/5 * * * *" # Runs every 5 minutes
  jobTemplate:
    spec:
      ttlSecondsAfterFinished: 300 # Jobs and pods will be deleted 5 minutes after completion
      template:
        spec:
          containers:
          - name: llm-api-caller
            image: curlimages/curl:latest
            args:
            - -s
            - --max-time
            - "300" # Timeout after 5 minutes
            - http://llm-service-dev:9000/integrations/sync/rbc
          restartPolicy: Never
          nodeSelector:
            nodes-group: dev-nodes-group
