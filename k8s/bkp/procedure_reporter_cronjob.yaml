apiVersion: batch/v1
kind: CronJob
metadata:
  name: procedure-reporter
  namespace: development
spec:
  schedule: "20 0 * * *" # run daily at 01:00 (UTC)
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      parallelism: 1
      template:
        spec:
          serviceAccountName: dev-deployment
          containers:
            - name: procedure-reporter
              image: ************.dkr.ecr.us-east-1.amazonaws.com/procedure_reporter:latest
              imagePullPolicy: Always
              env:
                - name: REPORTER_SERVICE
                  value: llm-service-dev:9000
                - name: S3_REPORT_LOCATION
                  value: ba-procedure-runs/reports

          restartPolicy: OnFailure
          nodeSelector:
            nodes-group: dev-nodes-group
