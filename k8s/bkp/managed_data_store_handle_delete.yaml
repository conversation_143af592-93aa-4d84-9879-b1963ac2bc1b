apiVersion: batch/v1
kind: CronJob
metadata:
  name: managed-datastores-handle-delete-cronjob
  namespace: development
spec:
  schedule: "0 * * * *" # Every hour
  jobTemplate:
    spec:
      ttlSecondsAfterFinished: 3600 # Jobs and pods will be deleted 1 hour after completion
      template:
        spec:
          containers:
          - name: llm-api-caller
            image: curlimages/curl:latest
            args:
            - -s
            - --max-time
            - "3600" # 1 hour
            - http://llm-service-dev:9000/integrations/handleDelete/rbc
          restartPolicy: Never
          nodeSelector:
            nodes-group: dev-nodes-group
