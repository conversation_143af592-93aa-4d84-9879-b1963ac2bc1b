apiVersion: apps/v1
kind: Deployment
metadata:
    name: opensearch-dashboards
    namespace: development
spec:
    replicas: 1
    selector:
        matchLabels:
            app: opensearch-dashboards
    template:
        metadata:
            labels:
                app: opensearch-dashboards
        spec:
            serviceAccountName: dev-deploy
            volumes:
              - name: opense-dev-secrets-store-inline
                csi:
                  driver: secrets-store.csi.k8s.io
                  readOnly: true
                  volumeAttributes:
                    secretProviderClass: "aws-secrets-opense-dev"
            containers:
                - name: opensearch-dashboards
                  image: opensearchproject/opensearch-dashboards:2.15.0
                  ports:
                      - containerPort: 5601
                  volumeMounts:
                    - name: opense-dev-secrets-store-inline
                      mountPath: "/mnt/secrets-store"
                      readOnly: true
                  env:
                      - name: OPENSEARCH_HOSTS
                        value: '["https://vpc-blogs-udnwffklnwtbqyg6zqnk4z6waa.us-east-1.es.amazonaws.com:443"]'
                  command:
                      - /bin/sh
                      - -c
                      - |
                          curl -L -o ~/bin/jq https://github.com/stedolan/jq/releases/download/jq-1.6/jq-linux64
                          chmod +x ~/bin/jq
                          export PATH=~/bin:$PATH
                          export OPENSEARCH_USERNAME=$(jq -r '.username' /mnt/secrets-store/opense-secrets)
                          export OPENSEARCH_PASSWORD=$(jq -r '.password' /mnt/secrets-store/opense-secrets)
                          exec /usr/share/opensearch-dashboards/bin/opensearch-dashboards --opensearch.username "$OPENSEARCH_USERNAME" --opensearch.password "$OPENSEARCH_PASSWORD" --opensearch.hosts "$OPENSEARCH_HOSTS"

                  livenessProbe:
                    exec:
                      command:
                        - /bin/sh
                        - -c
                        - |
                            mkdir -p ~/bin
                            curl -L -o ~/bin/jq https://github.com/stedolan/jq/releases/download/jq-1.6/jq-linux64
                            chmod +x ~/bin/jq
                            export PATH=~/bin:$PATH
                            export OPENSEARCH_USERNAME=$(~/bin/jq -r '.username' /mnt/secrets-store/opense-secrets)
                            export OPENSEARCH_PASSWORD=$(~/bin/jq -r '.password' /mnt/secrets-store/opense-secrets)
                            export OPENSEARCH_HOST=$(echo $OPENSEARCH_HOSTS | ~/bin/jq -r '.[0]')
                            curl -k -f -u "$OPENSEARCH_USERNAME:$OPENSEARCH_PASSWORD" "$OPENSEARCH_HOST/_cluster/health"
                    initialDelaySeconds: 60
                    periodSeconds: 10
                    failureThreshold: 3

                  readinessProbe:
                    exec:
                      command:
                        - /bin/sh
                        - -c
                        - |
                            mkdir -p ~/bin
                            curl -L -o ~/bin/jq https://github.com/stedolan/jq/releases/download/jq-1.6/jq-linux64
                            chmod +x ~/bin/jq
                            export PATH=~/bin:$PATH
                            export OPENSEARCH_USERNAME=$(~/bin/jq -r '.username' /mnt/secrets-store/opense-secrets)
                            export OPENSEARCH_PASSWORD=$(~/bin/jq -r '.password' /mnt/secrets-store/opense-secrets)
                            export OPENSEARCH_HOST=$(echo $OPENSEARCH_HOSTS | ~/bin/jq -r '.[0]')
                            curl -k -f -u "$OPENSEARCH_USERNAME:$OPENSEARCH_PASSWORD" "$OPENSEARCH_HOST/_cluster/health"
                    initialDelaySeconds: 60
                    periodSeconds: 10
                    failureThreshold: 3
