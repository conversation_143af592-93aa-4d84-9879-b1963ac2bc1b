apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: development
  name: procedures-api-deployment-dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: procedures-api
  template:
    metadata:
      labels:
        app: procedures-api
    spec:
      serviceAccountName: dev-deploy
      containers:
        - name: procedures-api
          image: ************.dkr.ecr.us-east-1.amazonaws.com/procedures_api:R1
          imagePullPolicy: Always
          ports:
            - containerPort: 9001
          envFrom:
            - configMapRef:
                name: procedures-api-cm-dev
          command:
          - python
          - "-c"
          - 'from llm_api.cli.procedures import procedure_engine; procedure_engine(["api"]);'
          livenessProbe:
            httpGet:
              path: /healthcheck
              port: 9001
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 3
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /healthcheck
              port: 9001
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 2
            failureThreshold: 5
      nodeSelector:
        nodes-group: llm-node-group
---
apiVersion: v1
kind: Service
metadata:
  namespace: development
  name: procedures-api-service-dev
  labels:
    app: procedures-api
spec:
  ports:
    - port: 9001 # Llm service listens on 9000 for internal traffic (coming from backend)
      targetPort: 9001
  selector:
    app: procedures-api
