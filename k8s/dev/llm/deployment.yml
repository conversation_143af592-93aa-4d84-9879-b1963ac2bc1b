apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: development
  name: llm-deployment-dev
spec:
  selector:
    matchLabels:
      app: llm
  template:
    metadata:
      labels:
        app: llm
    spec:
      serviceAccountName: dev-deploy
      containers:
        - name: llm-app
          image: ************.dkr.ecr.us-east-1.amazonaws.com/bricklayerai-llm:develop-9310e25fb8f499fffadcceff6334e08dfa74a07b
          imagePullPolicy: Always
          ports:
            - containerPort: 9000
          command:
            - bash
            - -c
            - |
              export POSTGRES_USER=$(jq -r '.username' /mnt/rds-store/rds-secrets)
              export POSTGRES_PASS=$(jq -r '.password' /mnt/rds-store/rds-secrets)
              uvicorn llm_api.blai_api.main:get_app --factory --host 0.0.0.0 --limit-max-requests $UVICORN_WORKERS_MAX_REQUESTS --log-level $LLM__LOGGING__LEVEL --port $PORT --workers $WORKERS
          envFrom:
            - configMapRef:
                name: llm-configmap-dev
          volumeMounts:
            - name: llm-dev-secrets-store-inline
              mountPath: "/mnt/secrets-store"
              readOnly: true
            - name: rds-dev-secrets-store-inline
              mountPath: "/mnt/rds-store"
              readOnly: true
          livenessProbe:
            httpGet:
              path: /healthcheck
              port: 9000
            initialDelaySeconds: 300
            periodSeconds: 10
            timeoutSeconds: 3
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /healthcheck
              port: 9000
            initialDelaySeconds: 60
            periodSeconds: 10
            timeoutSeconds: 2
            failureThreshold: 100
      volumes:
        - name: llm-dev-secrets-store-inline
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: aws-secrets-llm-dev
        - name: rds-dev-secrets-store-inline
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: "aws-secrets-rds-dev"
      nodeSelector:
        nodes-group: llm-node-group
