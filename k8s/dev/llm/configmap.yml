---
apiVersion: v1
kind: ConfigMap
metadata:
  namespace: development
  name: llm-configmap-dev
data:
  BLAI_ENV: dev
  BLOG_SAMPLES_PATH: /data/blogs
  CORS_ORIGINS: "*"
  CUSTOM_RAG_CHAIN_ENABLED: "True"
  DOCKER_HOST: "tcp://dind-service-dev:2376"
  FEEDLY_ARTICLES_COUNT: "250"
  FEEDLY_MAX_PAGES: "2"
  FEEDLY_NEWER_THAN: "1692086400"
  FEEDLY_UNREAD_ONLY: "True"
  FF_CSV_DATASTORES_ENABLED: "True"
  FF_RAISE_SPECIFIC_CONTENT_FILTERING_ERROR: "1"
  FF_USE_MODIFIED_BLOGS_TOOL_PROMPT: "1"
  FF_USE_NEW_OPENSEARCH_DATERANGE_FILTERING: "1"
  LLM_CHAT_MEMORY: "True"
  LLM__CONVERSATION__BUCKET__URL: s3://ba-conversations-dev
  LLM__LOGGING__FORMAT: "%(message)s"
  LLM__LOGGING__LEVEL: "debug"
  LLM__OPENSEARCH__HOST: vpc-blogs-udnwffklnwtbqyg6zqnk4z6waa.us-east-1.es.amazonaws.com
  LLM__OPENSEARCH__INDEXNAME: "blogs_dev_14_11_2023"
  LLM__OPENSEARCH__PORT: "443"
  LLM__VECTORSTOREANDKEYWORDS__STRATEGY: seq
  PUBLIC_BACKEND_URL: http://backend-service-dev:8000/api/v1
  RATE_LIMIT_BUCKET_SIZE: "1"
  RATE_LIMIT_CHECK_FREQ: "1"
  RATE_LIMIT_RPS: "3"
  REDIS_PORT: "6379"
  REDIS_URL: "redis-development.jj8l5o.0001.use1.cache.amazonaws.com"
  SENTRY_DSN: https://<EMAIL>/4508738019393536
  SENTRY_PROFILES_SAMPLE_RATE: "1.0"
  SENTRY_TRACES_SAMPLE_RATE: "1.0"
  USE_OPENAI_FUNCTION_CALLING: "True"
  USE_REDIS_RATE_LIMITER: "False"
  USE_SEMANTIC_CHUNKING: "True"
  WORKERS: "16" 
  LANGSMITH_TRACING: "false"
  LANGSMITH_ENDPOINT: "https://api.smith.langchain.com"
  LANGSMITH_PROJECT: "llm-api-dev"
  DATASTORE: '{"enable_chunk_traceability":true}'
  DATASTORE_TOOL_RETRIEVER_MODE: "VECTOR"
  ANNOTATE: '{"local_root":"/tmp/annotations/"}'
