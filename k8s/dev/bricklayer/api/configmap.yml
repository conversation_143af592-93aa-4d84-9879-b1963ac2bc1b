---
apiVersion: v1
kind: ConfigMap
metadata:
  namespace: development
  name: bricklayer-api-configmap-dev
data:
  BLAI_ENV: dev
  BRICKLAYER_AWS_SES_IDENTITY_ARN: arn:aws:ses:us-east-1:225989354984:identity/bricklayer.ai
  BRICKLAYER_AWS_SHARED_SES_ROLE: arn:aws:iam::225989354984:role/BricklayerSESRole
  DJANGO_SETTINGS_MODULE: bricklayer_api.settings.dev
  INVITATION_EMAIL: <EMAIL>
  PUBLIC_APP_URL: https://dev1.bricklayer.ai
  PUBLIC_BACKEND_URL: http://backend-service-dev:8000/api/v1
  REDIS_HOST: redis-development.jj8l5o.0001.use1.cache.amazonaws.com
  S3_BUCKET: blai-dev-data-dev
  SENTRY_DSN: https://<EMAIL>/4509128039792640
  SENTRY_PROFILES_SAMPLE_RATE: "1.0"
  SENTRY_TRACES_SAMPLE_RATE: "1.0"
