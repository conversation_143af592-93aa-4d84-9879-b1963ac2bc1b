apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: development
  name: bricklayer-api-deployment-dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: bricklayer-api
  template:
    metadata:
      labels:
        app: bricklayer-api
    spec:
      serviceAccountName: dev-deploy
      containers:
        - name: bricklayer-api
          image: ************.dkr.ecr.us-east-1.amazonaws.com/bricklayer_api:R1
          imagePullPolicy: Always
          ports:
            - containerPort: 8001
          command:
              - bash
              - -c
              - |
                export POSTGRES_DB=$(jq -r '.POSTGRES_DB' /mnt/secrets-store/secrets)
                export POSTGRES_HOST=$(jq -r '.POSTGRES_HOST' /mnt/secrets-store/secrets)
                export POSTGRES_PORT=$(jq -r '.POSTGRES_PORT' /mnt/secrets-store/secrets)
                export POSTGRES_USER=$(jq -r '.username' /mnt/rds-store/rds-secrets)
                export POSTGRES_PASS=$(jq -r '.password' /mnt/rds-store/rds-secrets)
                export DJANGO_SECRET_KEY=$(jq -r '.DJANGO_SECRET_KEY' /mnt/secrets-store/secrets)
                python manage.py migrate_fakes && python manage.py migrate && uvicorn --host 0.0.0.0 --port 8001 bricklayer_api.asgi:application
          envFrom:
            - configMapRef:
                name: bricklayer-api-configmap-dev
          volumeMounts:
            - name: bricklayer-api-dev-secrets-store-inline
              mountPath: "/mnt/secrets-store"
              readOnly: true
            - name: rds-dev-secrets-store-inline
              mountPath: "/mnt/rds-store"
              readOnly: true
          # TODO: As we progress, use https://github.com/revsys/django-health-check
          # livenessProbe:
          #   httpGet:
          #     path: /healthcheck
          #     port: 8001
          #   initialDelaySeconds: 30
          #   periodSeconds: 10
          #   timeoutSeconds: 3
          #   failureThreshold: 3
          # readinessProbe:
          #   httpGet:
          #     path: /healthcheck
          #     port: 8001
          #   initialDelaySeconds: 30
          #   periodSeconds: 10
          #   timeoutSeconds: 2
          #   failureThreshold: 5
      volumes:
        - name: bricklayer-api-dev-secrets-store-inline
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: aws-secrets-bricklayer-api-dev
        - name: rds-dev-secrets-store-inline
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: "aws-secrets-rds-dev"
      nodeSelector:
        nodes-group: llm-node-group
---
apiVersion: v1
kind: Service
metadata:
  namespace: development
  name: bricklayer-api-service-dev
  labels:
    app: bricklayer-api
spec:
  ports:
    - port: 8001
      targetPort: 8001
  selector:
    app: bricklayer-api
