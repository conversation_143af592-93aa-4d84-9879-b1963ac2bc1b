[build-system]
requires = ["setuptools>=48", "setuptools-scm>=4"]
build-backend = "setuptools.build_meta"

[project]
name = "llm-api"
dynamic = ["version"]
requires-python = ">=3.11,<3.12"
dependencies = [
    "boto3==1.35.74",
    "fastapi==0.115.4",
    "uvicorn==0.22.0",
    "redis==5.0.1",
    "click>=8.1.7",
    "python-dotenv>=1.0.0",
    "smart-open>=6.4.0",
    "sentry-sdk==2.28.0",
    "numexpr>=2.10.0",
    "langchain==0.3.12",
    "langchain-community>=0.0.31",
    "langchain-openai>=0.1.1",
    "langchain-core>=0.1.40",
    "ragas==0.2.4",
    "termcolor==2.5.0",
    "psycopg2",
    "playwright==1.52.0",
]
[project.optional-dependencies]
llm_api = [
    "llama-index== 0.12.27",
    "newspaper3k==0.2.8",
    "pypdf>=3.11.1",
    "beautifulsoup4>=4.12.2",
    "tqdm==4.66.1",
    "pandas==1.5.3",
    "structlog>=23.1.0",
    "opensearch-py>=2.3.2",
    "httpx>=0.27.0",
    "thefuzz>=0.20.0",
    "tiktoken>=0.5.2",
    "langchain-unstructured==0.1.5",
    "langchain_experimental==0.3.3",
    "unstructured==0.16.5",
    "unstructured[all-docs]==0.16.5",
    "pdfminer.six<=20240706",
    "pytesseract",
    "protobuf<5.0",
    "fpdf2==2.8.1",
    "python-docx==1.1.2",
    "cairosvg==2.7.1",
    "markdown",
    "langchain_postgres<=0.0.13",
    "sqlalchemy",
    "setuptools>=70.0.0",
    "docker==7.1.0",
    "pymupdf>=1.25.5",
    "aioboto3>=13.3.0",
    "aiofile>=3.9.0",
    "newspaper3k==0.2.8",
    "beautifulsoup4==4.13.3",
    "chardet>=5.2.0,<6",
    "lxml_html_clean",
    "readability-lxml"

]
dev = [
    "moto[all]>=5.1.5",
    "pre-commit>=4.2.0",
    "pytest",
    "pytest-asyncio"
]
[tool.setuptools_scm]
version_file = "llm_api/llm_api/_version.py"

[tool.setuptools.packages.find]
where = ["llm_api"]

[tool.uv]
package = true
cache-keys = [{ file = "pyproject.toml" }, { git = true }]
dev-dependencies = [
    "moto[all]>=5.1.5",
    "pre-commit>=4.2.0",
    "pytest",
    "pytest-asyncio"
]

[dependency-groups]
chunk-traceability = []
llm-api = [
    "pymupdf>=1.26.0",
    "pypdf>=5.5.0",
]
