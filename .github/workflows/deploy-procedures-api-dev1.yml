name: Deploy Procedures API to dev1 cluster
on:
  pull_request_target:
    types:
      - closed
    branches:
      - "develop"
    paths:
      - llm_api/**
  workflow_dispatch:
    inputs:
      logLevel:
        description: "Log level"
        required: true
        default: "warning"
        type: choice
        options:
          - info
          - warning
          - debug

jobs:
  build-and-push-image:
    runs-on: ubuntu-latest

    if: ${{ github.event_name == 'workflow_dispatch' || (github.event_name == 'pull_request_target' && github.event.pull_request.merged == true) }}

    permissions:
      id-token: write
      contents: read
      packages: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_SHARED_GITHUB_ROLE_ARN }}
          role-session-name: github-actions-session
          aws-region: us-east-1

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build, tag, and push docker image to Amazon ECR
        env:
          REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          REPOSITORY: procedures_api
          IMAGE_TAG: develop-${{ github.sha }}
        run: |
          docker build ./llm_api --pull --no-cache -t $REGISTRY/$REPOSITORY:$IMAGE_TAG -f ./llm_api/Dockerfile.procedures_api
          docker push $REGISTRY/$REPOSITORY:$IMAGE_TAG

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_DEV_GITHUB_ROLE_ARN }}
          role-session-name: github-actions-session
          aws-region: us-east-1

      - name: Deploy Procedure API to dev1 cluster
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: procedures_api
          IMAGE_TAG: develop-${{ github.sha }}
        run: |
          aws eks update-kubeconfig --region us-east-1 --name development-dev
          kubectl config get-contexts
          kubectl config use-context arn:aws:eks:us-east-1:140023396818:cluster/development-dev
          kubectl apply -f k8s/dev/procedures/api
          kubectl set image deployment/procedures-api-deployment-dev -n development procedures-api=$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
