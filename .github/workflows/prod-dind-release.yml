name: Release DIND Service

on:
  workflow_dispatch:
    inputs:
      tag:
        description: "release number: ex- R1"
        required: true

jobs:
  production-release:
    runs-on: ubuntu-latest

    permissions:
      id-token: write
      contents: read
      packages: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_SHARED_GITHUB_ROLE_ARN }}
          role-session-name: github-actions-session
          aws-region: us-east-1

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Build, tag, and push docker image to Amazon ECR
        env:
         REGISTRY: ${{ steps.login-ecr.outputs.registry }}
         REPOSITORY: dind
        run: |
          docker build ./llm_api --pull --no-cache -t $REGISTRY/$REPOSITORY:${{ github.event.inputs.tag }} -f ./llm_api/Dockerfile.bricklayer_api
          docker push $REGISTRY/$REPOSITORY:${{ github.event.inputs.tag }}
