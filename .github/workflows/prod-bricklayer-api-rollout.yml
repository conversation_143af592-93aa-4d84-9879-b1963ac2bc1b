name: RollOut Django Bricklayer API Service to production cluster

on:
  workflow_dispatch:
    inputs:
      tag:
        description: "release number: ex- R1"
        required: true
      env:
        type: choice
        options:
          - production-production

jobs:
  production-rollout:
    runs-on: ubuntu-latest

    permissions:
      id-token: write
      contents: read
      packages: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_SHARED_GITHUB_ROLE_ARN }}
          role-session-name: github-actions-session
          aws-region: us-east-1

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_PROD_GITHUB_ROLE_ARN }}
          role-session-name: github-actions-session
          aws-region: us-east-1

      - name: Deploy Django Bricklayer API service to ${{ github.event.inputs.env }} cluster
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: bricklayer_api
        run: |
          aws eks update-kubeconfig --region us-east-1 --name ${{ github.event.inputs.env }}
          kubectl apply -f k8s/prod/bricklayer/api
          kubectl apply -f k8s/prod/bricklayer/worker
          kubectl set image deployment/bricklayer-api-deployment-prod -n production bricklayer-api=$ECR_REGISTRY/$ECR_REPOSITORY:${{ github.event.inputs.tag }}
          kubectl set image deployment/bricklayer-worker-deployment-prod -n production bricklayer-worker=$ECR_REGISTRY/$ECR_REPOSITORY:${{ github.event.inputs.tag }}
