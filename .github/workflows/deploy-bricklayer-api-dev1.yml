name: Deploy Django Bricklayer API & Worker to dev1 cluster
on:
  pull_request_target:
    types:
      - closed
    branches:
      - "develop"
    paths:
      - llm_api/**
      - k8s/dev/bricklayer/**
  workflow_dispatch:
    inputs:
      logLevel:
        description: "Log level"
        required: true
        default: "warning"
        type: choice
        options:
          - info
          - warning
          - debug

jobs:
  build-and-push-image:
    runs-on: ubuntu-latest

    if: ${{ github.event_name == 'workflow_dispatch' || (github.event_name == 'pull_request_target' && github.event.pull_request.merged == true) }}

    permissions:
      id-token: write
      contents: read
      packages: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Setup SonarQube
        uses: warchant/setup-sonar-scanner@v7

      - name: SonarQube Scan
        run: |
            cd llm_api/llm_api/blai_api 
            sonar-scanner \
            -Dsonar.projectKey=${{ secrets.BRICKLAER_API_PROJECT_KEY }} \
            -Dsonar.sources=. \
            -Dsonar.host.url=${{ secrets.SONAR_HOST }} \
            -Dsonar.login=${{ secrets.BRICKLAER_API_SONAR_TOKEN }}

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_SHARED_GITHUB_ROLE_ARN }}
          role-session-name: github-actions-session
          aws-region: us-east-1

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Build, tag, and push docker image to Amazon ECR
        env:
          REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          REPOSITORY: bricklayer_api
          IMAGE_TAG: develop-${{ github.sha }}
        run: |
          docker build ./llm_api --pull --no-cache -t $REGISTRY/$REPOSITORY:$IMAGE_TAG -f ./llm_api/Dockerfile.bricklayer_api
          docker push $REGISTRY/$REPOSITORY:$IMAGE_TAG

      - name: Install Trivy
        run: |
          sudo apt-get update
          sudo apt-get install -y wget
          wget https://github.com/aquasecurity/trivy/releases/download/v0.33.0/trivy_0.33.0_Linux-64bit.deb
          sudo dpkg -i trivy_0.33.0_Linux-64bit.deb

      - name: Run Trivy scan
        continue-on-error: false
        env:
          REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          REPOSITORY: bricklayer_api
          IMAGE_TAG: develop-${{ github.sha }}
        run: |
          trivy image --severity HIGH,CRITICAL --ignore-unfixed --security-checks vuln --timeout 10m ${{env.REGISTRY}}/${{env.REPOSITORY}}:${{env.IMAGE_TAG}}

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ secrets.AWS_DEV_GITHUB_ROLE_ARN }}
          role-session-name: github-actions-session
          aws-region: us-east-1

      - name: Deploy Django Bricklayer API to dev1 cluster
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: bricklayer_api
          IMAGE_TAG: develop-${{ github.sha }}
        run: |
          aws eks update-kubeconfig --region us-east-1 --name development-dev
          kubectl config get-contexts
          kubectl config use-context arn:aws:eks:us-east-1:140023396818:cluster/development-dev
          kubectl apply -f k8s/dev/bricklayer/api
          kubectl apply -f k8s/dev/bricklayer/worker
          kubectl set image deployment/bricklayer-api-deployment-dev -n development bricklayer-api=$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
          kubectl set image deployment/bricklayer-worker-deployment-dev -n development bricklayer-worker=$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
