#!/usr/bin/env python3
"""
Test script to verify that the ApiPluginSpec schema is now compatible with OpenAI's structured output.
"""

import json
import sys
import os

# Add the llm_api path to sys.path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'llm_api'))

try:
    from llm_api.specs.api_plugin_spec import ApiPluginSpec
    
    # Generate the JSON schema
    schema = ApiPluginSpec.model_json_schema()
    
    print("✅ Schema generated successfully!")
    print("\n📋 Schema structure:")
    print(json.dumps(schema, indent=2))
    
    # Check for problematic anyOf without type
    def check_anyof_has_types(obj, path=""):
        if isinstance(obj, dict):
            if "anyOf" in obj:
                for i, item in enumerate(obj["anyOf"]):
                    if isinstance(item, dict) and "type" not in item and "$ref" not in item:
                        print(f"❌ Found anyOf item without 'type' at {path}.anyOf[{i}]: {item}")
                        return False
            for key, value in obj.items():
                if not check_anyof_has_types(value, f"{path}.{key}" if path else key):
                    return False
        elif isinstance(obj, list):
            for i, item in enumerate(obj):
                if not check_anyof_has_types(item, f"{path}[{i}]"):
                    return False
        return True
    
    if check_anyof_has_types(schema):
        print("\n✅ All anyOf constructs have proper 'type' fields!")
        print("🎉 Schema should now be compatible with OpenAI structured output!")
    else:
        print("\n❌ Schema still has issues with anyOf constructs")
        sys.exit(1)
        
except Exception as e:
    print(f"❌ Error generating schema: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
