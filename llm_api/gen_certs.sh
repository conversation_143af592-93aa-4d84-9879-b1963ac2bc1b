#!/bin/bash
set -e

# Clean up any old certificates in the cert folders
rm -f dind-certs/ca.pem dind-certs/server-key.pem dind-certs/server-cert.pem
rm -f docker-certs/ca.pem docker-certs/client-key.pem docker-certs/client-cert.pem

# Ensure the required directories exist
mkdir -p dind-certs docker-certs

# Create a temporary directory for certificate generation
tempdir=$(mktemp -d)

# Create an extension file for the server certificate with Subject Alternative Names
cat > "$tempdir/server-ext.cnf" <<EOF
subjectAltName = DNS:localhost,DNS:dind,DNS:dind-service-dev,DNS:dind-service-prod
EOF

# Create an extension file for the client certificate to include clientAuth usage
cat > "$tempdir/client-ext.cnf" <<EOF
extendedKeyUsage = clientAuth
EOF

echo "Generating CA key and certificate..."
openssl genrsa -out "$tempdir/ca-key.pem" 4096
openssl req -new -x509 -days 3650 -key "$tempdir/ca-key.pem" -subj "/CN=MyDockerCA" -out "$tempdir/ca.pem"

echo "Generating server key and certificate..."
openssl genrsa -out "$tempdir/server-key.pem" 4096
openssl req -new -key "$tempdir/server-key.pem" -subj "/CN=dind" -out "$tempdir/server.csr"
openssl x509 -req -in "$tempdir/server.csr" -CA "$tempdir/ca.pem" -CAkey "$tempdir/ca-key.pem" -CAcreateserial -days 3650 -extfile "$tempdir/server-ext.cnf" -out "$tempdir/server-cert.pem"

echo "Generating client key and certificate..."
openssl genrsa -out "$tempdir/client-key.pem" 4096
openssl req -new -key "$tempdir/client-key.pem" -subj "/CN=client" -out "$tempdir/client.csr"
openssl x509 -req -in "$tempdir/client.csr" -CA "$tempdir/ca.pem" -CAkey "$tempdir/ca-key.pem" -CAcreateserial -days 3650 -extfile "$tempdir/client-ext.cnf" -out "$tempdir/client-cert.pem"

# Copy the CA and server certificates to the dind-certs folder
cp "$tempdir/ca.pem" dind-certs/
cp "$tempdir/server-key.pem" dind-certs/
cp "$tempdir/server-cert.pem" dind-certs/

# Copy the CA and client certificates to the docker-certs folder
cp "$tempdir/ca.pem" docker-certs/
cp "$tempdir/client-key.pem" docker-certs/
cp "$tempdir/client-cert.pem" docker-certs/

# Clean up the temporary directory
rm -rf "$tempdir"

echo "All certificates generated and copied successfully."
