aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin 225989354984.dkr.ecr.us-east-1.amazonaws.com

DOCKER_BUILDKIT=1 docker buildx build --no-cache --pull --platform linux/amd64 -t dind -f Dockerfile.dind --load .

docker tag dind:latest 225989354984.dkr.ecr.us-east-1.amazonaws.com/dind:latest

aws ecr batch-delete-image --repository-name dind --image-ids imageTag=latest --region us-east-1 || true

docker push 225989354984.dkr.ecr.us-east-1.amazonaws.com/dind:latest