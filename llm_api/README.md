# LLM API

## Update [cosmin]: 24-10-2023
All in one docker compose file: `docker-compose.yml`
* starts all the components required to run the llm server (opensearch, redis llm_api)
* the llm_api docker image is rebuilt (given changes in the files) so it should always
  use the latest code (remember to pull the branch first)
* in order to use opensearch, some commands need to be run (once) before starting the docker compose:

```
sudo swapoff -a
```

```
# Edit the sysctl config file
sudo vi /etc/sysctl.conf

# Add a line to define the desired value
# or change the value if the key exists,
# and then save your changes.
vm.max_map_count=262144

# Reload the kernel parameters using sysctl
sudo sysctl -p

# Verify that the change was applied by checking the value
cat /proc/sys/vm/max_map_count
```
Start the system with:
`docker compose up --remove-orphans --build`

## Setup

1. Create virtual env: `python -m venv ~/.venvs/blai`
2. Activate the virtual env: `source ~/.venvs/blai/bin/activate`
3. Install requirements: `pip install -r requirements.txt`
4. If you want to have conversation memory:  
   `docker pull redis`  
   `docker run --name convo-redis -p 6379:6379 -d redis redis-server --save 60 1 --loglevel warning`
5. If you don't need conversation memory: `export LLM_CHAT_MEMORY=False`

### Install pre-commit hooks

Run the following commands from this folder.

```
pip install pre-commit

pre-commit install
```

From now on, the files you stage for committing should:

- Have their imports sorted by `isort` [[ref](https://pycqa.github.io/isort/)]
- Have their code formatted by `black` [[ref](https://black.readthedocs.io/en/stable/)]
- Have their unused imports removed by `autoflake` [[ref](https://github.com/PyCQA/autoflake)]

If these tools will make changes to your files, you'll need to `git add` the changed files again, then `git commit`.

Using these tools we'll have a much more consistent, clean codebase.

## Run the app

`uvicorn blai_api.main:app --port 9000 --reload`

## Run unit tests

`pytest`

## Agents and vector database

1. Create a vector database with the blog post samples:  
   `cd agent_tools/utils`  
   `python init_vectorstore.py`
2. Run the application
3. Ask questions for the agent to respond with the corresponding tool, for example:  
   `What day is today?`  
   `What is the circumference of a circle with a radius of 2 km?`  
   `What are the top 3 latest ransomware threats?`


## Run locally
1. have a .env.docker file
2. run
```
docker compose up --build
```

## Repo description

### k8s/
K8S deployment files for development and production
* llm
  files for llm_api
* procedures
  files for procedure_workers and procedure_api
`ConfigMaps` contain most of the env variables used in the application. They are safe
to be maintained in the repository because they don't contain secrets. `Secret` values
are stored in `AWS Secrets Manager` and mounted as a file at runtime.

### llm_api
Main code of llm_api
* `agent_tools/`
Mostly legacy code.
* `blai_api/`

Contains API definitions and data object definitions for `llm_api`, as well as infra code for Fast API applications.The API consists mainly of *datastore* related calls and *llm* related calls:
   * `message/` for sync calls (normal rest api calls) and `message-streaming/` for streaming calls

* `blai_llm/`
   * `llm.py` 
      * Main entry point for llm calls.
      * Handles hierarchy instantiation through `contruct_component` method.
      * Drives the conversation and creates the final return value
   * `datastore.py`
      * contains the method for datastore management (create / delete) datastores and datastore
        content
   * the rest of the files are miscellaneous stuff (also legacy code) 
* `callbacks/`
   * contains code for callback classes. For now, only logging callback is implemented,
     but in the future, we will have a dynamic notification system where the llm notifies
     the backend with events about the current status of the conversation
* `cli/`
   * entry points for `llm_api`, `procedure_api` and `procedure_workers` applications.
   See `pyproject.toml entrypoints`.
* `coordinators/`
   * implementation for coordinators. Coordinators are mostly `Agents` that are configured to use
   a specific model, have a specific prompt and can have tools associated. Their purpose is to
   encapsulate an interaction with the AI models and (perhaps) provide a mechanism through which
   tools can be selected and called the AI.
   * currently, we only have 1 type of coordinator.
* `datastores/`
   * implementations for tools that interact with datastores:
      * BlogsTool - public datastore. It's used to answer general questions (general knowledge bin)
      * DatastoreTool - private datastore. Datastores containing customer information (uploaded by customers)
* `dummy_procedure/`
   * legacy procedure code
* `llm/`
   * code for instantiating basic llm objects. Factory mechanism to instantiate different llm objects
   based on the given description (see specifications) or default GPT3.5/4 instances.
* `parsers/`
   * fix for multiple json parsing errors found in the past (should be eliminated once a newer versions
   of langchain are used)
* `plugins/`
   * code for `plugin` type tools. Plugins are tools able to call specific API's based on the AI selection,
   using parameters (endpoint, query params, etc) which are also selected through AI interactions
   * we have a generic `ApiPlugin` class that can be used for simple API interactions:
      1. endpoint selection
      2. api call
      3. summarize the response
   * specific APIs with a more complicated flow need separate classes, e.g. TidalTool
* `procedures/`
   * code for procedure running. Procedures are a user defined set of steps (AI calls using agents and tools)
   that are meant to achieve a specific goal: create reports, analyze alerts, etc
   * it's broken down into 2 components:
      * `procedure_api` - the API for procedures - called from the backend. Main job is to enqueue work for workers. Queuing mechanism is based on `celery`
      * `procedure_workers` - instantiate the graph of tasks and run them + notify the backend
      with task status.
* `retrievers/`
   * custom implementation for a opensearch + vectorstore retriever used by the system. It was observed that by
   augmenting the RAG with keyword based searches, the results are much more focused on the initial question
   compared to the default case when only vectorstore search is used
* `services/`
   * custom tools for different use cases that can be used during a conversation
   * currently, we only support report creation. In the future, *send message on slack*, *send emails*, etc
* `specs/`
   * contains specification classes for types that can be used in a conversation
   * all types are defined as `pydantic` BaseModels - serialized to / deserialized from json
   * `Component` is the most generic type (*root* type of all types). *Subtypes* are composed into
   a `Component` in the `spec` field.
   * `ComponentType` enum defines all specific types in the system
      * used in `llm.py:construct_component` to instantiate the specific tool class
* `utils/`
   * utility functions
