FROM docker:dind

RUN apk add --no-cache tini

COPY dind-entrypoint.sh /usr/local/bin/dind-entrypoint.sh

COPY ./dind-certs /dind-certs
COPY ./docker-certs /docker-certs

RUN chmod +x /usr/local/bin/dind-entrypoint.sh

EXPOSE 2376

ENTRYPOINT ["/sbin/tini", "--", "/usr/local/bin/dind-entrypoint.sh"]
CMD ["dockerd", "--host=tcp://0.0.0.0:2376", "--tlsverify", "--tlscacert=/dind-certs/ca.pem", "--tlscert=/dind-certs/server-cert.pem", "--tlskey=/dind-certs/server-key.pem"]
