from pydantic import BaseModel, Field


class TidalSpec(BaseModel):
    name: str = Field(
        default="Tidal Agent",
        description="The name of the Tidal tool",
    )
    description: str = Field(
        default="""
    useful for answering questions using Tidal Cyber. 
    Input must be a fully formed command for Tidal Cyber including any specific information such as technique or tactic or product id's or something else. 
    Input may also include follow-up questions for the response. 
    ACTION INPUT MUST be in the format below:
    'Fully Formed Command for Tidal Cyber --- Follow Up Question (IF ANY)'
    All responses should be considered sourced from [source: "Tidal Cyber"]
        """,
        description="The description of the Tidal Agent tool",
    )
