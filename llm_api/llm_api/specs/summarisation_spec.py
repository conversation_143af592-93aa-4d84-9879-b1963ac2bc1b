from pydantic import BaseModel, <PERSON>

from llm_api.specs.llm_spec import LLMSpec, LLMType
from llm_api.llm.factory import default_3_5_gpt_spec_data


class SummarizationSpec(BaseModel):
    name: str = Field(description="The name of the tool")

    description: str = Field(
        description="The description of the tool. Will be used by the LLM during selection",
    )

    prompt: str = Field(description="The prompt used for summarization")

    chunk_count: int = Field(
        description="How many chunks to summarize",
        default=2,
    )

    chunk_size: int = Field(
        description="Size of the chunks (in characters)",
        default=12000,
    )

    llm: LLMSpec = Field(
        description="The specification for the LLM used to summarize",
        default=LLMSpec(
            type=LLMType.AzureChatOpenAI,
            data=default_3_5_gpt_spec_data,
        ),
    )
