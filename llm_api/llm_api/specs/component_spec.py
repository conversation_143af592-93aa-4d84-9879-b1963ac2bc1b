from enum import Enum
from typing import Any, List, Optional

from pydantic import BaseModel, Field


class ComponentType(str, Enum):
    Coordinator = "coordinator"
    ApiPlugin = "plugin"
    BlogsDatastore = "publicDataSource"
    PrivateDatastore = "datastore"
    Tidal = "tidal"
    PatchTuesday = "patchTuesday"
    CSVTool = "csvTool"
    Reporter = "reporter"
    Procedure = "procedure"
    LongReporter = "longReporter"


class ComponentSpec(BaseModel):
    class Config:
        use_enum_values = True

    id: str = Field(
        description="The ID of the component",
    )

    type: ComponentType = Field(
        description="The type of the component",
    )

    spec: Any = Field(
        description="The technical specifications of the component",
    )

    children: Optional[List["ComponentSpec"]] = Field(
        description="Children of the current component; usually, tools used by the agent",
        default=None,
    )
