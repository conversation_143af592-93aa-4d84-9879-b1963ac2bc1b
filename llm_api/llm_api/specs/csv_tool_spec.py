from typing import Optional

from pydantic import BaseModel, Field

from llm_api.llm.factory import default_4_gpt_spec_data
from llm_api.specs.llm_spec import LLMSpec, LLMType


class CSVToolSpec(BaseModel):
    name: str = Field(
        default="CSVTool",
        description="The name of the Microsoft Patch Tuesday tool",
    )
    description: str = Field(
        default="""
    useful for processing CSV files stores in S3. 
    These file are annotated in the conversation with the following format:
    [S3File key=<key>] 
    The tool input should be the users query, the separator ( --- ) and the key of the file, for example:
    How many false positives were detected? --- org-files/pzpesrx7o5h6xz873m7gv3hs/test.csv
    """,
        description="The description of the Microsoft Patch Tuesday tool",
    )

    llm: Optional[LLMSpec] = Field(
        description="The specification of the LLM used by the retriever chain.",
        default=LLMSpec(
            type=LLMType.AzureChatOpenAI,
            data=default_4_gpt_spec_data,
        ),
    )

    organization_id: str = Field(
        description="The organization ID of the user.",
        default="",
    )
