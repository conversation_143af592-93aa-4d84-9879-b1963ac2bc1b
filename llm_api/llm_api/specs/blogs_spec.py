from enum import Enum
from typing import List, Optional

from pydantic import BaseModel, Field, validator

from llm_api.llm.factory import default_3_5_gpt_spec_data
from llm_api.specs.llm_spec import LLMSpec, LLMType


class RetrievalChainType(Enum):
    Stuff = "stuff"
    MapReduce = "map_reduce"
    Refine = "refine"
    MapRerank = "map_rerank"


class BlogsSpec(BaseModel):
    name: str = Field(
        description="The display name of the blogs tool",
        default="Blogs Tool",
    )

    description: Optional[str] = Field(
        description=(
            "The description of the tool. "
            "This description acts as a prompt for the "
            "LLM call and will finally determine what the tool input will be."
        ),
        default="",
    )

    llm: Optional[LLMSpec] = Field(
        description="The specification of the LLM used by the retriever chain.",
        default=LLMSpec(
            type=LLMType.AzureChatOpenAI,
            data=default_3_5_gpt_spec_data,
        ),
    )

    chain_type: Optional[RetrievalChainType] = Field(
        description="The retriever chain type. Possible options: "
        "'stuff', 'map_reduce', 'refine', 'map_rerank'",
        default=RetrievalChainType.Stuff,
    )

    reduce_k_below_max_tokens: Optional[bool] = Field(
        description="Reduce the number of results to return from store based on tokens limit",
        default=True,
    )

    max_tokens_limit: Optional[int] = Field(
        description="Restrict the docs to return from store based on tokens, "
        "enforced only for StuffDocumentChain and if reduce_k_below_max_tokens is to true",
        default=2000,
    )

    disabled_public_source_feed_ids: Optional[List[int]] = Field(
        description="Disables public data source",
        default=[],
    )

    secondary_index_id: Optional[str] = Field(
        description="Secondary index id for the blogs tool",
        default=None,
    )

    organization_id: Optional[str] = Field(
        description="Organization id for the secondary index of blogs tool",
        default=None,
    )

    disabled_feed_ids_for_secondary_index: Optional[List[int]] = Field(
        description="Disables feed ids for secondary index",
        default=[],
    )

    class Config:
        title = "Blogs Tool"
