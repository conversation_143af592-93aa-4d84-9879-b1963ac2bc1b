from enum import Enum
from typing import Any, Dict, List, Literal, Optional, Union

from pydantic import BaseModel, Field, SecretStr

from llm_api.llm.factory import default_3_5_gpt_spec_data
from llm_api.specs.llm_spec import LLMSpec, LLMType


class ApiCredentialType(str, Enum):
    ApiKey = "api_key"
    BasicAuth = "basic_auth"


class BasicAuthData(BaseModel):
    username: SecretStr
    password: SecretStr


class ApiKeyData(BaseModel):
    key: SecretStr = Field(description="The key")
    header: str = Field(description="The header")


class ApiCredentialsSpec(BaseModel):
    type: ApiCredentialType = Field(
        description="Type of credentials: api key / basic auth",
        default=ApiCredentialType.ApiKey,
    )
    data: Optional[ApiKeyData | BasicAuthData | str | Any] = Field(
        description="The data for this credentials object. If it is a string, the data is encrypted.",
        default=None,
    )


class EndpointArgumentSpec(BaseModel):
    """
    Specification for an argument found in a endpoint
    """

    examples: List[str]


class EndpointSpec(BaseModel):
    """
    Specification for a specific endpoit
    """

    path: str
    definition: str
    arguments: Dict[str, EndpointArgumentSpec]


class UrlSpec(BaseModel):
    """
    Specification for a base url of the API
    """

    # Base url of the route
    # e.g. www.some-security-client.com
    base_url: str

    # HTTP method, usually GET or POST
    method: str

    # extra header to add to the call
    extra_headers: Optional[Dict[str, Any]] = None

    # List of API endpoints
    # e.g. /domain/[value]/analyze
    endpoints: Optional[List[EndpointSpec]] = None

    # Prompt used by the LLM
    # to select the appropriate endpoint
    select_endpoint_prompt: str = Field(
        description="Prompt used by the LLM to select the appropriate endpoint",
    )

    # LLM used to select the endpoints
    select_endpoint_llm: Optional[LLMSpec] = Field(
        description="Specification of the LLM used for endpoint selection",
        default=LLMSpec(
            type=LLMType.AzureChatOpenAI,
            data=default_3_5_gpt_spec_data,
        ),
    )

    # Pattern that confirms that the selected endpoint
    # is valid
    select_endpoint_validation_pattern: str = Field(
        description="Pattern that confirms that the selected endpoint is valid",
    )

    # Prompt used by the LLM to interpret responses
    interpret_response_prompt: str = Field(
        description="Prompt used to interpret the API response."
    )

    # LLM used to interpret the responses
    interpret_response_llm: Optional[LLMSpec] = Field(
        description="Specification of the LLM used to interpret the response.",
        default=LLMSpec(
            type=LLMType.AzureChatOpenAI,
            data=default_3_5_gpt_spec_data,
        ),
    )


class SummarizationSpec(BaseModel):
    prompt: str = Field(
        default="""
        I want you to act as a cybersecurity analyst that summarises {file_details} 
        and returns a detailed list of information including all links for further information.
        Do not include explanations of specific terms but rather focus on the inferences drawn from it.
        The json string is the information on vulnerabilities. Key items to look for in the JSON include 

        - Descriptions in English,
        - CVSS V3 Base Scores and Severity, 
        - Weaknesses and CWE-ID.

        Try answering questions such as:

        - Does the Indicator seems malicious? 
        - What are associated threat actors are? 
        - What devices are typically affected and what is their description?

        MAKE SURE that your answer is in valid MARKDOWN.
        """,
        description="The prompt used for summarization",
    )

    chunk_count: int = Field(
        description="How many chunks to summarize",
        default=2,
    )

    chunk_size: int = Field(
        description="Size of the chunks (in characters)",
        default=12000,
    )

    llm: LLMSpec = Field(
        description="The specification for the LLM used to summarize",
        default=LLMSpec(
            type=LLMType.AzureChatOpenAI,
            data=default_3_5_gpt_spec_data,
        ),
    )


class Parameter(BaseModel):
    name: str
    type: Literal["str", "int", "bool", "float", "list", "dict", "file", "object"] = (
        "str"
    )
    required: bool = False
    location: Literal["query", "header", "body", "path", "json"] = "query"
    default: Optional[Union[str, int, bool, float, list, dict]] = None
    sub_parameters: Optional[list["Parameter"]] = Field(default_factory=list)
    prefix: Optional[str] = None
    suffix: Optional[str] = None
    definition: Optional[str] = None
    fieldsList: Optional[list[str]] = None


class Endpoint(BaseModel):
    path: str
    method: Literal["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS"] = "GET"
    parameters: list[Parameter] = Field(default_factory=list)
    headers: Optional[dict[str, str]] = Field(default_factory=dict)
    auth_required: bool = False
    auth_location: Literal["header", "query"] = "header"
    auth_header_name: Optional[str] = None
    description: Optional[str] = None
    content_type: Literal[
        "application/json", "application/x-www-form-urlencoded", "multipart/form-data",
        "application/vnd.github.v3.raw"
    ] = "application/json"
    user_agent: Optional[str] = None
    basic_auth_header_name: Optional[str] = None
    api_id_name: Optional[str] = None
    api_secret_name: Optional[str] = None


class ParameterLocation(str, Enum):
    BODY = "body"
    QUERY = "query"
    PATH = "path"
    HEADER = "header"


class RequestFormat(str, Enum):
    JSON = "json"
    FORM_URLENCODED = "x-www-form-urlencoded"


class TokenMethod(str, Enum):
    POST = "POST"
    GET = "GET"
    PUT = "PUT"
    DELETE = "DELETE"


class GrantType(str, Enum):
    CLIENT_CREDENTIALS = "client_credentials"
    REFRESH_TOKEN = "refresh_token"
    JWT_BEARER = "urn:ietf:params:oauth:grant-type:jwt-bearer"
    AUTHORIZATION_CODE = "authorization_code"
    PKCE = "proof_key_for_code_exchange"


class OAuthParameter(BaseModel):
    value: str
    required: bool
    location: ParameterLocation


class OAuthSpec(BaseModel):
    token_url: str
    request_format: RequestFormat
    token_method: TokenMethod
    parameters: Dict[str, OAuthParameter]
    headers: Optional[Dict[str, str]] = None
    token_response_key: str
    grant_type: Optional[GrantType] = None
    scope: Optional[str] = None

    def get_grant_type(self) -> GrantType:
        """Returns the explicitly set grant type, or falls back to the parameters dictionary."""
        if self.grant_type:
            return self.grant_type

        grant_type = self.parameters.get("grant_type")
        if grant_type:
            return GrantType(grant_type.value)

        raise ValueError("OAuth grant type must be explicitly defined.")

    def get_scope(self) -> Optional[str]:
        """Returns the explicitly set scope, or falls back to the parameters dictionary."""
        if self.scope:
            return self.scope

        scope_param = self.parameters.get("scope")
        if scope_param:
            return scope_param.value

        return None


class PluginV2(BaseModel):
    base_url: Optional[str] = Field(
        description="The base URL of the API",
        default=None,
    )

    endpoints: Optional[list[Endpoint]] = Field(
        default_factory=list,
        description="List of endpoints",
    )

    oauth_spec: Optional[OAuthSpec] = Field(
        description="OAuth specification", default=None
    )


class ApiPluginSpec(BaseModel):
    # Common properties

    name: str = Field(
        description="The name of the tool. Will be used by the LLM during selection"
    )
    description: str = Field(
        description="The description of the tool. Will be used by the LLM during selection"
    )

    # Credentials used to authenticate
    # to the url
    credentials: Optional[ApiCredentialsSpec] = None

    # V1 properties

    # List of urls
    # For now, we only handle lists with 1 url
    urls: Optional[List[UrlSpec]] = Field(
        description="List of urls found in the API",
        default=None,
    )

    summarization: Optional[SummarizationSpec] = Field(
        default=SummarizationSpec(),
        description="Properties used by the summarization feature.",
    )

    # V2 properties
    spec: Optional[PluginV2] = Field(
        description="Specification for the plugin",
        default=None,
    )

    auth_type: Optional[str] = Field(
        description="Type of authentication",
        default=None,
    )

    class Config:
        title = "Api Plugin"
