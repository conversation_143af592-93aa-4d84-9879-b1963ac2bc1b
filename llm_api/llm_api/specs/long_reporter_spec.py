import enum
from typing import Dict, List, Optional


from pydantic import BaseModel, Field

from llm_api.specs.llm_spec import LLMSpec, LLMType
from llm_api.llm.factory import default_4_gpt_spec_data


class Section(BaseModel):
    name: str = Field(
        description="The name of the section",
        alias="section_name",
    )
    description: str = Field(
        description="The description of the template",
        alias="section_description",
    )


class LongReporterSpec(BaseModel):

    name: str = Field(
        description="The name of the tool.",
        default="Long Reporter Tool",
    )

    description: str = Field(
        description="The description of the tool.",
        default=(
            "Used to create reports based on the provided user input. "
            "The entire user input must be used when creating the report. "
            "Include all the information from the context from which the report should be generated."
        ),
    )

    sections: List[Section] = Field(
        description="The list of sections the user wants included in the answer."
    )

    report_title: str = Field(
        description="The name of the report",
        default="Title of report",
        alias="title",
    )

    llm: Optional[LLMSpec] = Field(
        description="The specification of the LLM used by the retriever chain.",
        default=LLMSpec(
            type=LLMType.AzureChatOpenAI,
            data=default_4_gpt_spec_data,
        ),
    )
