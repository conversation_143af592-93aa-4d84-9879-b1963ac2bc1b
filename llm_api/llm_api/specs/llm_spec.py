from enum import Enum

from pydantic import BaseModel, Field, SecretStr


class LLMType(str, Enum):
    """
    Types of LLMs supported in the system.
    For now, just AzureOpenAI and AzureChatOpenAI
    """

    AzureOpenAI = "azure"
    AzureChatOpenAI = "azure_chat"


class AzureOpenAISpec(BaseModel):
    temperature: float
    deployment_name: str
    openai_api_version: str
    openai_api_type: str
    openai_api_base: str
    openai_api_key: SecretStr
    model_name: str
    model_version: str
    json_mode_enabled: bool = False

    class Config:
        json_encoders = {
            SecretStr: lambda v: v.get_secret_value() if v else None,
        }
        protected_namespaces = ()


class LLMSpec(BaseModel):
    """
    Specification for a LLM.
    Currently, we only support AzureOpenAI
    """

    type: LLMType = Field(
        description="The type of the LLM. Can be normal or chatty",
    )
    data: AzureOpenAISpec = Field(
        description="The parameters used to instantiate the LLM object",
    )
    streaming: bool = Field(
        description="Do we want this LLM to stream it's response?",
        default=False,
    )
