import enum
from typing import Dict, List, Optional

from pydantic import BaseModel, Field

from llm_api.llm.factory import default_4_gpt_spec_data
from llm_api.specs.llm_spec import LLMSpec, LLMType


class ReportType(enum.Enum):
    Short = "short"
    Long = "long"


class ReporterSpec(BaseModel):

    name: str = Field(
        description="The name of the tool.",
        default="Reporter Tool",
    )

    description: str = Field(
        description="The description of the tool.",
        default=(
            "Used to create reports based on the provided user input. "
            "The entire user input must be used when creating the report. "
            "Never give a reference to the context, always write the full content of the context."
            "Include all the information from the context from which the report should be generated."
        ),
    )

    report_type: ReportType = Field(
        description="Type of report the user wants: 'short' / 'long'. Short translates to a single LLM call for all sections, while long means individual LLM calls for each section."
    )

    sections: List[Dict[str, str]] = Field(
        description="The list of sections the user wants included in the answer."
    )

    short_prompt: str = Field(
        description="The prompt used in the LLM call to obtain the section text for 'short' reports.",
        default=(
            "Generate a security report in the following TEMPLATE FORMAT using "
            "the given information about the user input. You MUST follow the TEMPLATE "
            "and fill its sections according to their section description. "
            "DO NOT INCLUDE ANY ADDITIONAL DETAILS EXCEPT THE REPORT ITSELF. "
            "DO NOT INCLUDE TEMPLATE FORMAT START AND END.\n\n"
            "************TEMPLATE FORMAT START***********\n"
            "{sections}\n\n"
            "**********TEMPLATE FORMAT ENDS************"
        ),
    )

    section_template: str = Field(
        description="The string template to use for each section.",
        default=("----- {section_name} -----\n\n" "{section_description}\n\n"),
    )

    section_name_pattern: str = Field(
        description="The pattern used to identify section names in the response.",
        default=r"----- ([a-zA-Z\& \-]+) -----\n\n",
    )

    section_text_pattern: str = Field(
        description="The pattern used to identify section text in the response.",
        default=r"----- [a-zA-Z\& \-]+ -----\n\n",
    )

    long_prompt: str = Field(
        description="The prompt used in the LLM call to obtain the section text for 'short' reports.",
        default="",
    )

    llm: Optional[LLMSpec] = Field(
        description="The specification of the LLM used by the retriever chain.",
        default=LLMSpec(
            type=LLMType.AzureChatOpenAI,
            data=default_4_gpt_spec_data,
        ),
    )
