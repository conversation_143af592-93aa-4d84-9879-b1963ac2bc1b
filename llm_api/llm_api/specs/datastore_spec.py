from enum import Enum
from typing import List, Optional

from pydantic import BaseModel, Field

from llm_api.llm.factory import default_3_5_gpt_spec_data
from llm_api.specs.llm_spec import LLMSpec, LLMType


class DatastoreType(str, Enum):
    Pdf = "pdf"
    Csv = "csv"


class DatastoreSpec(BaseModel):
    display_name: str = Field(
        description="The customer given name of the datastore",
    )

    display_description: str = Field(
        description="The customer given description of the datastore",
    )

    organization_id: str = Field(
        description="The ID of the user's organization.",
    )

    datastore_id: str = Field(
        description="The ID of the datastore.",
    )

    tool_description: str = Field(
        description="The general description used for the tool - will contain the user provided description.",
        default="""Retrieves information from files with the following description: {description}.
When available use the tool since the user probably uploaded the information need to answer the question.
The tool Input should be a STRING that contains a fully formed question with all the necessary context.

EXAMPLES:
    - What are the news about Kubernetes?
    - How do you initialize a React project?""",
    )

    type: DatastoreType = Field(
        description="Type of datastore. Currently, only 'pdf' and 'csv' supported.",
        default=DatastoreType.Pdf,
    )

    llm: LLMSpec = Field(
        description="The llm spec used for data retrieval",
        default=LLMSpec(
            type=LLMType.AzureChatOpenAI,
            data=default_3_5_gpt_spec_data,
        ),
    )

    is_memory_datastore: bool = Field(
        description="True if the datastore is a memory datastore.",
        default=False,
    )

    procedure_id: Optional[int] = Field(
        description="The ID of the procedure associated with the datastore.",
        default=None,
    )

    procedure_name: Optional[str] = Field(
        description="The name of the procedure associated with the datastore.",
        default=None,
    )


class ColumnDescription(BaseModel):
    column_name: str = Field(description="The name of the column in the CSV.")
    description: str = Field(description="A description of what the column represents.")


class CSVDatastoreSpec(BaseModel):
    display_name: str = Field(
        description="The customer given name of the datastore",
    )

    display_description: str = Field(
        description="The customer given description of the datastore",
    )

    organization_id: str = Field(
        description="The ID of the user's organization.",
    )

    datastore_id: str = Field(
        description="The ID of the datastore.",
    )

    data_description: Optional[List[ColumnDescription]] = Field(
        description="A description of the columns of the CSV data contained in this datastore.",
        default=[],
    )

    tool_description: str = Field(
        description="The general description used for the tool - will contain the user provided description.",
        default="""Can answer questions about CSV data with the following description: {description}.
CSV Structure (column names + optional description): 
{data_description}

The tool Input should be a STRING with the users query ONLY.
""",
    )

    type: DatastoreType = Field(
        description="Type of datastore. For CSV datastores use 'csv'.",
        default=DatastoreType.Csv,
    )

    s3_key: str = Field(description="The S3 key of the CSV")

    llm: LLMSpec = Field(
        description="The llm spec used for data retrieval",
        default=LLMSpec(
            type=LLMType.AzureChatOpenAI,
            data=default_3_5_gpt_spec_data,
        ),
    )
