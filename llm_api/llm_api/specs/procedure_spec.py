import os
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Extra, Field

from llm_api.specs.api_plugin_spec import ApiCredentialsSpec


class ProcedureInput(BaseModel):
    name: str = Field(
        description="The name of the input variable",
        alias="Name",
    )
    description: str = Field(
        description="The description of this variable",
        alias="Description",
    )


class ProcedureToolSpec(BaseModel):
    class Config:
        extra = Extra.allow

    procedure_name: str = Field(
        description="The name of the procedure",
        alias="procedureName",
        default="",
    )
    procedure_overview: str = Field(
        description="The description of the procedure - Procedure Overview",
        alias="procedureOverview",
    )
    procedure_id: int = Field(
        description="The ID of the procedure ran by this tool instance.",
        alias="procedureId",
    )
    inputs: List[ProcedureInput] = Field(
        description="The list of <name, description> for each input in the procedure",
    )

    input_match_regex: str = Field(
        description="The regex used to identify procedure inputs from the GPT tool input",
        # default=r"\((?P<name>.*?)\)",
        default=r"\[\[\[(?P<name>.*?)\]\]\]",
    )


env = os.environ["BLAI_ENV"]


class CdiProcedureSpec(BaseModel):
    name: str = Field(
        default="CDI Procedure",
        description="The name of the CDI Procedure tool",
    )
    description: str = Field(
        default="""
Useful for analyzing json alerts using the CDI procedure. 
Input must be the string to be analyzed.
        """,
        description="The description of the CDI Procedure tool",
    )

    credentials: Optional[ApiCredentialsSpec] = Field(
        description="The credentials to use during the procedure call.",
        default=None,
    )

    # extra header to add to the call
    extra_headers: Optional[Dict[str, Any]] = Field(
        default={
            "Content-Type": "application/json",
        },
        description="Extra header to the mock procedure api call",
    )

    procedure_endpoint: str = Field(
        default=f"https://{'dev' if env == 'dev' else 'app'}.bricklayer.ai/api/v1/mock/cdi_procedure",
        description="The mock procedure endpoint.",
    )
