from pydantic import BaseModel, <PERSON>
from llm_api.specs.llm_spec import LLMSpec, LLMType
from llm_api.llm.factory import default_4_gpt_spec_data
from typing import Optional

class MicrosoftPatchTuesdaySpec(BaseModel):
  name: str = <PERSON>(
    default="Microsoft Patch Tuesday Tool",
    description="The name of the Microsoft Patch Tuesday tool",
  )
  description: str = Field(
    default="""
    useful for retrieving information about the Microsoft Patch Tuesday updates. 
    Input must be a date formatted as 'YYYY-MMM' (e.g., '2024-Aug').
    The tool will return a summary of vulnerabilities, their severities, and critical information about publicly disclosed and exploited vulnerabilities for that month.
    """,
    description="The description of the Microsoft Patch Tuesday tool",
  )

  llm: Optional[LLMSpec] = Field(
    description="The specification of the LLM used by the retriever chain.",
    default=LLMSpec(
        type=LLMType.AzureChatOpenAI,
        data=default_4_gpt_spec_data,
    ),
  )