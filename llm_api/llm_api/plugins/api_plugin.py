import json
import logging
import os
import re
from typing import Any, Coroutine, Dict, Generator, List, Optional, Tuple
from uuid import UUID

import tiktoken
from httpx import AsyncClient
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.tools import BaseTool
from pydantic import Extra, root_validator

from llm_api.blai_api.dtos import Evidence
from llm_api.blai_llm.utils import PlanLogger, sanitize_tool_name
from llm_api.callbacks import LoggingCallbackHandler
from llm_api.http_client import get_session
from llm_api.llm.factory import get_model_from_spec
from llm_api.specs.api_plugin_spec import (
    ApiCredentialsSpec,
    ApiCredentialType,
    ApiKeyData,
    ApiPluginSpec,
    BasicAuthData,
    EndpointSpec,
    UrlSpec,
)
from llm_api.utils import store_evidence

logger = logging.getLogger(__name__)


class ApiPlugin(BaseTool):
    spec: ApiPluginSpec
    plan_id: UUID

    logging_cb: LoggingCallbackHandler

    store_json_location: str = ""

    @root_validator(pre=True)
    def validate_basic(cls, values: Dict) -> Dict:
        if not values.get("name"):
            values["name"] = sanitize_tool_name(values["spec"].name)
        if not values.get("description"):
            values["description"] = values["spec"].description

        return values

    class Config:
        extra = Extra.allow

    def _run(self, question: str):
        return NotImplementedError(
            f"You are calling a sync method on {self.__class__}. Illegal!"
        )

    async def _arun(self, question: str) -> Coroutine[Any, Any, Any]:
        log = PlanLogger()
        log_text = f"Used '{self.name}' with the input '{question}'"
        log.addToLog(log_text, self.plan_id)
        response, source, evidence = await self.call_api(question)

        resp = response

        if source:
            resp = {"question": question, "answer": response, "source_api": [source]}

            if evidence:
                resp["evidence"] = [evidence]

        return resp

    async def call_api(self, question: str) -> Tuple[str, str, Evidence | None]:
        # Here, we presume for now that we only have 1 url
        # For APIs with multiple urls, some other logic
        # needs to be implemented
        base_url_spec = self.spec.urls[0]
        selected_endpoint = await self.select_endpoint(
            question=question, url=base_url_spec
        )
        if not selected_endpoint:
            logger.error(
                f"{self.logging_cb.message_id} Error composing API endpoint: input: '{question}'"
            )
            return "Error composing API.", "", None
        composed_url = base_url_spec.base_url + selected_endpoint

        async with self.prepare_client(
            credentials=self.spec.credentials,
            extra_headers=base_url_spec.extra_headers,
        ) as client:
            match base_url_spec.method:
                case "GET":
                    call_func = client.get
                case "POST":
                    call_func = client.post

            logger.info(
                f"{self.logging_cb.message_id} {base_url_spec.method} {composed_url}"
            )
            response = await call_func(composed_url)

        if response.status_code == 401:
            error_msg = "Invalid Or Missing API key. Please check your credentials and try again."
            logger.warning(f"{self.logging_cb.message_id} {error_msg}")
            return error_msg, "", None

        if response.status_code == 200:
            logger.info(f"API response len: {len(response.text)}")

            evidence = store_evidence(
                evidence_text=response.text,
                evidence_name=self.spec.name,
                evidence_location=self.store_json_location,
            )

            return (
                await self.interpret_response(
                    url=base_url_spec,
                    response_text=response.text,
                    selected_endpoint=selected_endpoint,
                ),
                composed_url,
                evidence if evidence else None,
            )

        logger.error(f"{composed_url} - {response.status_code}")
        return (
            f"Error retrieving threat intelligence data. Status Code: {response.status_code}",
            "",
            None,
        )

    def prepare_client(
        self,
        credentials: Optional[ApiCredentialsSpec],
        extra_headers: Optional[Dict[str, Any]],
    ) -> AsyncClient:
        client_params = {}

        # set a 20s timeout for API calls
        client_params["timeout"] = 60

        if credentials:
            # handle the encrypted case
            if isinstance(credentials.data, str):
                decrypt_url = (
                    f"{os.environ['PUBLIC_BACKEND_URL']}/internal/api/llm/decrypt"
                )
                decrypt_url = decrypt_url.replace("/api/v1", "")
                decrypt_payload = {
                    "data": credentials.data,
                }
                logger.info(
                    self.logging_cb.format_msg(
                        f"decrypting: {decrypt_url}",
                        "prepare_client",
                    )
                )
                session = get_session()
                data_resp = session.post(
                    url=decrypt_url,
                    json=decrypt_payload,
                    timeout=int(
                        os.environ.get("PROCEDURES__WORKERS__REQUEST__TIMEOUT", "10")
                    ),
                )
                logger.info(
                    self.logging_cb.format_msg(
                        f"received after decryption: {data_resp.content}",
                        "prepare_client",
                    )
                )
                match credentials.type:
                    case ApiCredentialType.ApiKey:
                        data = ApiKeyData.parse_raw(data_resp.json()["decryptedData"])
                    case ApiCredentialType.BasicAuth:
                        data = BasicAuthData.parse_raw(
                            data_resp.json()["decryptedData"]
                        )
            else:
                # credentials not encrypted
                data = credentials.data

            match credentials.type:
                case ApiCredentialType.ApiKey:
                    client_params["headers"] = {
                        data.header: data.key.get_secret_value()
                    }
                case ApiCredentialType.BasicAuth:
                    client_params["auth"] = (
                        data.username.get_secret_value(),
                        data.password.get_secret_value(),
                    )

            if extra_headers:
                client_params["headers"].update(extra_headers)

        client = AsyncClient(**client_params)

        return client

    async def select_endpoint(self, question: str, url: UrlSpec) -> str:
        system_rules = self.prepare_system_rules_for_endpoint_selection(
            prompt=url.select_endpoint_prompt, endpoints=url.endpoints
        )

        chat_model = get_model_from_spec(url.select_endpoint_llm)

        messages = [SystemMessage(content=system_rules), HumanMessage(content=question)]

        res = await chat_model.ainvoke(messages)
        logger.info(f"endpoint_selection: {res}")

        endpoint = re.search(url.select_endpoint_validation_pattern, res.content)
        if endpoint:
            return endpoint[0]
        return ""

    def prepare_system_rules_for_endpoint_selection(
        self,
        prompt: str,
        endpoints: List[EndpointSpec],
    ):
        examples = {}

        if "{examples}" in prompt:
            for e in endpoints:
                for arg in e.arguments.keys():
                    examples[arg] = e.arguments[arg].examples

        options_str = ""
        if "{options}" in prompt:
            for index, e in enumerate(endpoints):
                eNo = chr(65 + index)
                eName = e.path
                eDef = e.definition
                options_str += f'\n{eNo}) "{eName}"- {eDef}'

        return prompt.format(examples=json.dumps(examples), options=options_str)

    async def interpret_response(
        self,
        url: UrlSpec,
        response_text: str,
        selected_endpoint: str,
    ):
        # Estimate number of tokens using tiktoken
        llm = get_model_from_spec(url.interpret_response_llm)
        encoding = tiktoken.get_encoding("cl100k_base")

        try:
            json_resp = json.loads(response_text)
            response_text = json.dumps(json_resp, separators=(",", ":"))
        except:
            pass
        logger.info(f"API Response length (no spaces): {len(response_text)}")

        num_tokens = len(encoding.encode(response_text))
        if num_tokens < 7000:
            system_rules = url.interpret_response_prompt.format(
                endpoint=selected_endpoint
            )

            resp = await llm.ainvoke(
                [
                    SystemMessage(content=system_rules),
                    HumanMessage(content=response_text),
                ]
            )

            logger.debug(f"interpret_response: {resp}")
            return resp.content
        else:
            system_rules = url.interpret_response_prompt.format(
                endpoint=selected_endpoint
            )

            resp = await llm.ainvoke(
                [
                    SystemMessage(content=system_rules),
                    HumanMessage(content=response_text[:50000]),
                ]
            )

            logger.debug(f"(!!) summarization response: {resp}")
            return resp.content

            # logger.info(f"Response size: {len(response_text)}. Should use summarization")
            # # tmpfile = tempfile.NamedTemporaryFile(mode="w", delete=False)
            # # tmpfile.write(response_text)
            # # return f"The response from the API was too long so I have dumped it into the location: {tmpfile.name} Please read the file and summarise the file."
            # max_iter = self.spec.summarization.chunk_count
            # chunk_size = self.spec.summarization.chunk_size
            # count = 0
            # tasks = []

            # for chunk in ApiPlugin.chunk_text(response_text, chunk_size):
            #     task = asyncio.create_task(
            #         self.summarize_chunk(chunk, "an input json string")
            #     )
            #     tasks.append(task)
            #     count += 1
            #     if count >= max_iter:
            #         break

            # await asyncio.gather(*tasks)

            # if len(tasks) == 1:
            #     return tasks[0]

            # return await self.summarize_chunk(
            #     str(tasks), "a list of summaries into a SINGLE SUMMARY"
            # )

    async def summarize_chunk(self, text_chunk: str, file_details: str) -> str:
        system_rules = self.spec.summarization.prompt.format(file_details=file_details)
        llm = get_model_from_spec(self.spec.summarization.llm)
        resp = await llm.ainvoke(
            [SystemMessage(content=system_rules), HumanMessage(content=text_chunk)]
        )
        return resp.content

    @staticmethod
    def chunk_text(response_text: str, chunk_size: int) -> Generator[str, None, None]:
        idx = 0
        while idx <= len(response_text):
            chunk = response_text[idx : idx + chunk_size]
            if not chunk:
                break
            yield chunk
            idx += chunk_size
