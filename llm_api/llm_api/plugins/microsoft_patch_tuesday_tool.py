import json
import os
import xml.etree.ElementTree as ET
from datetime import datetime, timedelta
from statistics import mode
from uuid import UUID

from langchain_core.tools import BaseTool

from llm_api.blai_llm.utils import PlanLogger
from llm_api.callbacks import <PERSON>ggingCallbackHandler
from llm_api.http_client import get_session
from llm_api.specs.microsoft_patch_tuesday_spec import MicrosoftPatchTuesdaySpec
from llm_api.utils import store_evidence


class MicrosoftPatchTuesdayTool(BaseTool):
    name: str = "microsoft_patch_tuesday_tool"
    description: str = """
    useful for answering questions using Microsoft Patch Tuesday updates.
    Input must be a fully formed date string formatted as 'YYYY-MMM' (e.g., '2024-Aug').
    The tool will return a summary and any available evidence related to the vulnerabilities, their severities, and critical information.
    """

    spec: MicrosoftPatchTuesdaySpec
    plan_id: UUID
    logging_cb: LoggingCallbackHandler
    store_json_location: str = ""

    def _run(self, question: str):
        raise NotImplementedError(
            "You are calling a sync method on MicrosoftPatchTuesdayTool. Please use the async method _arun."
        )

    async def _arun(self, question: str):
        log = PlanLogger()
        log_text = f"Used '{self.name}' with the input '{question}'"
        log.addToLog(log_text, self.plan_id)

        try:
            xml_data = self.call_cvrf_api(question)
            result, json_data = self.process_cvrf_response(xml_data, question)

            source_url = f"https://api.msrc.microsoft.com/cvrf/v3.0/cvrf/{question}"

            all_evidence = []

            if len(self.store_json_location) > 0:
                if evidence := store_evidence(
                    evidence_text=json.dumps(json_data, indent=2),
                    evidence_name=source_url,
                    evidence_location=self.store_json_location,
                ):
                    all_evidence.append(evidence)
            log_text = f"'{self.name}' is done"
            log.addToLog(log_text, self.plan_id)
            return {
                "question": question,
                "answer": result,
                "source_documents": [],
                "source_api": [source_url],
                "evidence": all_evidence,
            }

        except Exception as e:
            log_text = f"'{self.name}' is done"
            log.addToLog(log_text, self.plan_id)
            return {
                "question": question,
                "answer": f"An error occurred: {str(e)}",
                "source_documents": [],
                "source_api": [],
                "evidence": [],
            }

    def call_cvrf_api(self, date):
        session = get_session()
        response = session.get(
            f"https://api.msrc.microsoft.com/cvrf/v3.0/cvrf/{date}",
            timeout=int(os.environ.get("PROCEDURES__WORKERS__REQUEST__TIMEOUT", "10")),
        )
        response.raise_for_status()
        return response.text

    def process_cvrf_response(self, xml_string, date):
        tue = self.second_tuesday(date).strftime("%Y-%m-%d")
        v_list, product_id_map = self.parse_cvrf_from_string(xml_string)
        v_list = self.enrich_parsed_text(v_list, product_id_map)
        filtered_v_list = [
            v
            for v in v_list
            if v["Title"] and (f"{tue}T07:00:00" in v["RevisionDates"])
        ]
        imp_v = self.find_imp_vulnerabilities(filtered_v_list)

        impactCount = {}
        severityCount = {}
        disclosedAndExploited = 0
        publiclyDisclosed = 0
        exploited = 0

        for v in filtered_v_list:
            if v["CommonImpact"]:
                impactCount[v["CommonImpact"]] = (
                    impactCount.get(v["CommonImpact"], 0) + 1
                )
            if v["Severity"]:
                severityCount[v["Severity"]] = severityCount.get(v["Severity"], 0) + 1
            if v.get("Publicly Disclosed") == "Yes":
                publiclyDisclosed += 1
                if v.get("Exploited") == "Yes":
                    disclosedAndExploited += 1
                    exploited += 1
            elif v.get("Exploited") == "Yes":
                exploited += 1

        impacts = sorted(impactCount.items(), key=lambda x: x[1], reverse=True)[:3]
        severities = sorted(severityCount.items(), key=lambda x: x[1], reverse=True)
        summary = (
            f"There are a total of {len(filtered_v_list)} vulnerabilities addressed in the Microsoft Security Update from {tue}. "
            f"Their major impacts can be broken down by vulnerability count as {impacts}. "
            f"Their Severities can be broken down by vulnerability count as {severities}. "
            f"There are {disclosedAndExploited} vulnerabilities that are publicly disclosed and exploited. "
            f"Out of all of these, here is the information on the vulnerabilities that were either deemed critical or have been exploited in the past:\n\n {imp_v}"
        )

        json_data = {
            "summary": summary,
            "impacted_vulnerabilities": filtered_v_list,
            "top_impacts": impacts,
            "top_severities": severities,
            "publicly_disclosed_exploited_count": disclosedAndExploited,
        }

        return summary, json_data

    def second_tuesday(self, date_str):
        date = datetime.strptime(date_str, "%Y-%b")
        first_day = date.replace(day=1)
        first_tuesday = first_day + timedelta(days=(1 - first_day.weekday() + 7) % 7)
        second_tuesday = first_tuesday + timedelta(days=7)
        return second_tuesday

    def parse_cvrf_from_string(self, xml_string):
        namespaces = {
            "ns0": "http://www.icasi.org/CVRF/schema/cvrf/1.1",
            "ns1": "http://www.icasi.org/CVRF/schema/prod/1.1",
            "ns2": "http://www.icasi.org/CVRF/schema/vuln/1.1",
        }
        root = ET.fromstring(xml_string)

        vulnerabilities = root.findall(".//ns2:Vulnerability", namespaces)
        v_list = []
        for vulnerability in vulnerabilities:
            v = {}
            ns = "{http://www.icasi.org/CVRF/schema/vuln/1.1}"
            for i in list(vulnerability):
                if i.tag == f"{ns}Title":
                    v["Title"] = i.text
                elif i.tag == f"{ns}CVE":
                    v["CVE"] = i.text
                elif i.tag == f"{ns}Notes":
                    v["Notes"] = "".join(i.itertext())
                elif i.tag == f"{ns}CWE":
                    v["CWE"] = i.text
                    v["CWE_ID"] = i.attrib["ID"]
                elif i.tag == f"{ns}ProductStatuses":
                    for j in list(i):
                        if j.tag == f"{ns}Status":
                            v["Status"] = {j.attrib["Type"]: [k.text for k in list(j)]}
                elif i.tag == f"{ns}Threats":
                    for j in list(i):
                        if j.tag == f"{ns}Threat":
                            if "Threat" in v:
                                if j.attrib["Type"] in v["Threat"]:
                                    v["Threat"][j.attrib["Type"]].append(
                                        {
                                            "Description": [
                                                k.text
                                                for k in list(j)
                                                if k.tag == f"{ns}Description"
                                            ][0],
                                            "Products": [
                                                k.text
                                                for k in list(j)
                                                if k.tag == f"{ns}ProductID"
                                            ][0],
                                        }
                                    )
                                else:
                                    children = {k.tag: k.text for k in list(j)}
                                    v["Threat"][j.attrib["Type"]] = [children]
                            else:
                                v["Threat"] = {
                                    j.attrib["Type"]: [
                                        {
                                            "Description": [
                                                k.text
                                                for k in list(j)
                                                if k.tag == f"{ns}Description"
                                            ][0],
                                            "Products": [
                                                k.text
                                                for k in list(j)
                                                if k.tag == f"{ns}ProductID"
                                            ][0],
                                        }
                                    ]
                                }
                elif i.tag == f"{ns}CVSSScoreSets":
                    v["CVSS"] = [
                        {"Score": k.text, "Products": j.find(f"{ns}ProductID").text}
                        for j in list(i)
                        if j.tag == f"{ns}ScoreSet"
                        for k in list(j)
                        if k.tag == f"{ns}BaseScore"
                    ]
                elif i.tag == f"{ns}Remediations":
                    children = list()
                    for j in list(i):
                        if j.tag == f"{ns}Remediation":
                            child = dict()
                            child["Type"] = j.attrib["Type"]
                            for k in list(j):
                                if k.tag == f"{ns}URL":
                                    child["URL"] = k.text
                                elif k.tag == f"{ns}RestartRequired":
                                    child["RestartRequired"] = k.text
                            children.append(child)
                    v["Remediations"] = children
                elif i.tag == f"{ns}RevisionHistory":
                    v["RevisionDates"] = [
                        k.text
                        for j in list(i)
                        if j.tag == f"{ns}Revision"
                        for k in list(j)
                        if k.tag == f"{{{namespaces['ns0']}}}Date"
                    ]
            v_list.append(v)

        products = root.findall(".//ns1:FullProductName", namespaces)
        product_id_map = {p.attrib["ProductID"]: p.text for p in products}
        return v_list, product_id_map

    def enrich_parsed_text(self, v_list, product_id_map):
        for v in v_list:
            prod_info_list = {}
            v["CVSSList"] = []
            for k in v.keys():
                if k == "Status":
                    for id in v[k].get("Known Affected", []):
                        prod_info_list[id] = {"Name": product_id_map.get(id, "")}
                if k == "Threat":
                    for type, threats in v[k].items():
                        for e in threats:
                            if "Products" in e:
                                prod_info_list[e["Products"]].update(
                                    {type: e["Description"]}
                                )
                if k == "CVSS":
                    for ele in v[k]:
                        prod_info_list[ele["Products"]].update({"CVSS": ele["Score"]})
                        v["CVSSList"].append(float(ele["Score"]))
            v["Affected Products"] = prod_info_list
            v["CVSS"] = max(v["CVSSList"], default=None)
            exploitationStatus = [
                {i.split(":")[0]: i.split(":")[1]}
                for i in v["Threat"]["Exploit Status"][0][
                    "{http://www.icasi.org/CVRF/schema/vuln/1.1}Description"
                ].split(";")
            ]
            for i in exploitationStatus:
                key = list(i.keys())[0]
                v[key] = i[key]

            v["Severities"] = []
            for i in v["Affected Products"].keys():
                if "Severity" in v["Affected Products"][i].keys():
                    v["Severities"].append(v["Affected Products"][i]["Severity"])

            if len(v["Severities"]):
                v["Severity"] = mode(v["Severities"])
            else:
                v["Severity"] = None

            impacts = [
                v["Affected Products"][i].get("Impact")
                for i in v["Affected Products"].keys()
                if "Impact" in v["Affected Products"][i]
            ]

            if len(impacts):
                v["CommonImpact"] = mode(impacts)
            else:
                v["CommonImpact"] = None

            v.pop("Severities", None)
            v.pop("CVSSList", None)
            v.pop("Status", None)
            v.pop("Threat", None)
            v.pop("Affected Products")
            v["Remediations"] = v["Remediations"][:3]
        return v_list

    def find_imp_vulnerabilities(self, v_list):
        return [
            v
            for v in v_list
            if v.get("Severity") == "Critical"
            or v.get("Exploited") == "Yes"
            or v.get("Publicly Disclosed") == "Yes"
        ]
