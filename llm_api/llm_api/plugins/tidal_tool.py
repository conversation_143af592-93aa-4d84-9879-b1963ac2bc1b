import json
import logging
import os
from typing import Optional, Type
from uuid import UUID

import regex as re
from langchain.agents.agent import AgentExecutor
from langchain.chains.conversation.memory import ConversationBufferWindowMemory
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field
from thefuzz import fuzz

from llm_api.agent_tools.utils.endpoints import tidal_id_selection_endpoints
from llm_api.agent_tools.utils.prompts import PROMPT_TIDAL
from llm_api.blai_llm.blai_agents import BlaiConversationalChatAgent
from llm_api.blai_llm.openai import azure_openai_chat_llm, azure_openai_llm
from llm_api.blai_llm.utils import PlanLogger
from llm_api.callbacks import Logging<PERSON>allbackHandler
from llm_api.http_client import get_session
from llm_api.specs.tidal_spec import TidalSpec
from llm_api.utils import store_evidence

logger = logging.getLogger(__name__)


async def api_composer(question, endpoints, pattern):
    options = {}
    for e in endpoints:
        for arg in e["arguments"].keys():
            if arg not in options:
                options[arg] = e["arguments"][arg]["examples"]
            else:
                options[arg].extend(arg["examples"])
                options[arg] = list(set(options[arg]))
    system_rules = """
    You are an api composing assistant that composes an API URL by following the steps below:

    Step 1) Analyze the input and identify the relevant entity. 
    Here are a list of options and examples- 
    """
    system_rules += json.dumps(options)
    system_rules += """
    Step 2) Pick the correct endpoint from the list of options below based on the intent of the user and the description of the end-point. Options:
    """
    for index, e in enumerate(endpoints):
        eNo = chr(65 + index)
        eName = e["endpoint"]
        eDef = e["definition"]
        system_rules += f'\n{eNo}) "{eName}"- {eDef}'
    # A) "/api/v1/indicators/IPv4/[value]/general"- This endpoint should retrieve information related to the IPv4 indicator specified by the IP address.
    # B) "/api/v1/indicators/IPv6/[value]/general"- This endpoint should retrieve information related to the IPv6 indicator specified by the IP address.
    # C) "/api/v1/indicators/domain/[value]/general"- This endpoint should retrieve information related to the domain indicator specified by the domain name.
    # D) "/api/v1/indicators/hostname/[value]/general"- This endpoint should retrieve information related to the hostname indicator specified by the hostname.
    # E) "/api/v1/indicators/file/[value]/general"- This endpoint should retrieve information related to the file indicator specified by the file hash.
    # F) "/api/v1/indicators/url/[value]/general"- This endpoint should retrieve information related to the URL indicator specified by the URL.
    # G) "/api/v1/indicators/cve/[value]/general"- This endpoint should retrieve information related to the Common Vulnerabilities and Exposures (CVE) indicator specified by the CVE identifier.
    system_rules += """
    \nStep 3) Compose the API by replacing [value] in the correct end-point with the actual value of the entity identified.

    Here are some examples:

    Example 1)
    [Input] Tell me about common tactics used by cyberattackers? 
    [Output] /api/v1/tactic/

    Example 2) 
    [Input] What is the technique ID for the technique Abuse Elevation Control Mechanism?
    [Output] /api/v1/technique/
    
    Generate an [Output] for the submitted [Input] using the steps mentioned. MAKE SURE to include only the ENDPOINT in the [Output]
    """
    res = await azure_openai_llm.ainvoke(
        [system_rules + "\n\n[Input] " + question + "\n[Output] "]
    )
    endpoint = re.search(pattern, res.content)
    if endpoint:
        logger.info(f"{res}, {endpoint[0]}")
        return endpoint[0]
    logger.info(res)
    return None


async def interpret_json(response_text, endpoint, question, followup):
    # Estimate number of tokens, on average 1 token is 4 characters. Check if number of tokens is less than 7000
    system_rules = f"""
        I want you to act as a cybersecurity analyst that summarises an input json string 
        recieved from an endpoint {endpoint} of the Tidal Cyber Trident API 
        and returns a detailed list of information while answering specific questions.
        Do not include explanations of the JSON but rather focus on the inferences drawn from it. 
        The answer MUST be as detailed as possible.
        DO NOT LIE. 

        QUESTION: {followup}

        Context information will be provided now.
        """
    if len(response_text) / 4 < 3500:
        # import pdb; pdb.set_trace()
        azure_chat = await azure_openai_chat_llm.ainvoke(
            [SystemMessage(content=system_rules), HumanMessage(content=response_text)]
        )
        return azure_chat.content
    # else:
    #     tmpfile = tempfile.NamedTemporaryFile(mode="w", delete=False)
    #     tmpfile.write(response_text)
    #     return f"The response from the API was too long so I have dumped it into the location: {tmpfile.name} Please read the file and summarise the file."

    else:
        res = json.loads(response_text)
        if "data" in res.keys():
            data = res["data"]
        else:
            data = res

        parsed_data = long_json_parser(data)
        text = compose_parsed_data(parsed_data)
        if len(text) / 4 < 3500:
            azure_chat = await azure_openai_chat_llm.ainvoke(
                [
                    SystemMessage(content=system_rules),
                    HumanMessage(content="CONTEXT: " + text),
                ]
            )
            return azure_chat.content
        else:
            azure_chat = await azure_openai_chat_llm.ainvoke(
                [
                    SystemMessage(content=system_rules),
                    HumanMessage(content=text[:13998]),
                ]
            )
            return azure_chat.content
            # tmpfile = tempfile.NamedTemporaryFile(mode="w", delete=False)
            # tmpfile.write(response_text)
            # return f"The response from the API was too long so I have dumped it into the location: {tmpfile.name} Please read the file and summarise the file."


def long_json_parser(data):
    if type(data) == type(list()):
        length = len(data)
        examples = []
        for i in data:
            if type(i) == type(dict()):
                if "name" in [j.lower() for j in i.keys()]:
                    examples.append({"name": i["name"]})
                    if "id" in [j.lower() for j in i.keys()]:
                        examples[-1]["id"] = i["id"]
            elif type(i) == type(str()):
                examples.append(i)
            else:
                pass
        comment = (
            f"This is a list of {length} with the following information - {examples}"
        )
        return [["", comment]]
    else:
        no_list_data = [i for i in data.items() if type(i[1]) != type(list())]
        list_data = [i for i in data.items() if type(i[1]) == type(list())]
        list_summaries = []
        for lists in list_data:
            list_summary = long_json_parser(lists[1])
            list_summary[0][0] = lists[0]
            list_summaries.extend(list_summary)
        no_list_data.extend(list_summaries)
        return no_list_data


def compose_parsed_data(parsed_data):
    if type(parsed_data) == type(list()):
        composition = "{\n"
        for i in parsed_data:
            line = f'"{i[0]}" : "{i[1]}",\n'
            composition += line
        composition += "}"
        return composition


def get_id(question, response_text):
    res = json.loads(response_text)
    if "data" in res.keys():
        data = res["data"]
    else:
        data = res
    q = question.split(":")
    if len(q) > 1:
        q = q[1]
    names = []
    ids = []
    max = 0
    match_index = -1
    for i in range(len(data)):
        match = fuzz.ratio(data[i]["name"], q)
        if match > max:
            max = match
            match_index = i
        names.append((data[i]["name"], match))
        ids.append(data[i]["id"])

    # Threshold for fuzzy search in case the id doesn't match any entry
    if max > 80:
        id = ids[match_index]
        print("Fetching details about the entity: ", names[match_index])
        return id
    else:
        print(f"Couldn't find {data[i]['name']} in list")
        return ""


async def request_tidal_id_selection(question: str, store_json_location: str):
    l = question.split("---")
    followup = ""
    if len(l) > 1:
        question = l[0]
        followup = "\n-".join(l[1:])
    pattern = "\/api\/v1\/([a-zA-Z0-9_\-]*)+\/"
    endpoint = await api_composer(question, tidal_id_selection_endpoints, pattern)
    if not endpoint:
        return "Error composing API.", None, []
    endpoint_url = "https://app-api.tidalcyber.com" + endpoint
    logger.info(f"Tidal Cyber url: {endpoint_url}")
    session = get_session()
    response = session.get(
        endpoint_url,
        timeout=int(os.environ.get("PROCEDURES__WORKERS__REQUEST__TIMEOUT", "10")),
    )
    all_evidence = []
    if response.status_code == 200:

        if evidence := store_evidence(
            evidence_text=response.text,
            evidence_name=endpoint_url,
            evidence_location=store_json_location,
        ):
            all_evidence.append(evidence)

        id = get_id(question, response.text)
        if not id:
            return (
                f"No information on this threat actor found in TidalCyber.",
                None,
                [],
            )
        final_url = endpoint_url + f"{id}"
        logger.info(f"Tidal Cyber url: {final_url}")
        response = session.get(
            final_url,
            timeout=int(os.environ.get("PROCEDURES__WORKERS__REQUEST__TIMEOUT", "10")),
        )
        if response.status_code == 200:

            if evidence := store_evidence(
                evidence_text=response.text,
                evidence_name=endpoint_url,
                evidence_location=store_json_location,
            ):
                all_evidence.append(evidence)

            return (
                await interpret_json(response.text, endpoint, question, followup),
                [endpoint_url, final_url],
                all_evidence,
            )
        else:
            return (
                f"Error retrieving threat intelligence data. Status Code: {response.status_code}",
                None,
                [],
            )
    else:
        return (
            f"Error retrieving threat intelligence data. Status Code: {response.status_code}",
            None,
            [],
        )


class TidalIDSelectorToolInput(BaseModel):
    question: str = Field(description="The question to ask the Tidal Cyber API")


class TidalIDSelectorTool(BaseTool):
    name: str = "Tidal ID Selection Tool"
    description: str = """useful for finding details of Tidal Cyber Entities such as Techniques, Tactics, Products, Threat Groups, Associated Groups and Softwares using their NAMES. 
    Input must be the full name of the entity (technique or tactic or product id's or something else) along with the type of the entity. 
    Input may also include follow-up questions for the response. 
    ACTION INPUT MUST be in the format below:
    'Entity_Type:Entity_Name --- Follow Up Question (IF ANY)'

    EXAMPLES

    - 'technique:Abuse Elevation Control Mechanism'
    - 'tactic:Reconnaissance'
    - 'groups:APT1 --- what are its associated techniques?'
    """
    plan_id: UUID
    store_json_location: str = ""
    args_schema: Type[BaseModel] = TidalIDSelectorToolInput

    def _run(self, question: str):
        raise NotImplementedError(
            f"You are calling a sync method on {self.__class__}. That's not what you want to do! Probably..."
        )

    async def _arun(self, question: str):
        log = PlanLogger()
        log_text = f"Used '{self.name}' with the input '{question}'"
        log.addToLog(log_text, self.plan_id)
        response, source, all_evidence = await request_tidal_id_selection(
            question=question,
            store_json_location=self.store_json_location,
        )
        if source:
            return {
                "question": question,
                "answer": response,
                "source_api": source,
                "evidence": all_evidence,
            }
        else:
            return response


async def tidal(
    question: str,
    plan_id: UUID,
    logging_cb: LoggingCallbackHandler,
    store_json_location: str,
):
    tools = [
        TidalIDSelectorTool(
            plan_id=plan_id,
            store_json_location=store_json_location,
        )
    ]

    conversational_memory = ConversationBufferWindowMemory(
        memory_key="chat_history", k=0, return_messages=True
    )
    conversational_memory.output_key = "output"
    conversational_memory.input_key = "input"

    agent_obj = BlaiConversationalChatAgent.from_llm_and_tools(
        tools=tools, llm=azure_openai_chat_llm
    )
    agent = AgentExecutor.from_agent_and_tools(
        agent=agent_obj,
        tools=tools,
        max_iterations=5,
        early_stopping_method="generate",
        memory=conversational_memory,
        return_intermediate_steps=True,
        return_source_documents=True,
        handle_parsing_errors=True,
    )

    tools_prompt = agent.agent.create_prompt(system_message=PROMPT_TIDAL, tools=tools)
    agent.agent.llm_chain.prompt = tools_prompt

    # run the agent and get the execution result
    output = await agent.acall(
        {"input": question, "chat_history": []},
        callbacks=[logging_cb],
    )

    intermediate_steps = output["intermediate_steps"]
    all_sources = []
    all_apis = []
    all_evidence = []

    if intermediate_steps:
        try:
            for interm_step in intermediate_steps:
                if "source_documents" in interm_step[1].keys():
                    source_docs = interm_step[1]["source_documents"]
                    all_sources.extend(source_docs)
                if "source_api" in interm_step[1].keys():
                    source_api = interm_step[1]["source_api"]
                    all_apis.extend(source_api)
                if "evidence" in interm_step[1].keys():
                    evidence = interm_step[1]["evidence"]
                    all_evidence.extend(evidence)
        except Exception as error:
            pass

    reply = {"question": question, "answer": output["output"]}
    reply["source_documents"] = all_sources
    reply["source_api"] = all_apis
    reply["evidence"] = all_evidence
    return reply


class TidalCyberTool(BaseTool):
    name: str = "Tidal Agent"
    description: str = """useful for answering questions using Tidal Cyber. 
    Input must be a fully formed command for Tidal Cyber including any specific information such as technique or tactic or product id's or something else. 
    Input may also include follow-up questions for the response. 
    ACTION INPUT MUST be in the format below:
    'Fully Formed Command for Tidal Cyber --- Follow Up Question (IF ANY)'
    All responses should be considered sourced from [source: "Tidal Cyber"]"""
    conversation_id: Optional[str] = None
    spec: TidalSpec
    plan_id: UUID
    logging_cb: LoggingCallbackHandler
    store_json_location: str = ""

    def _run(self, question: str):
        return NotImplementedError(
            f"You are calling a sync method on {self.__class__}. That's not what you want to do! Probably..."
        )

    async def _arun(self, question: str):
        log = PlanLogger()
        log_text = f"Used '{self.name}' with the input '{question}'"
        log.addToLog(log_text, self.plan_id)

        res = await tidal(
            question=question,
            plan_id=self.plan_id,
            logging_cb=self.logging_cb,
            store_json_location=self.store_json_location,
        )

        log_text = f"'{self.name}' is done"
        log.addToLog(log_text, self.plan_id)
        return res
