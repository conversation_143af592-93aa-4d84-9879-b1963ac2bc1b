import json
import logging
import os
from typing import Any, Coroutine, Dict, Tu<PERSON>
from uuid import UUID

from langchain_core.tools import BaseTool
from pydantic import Extra, root_validator

from llm_api.blai_api.dtos import Evidence, EvidenceOrigin
from llm_api.blai_llm.utils import PlanLogger, sanitize_tool_name
from llm_api.callbacks import LoggingCallbackHandler
from llm_api.http_client import get_session
from llm_api.specs.api_plugin_spec import ApiPluginSpec
from llm_api.utils import store_evidence

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

PLUGINS_API_BASE = os.environ.get(
    "PLUGINS_API_BASE", "http://host.docker.internal:5001"
)


def decrypt_credentials(encrypted_data: dict) -> None:
    decrypt_url = (
        f"{os.environ['PUBLIC_BACKEND_URL']}/internal/api/llm/decrypt".replace(
            "/api/v1", ""
        )
    )
    decrypt_payload = encrypted_data
    logger.info(f"decrypting: {decrypt_url}")
    session = get_session()
    data_resp = session.post(
        url=decrypt_url,
        json=decrypt_payload,
        timeout=900,
    )
    logger.info(f"received after decryption: {data_resp.content}")

    return json.loads(data_resp.json()["decryptedData"])


class ApiPluginV2(BaseTool):
    spec: ApiPluginSpec
    plan_id: UUID

    logging_cb: LoggingCallbackHandler

    store_json_location: str = ""

    args_schema: dict = {
        "type": "object",
        "properties": {
            "question": {
                "type": "string",
                "description": "The question/query to be sent to the plugin API",
            }
        },
        "required": ["question"],
    }

    @root_validator(pre=True)
    def validate_basic(cls, values: Dict) -> Dict:
        if not values.get("name"):
            values["name"] = sanitize_tool_name(values["spec"].name)
        if not values.get("description"):
            values["description"] = values["spec"].description
        return values

    class Config:
        extra = Extra.allow

    def _run(self, question: str):
        return NotImplementedError(
            f"You are calling a sync method on {self.__class__}. Illegal!"
        )

    async def _arun(self, *args, **kwargs) -> Coroutine[Any, Any, Any]:
        question = ""
        if "question" in kwargs:
            question = kwargs["question"]
        else:
            for arg in args:
                question += f"{arg}; "
            for key, value in kwargs.items():
                question += f"; {key}={value}"

        logger.debug(f"PluginV2 Question: {question}")

        log = PlanLogger()
        log_text = f"Used '{self.name}' with the input '{question}'"
        log.addToLog(log_text, self.plan_id)

        response, evidence = await self.call_api(question)

        resp = {"question": question, "answer": response}

        if evidence:
            resp["evidence"] = evidence

        return resp

    async def call_api(self, question: str) -> Tuple[str, Evidence | None]:
        logger.debug(f"PLUGINS_API_BASE: {PLUGINS_API_BASE}")

        payload = {"spec": self.spec.model_dump(), "question": question}
        credentials = {"data": {}}

        if isinstance(payload.get("spec", {}).get("credentials", {}).get("data"), str):
            credentials = decrypt_credentials(payload["spec"]["credentials"])

        payload["spec"]["credentials"] = credentials
        payload["spec"]["summarization"] = {}

        logger.debug(f"Payload to Plugins API: {json.dumps(payload, indent=2)}")

        session = get_session()
        response = session.post(
            f"{PLUGINS_API_BASE}/process_question",
            json=payload,
            timeout=900,  # 15 minutes
        )

        response.raise_for_status()

        logger.debug(f"Plugins API response object: {response}")

        if response.status_code == 200:
            logger.info(f"API response len: {len(response.text)}")

            response_data = response.json()

            evidences_array = []
            for _, source in enumerate(
                response_data.get("answer", []).get("sources", [])
            ):
                source_evidence = {"answer": {"sources": [source]}}
                source_evidence_text = json.dumps(source_evidence, indent=2)
                if isinstance(response_data["answer"], str):
                    source_evidence_text = source_evidence
                evidence = store_evidence(
                    evidence_text=source_evidence_text,
                    evidence_name=f"{self.spec.name}",
                    evidence_location=self.store_json_location,
                    evidence_type=EvidenceOrigin.Plugin,
                )
                evidences_array.append(evidence)

            return response_data["answer"], evidences_array if evidences_array else None

        logger.error(f"{PLUGINS_API_BASE}/process_question - {response.status_code}")
        return (
            f"Error retrieving threat intelligence data. Status Code: {response.status_code}",
            None,
        )
