import functools
import json
import logging
import os
import traceback
from typing import Any, Literal, Union

import smart_open
from celery.result import AsyncResult

from llm_api.blai_api.dtos import Evidence, MessageResponse
from llm_api.exceptions import ContentFilteringError, HTTPProblem
from llm_api.http_client import get_session
from llm_api.procedures.celery.tasks.workflow import (
    generate_procedure_run_task_celery_key,
)
from llm_api.procedures.notifications import ProcedureStatus
from llm_api.procedures.notifications import (
    notify_procedure_status as _notify_procedure_status,
)
from llm_api.procedures.runtime.ui_reporter import reporter_component
from llm_api.procedures.runtime.utils import make_llm_call, merge_context
from llm_api.procedures.types import ProcedureResult, ProcedureRun

logger = logging.getLogger(__name__)


def run_procedure_task_results_processing(
    base_path: str,
    procedure_run: ProcedureRun,
    task_results: list[dict[Union[Literal["answer"], Literal["evidence"]], Any]] = [],
):
    from llm_api.procedures.celery import celery_app

    notify_procedure_status = functools.partial(
        _notify_procedure_status,
        procedure_spec=procedure_run,
        session=get_session(with_retries=True),
    )

    try:
        task_results = []
        for task in procedure_run.tasks:
            task_results.append(
                AsyncResult(
                    generate_procedure_run_task_celery_key(procedure_run, task),
                    app=celery_app,
                ).get(disable_sync_subtasks=False)
            )

        final_prompt = merge_context(
            prompt="Please create a report from the following context",
            context=[task_result["answer"] for task_result in task_results],
        )

        logger.info(
            "Procedure %d | %s: making final LLM call for report creation",
            procedure_run.id,
            procedure_run.procedure_run_id,
        )
        response: MessageResponse = make_llm_call(
            procedure_id=procedure_run.id,
            procedure_run_id=procedure_run.procedure_run_id,
            task_run_id="procedure_report",
            org_id=procedure_run.org_id,
            store_json_location=(
                f"{base_path}/{os.environ['BLAI_ENV']}/{procedure_run.org_id}/"
                f"{procedure_run.id}/{procedure_run.procedure_run_id}"
            ),
            user_query=final_prompt,
            root_component=reporter_component,
            local_timezone=procedure_run.local_timezone,
        )

        logger.info(
            "Procedure %d | %s: got reponse for reporter: %s",
            procedure_run.id,
            procedure_run.procedure_run_id,
            response,
        )

        if not response.evidence:
            raise ValueError(
                f"Procedure {procedure_run.id} | {procedure_run.procedure_run_id}: "
                "no evidence in reporter call"
            )

        report_text = ""
        with smart_open.smart_open(
            f"{base_path}/{os.environ['BLAI_ENV']}/{response.evidence[0].evidence_location}",
            "r",
        ) as f:
            report_text = f.read()

        procedure_output_location = (
            f"{base_path}/{os.environ['BLAI_ENV']}/{procedure_run.org_id}/"
            f"{procedure_run.id}/{procedure_run.procedure_run_id}/procedure_output.json"
        )
        with smart_open.smart_open(procedure_output_location, "w") as f:
            f.write(json.dumps({"response": {"answer": report_text}}))

        logger.info(
            "Procedure %d | %s: finished",
            procedure_run.id,
            procedure_run.procedure_run_id,
        )
        logger.debug(
            "Procedure %d | %s: response %s",
            procedure_run.id,
            procedure_run.procedure_run_id,
            response,
        )
        notify_procedure_status(
            status=ProcedureStatus.FINISHED,
            outcome_location=(
                f"{os.environ['BLAI_ENV']}/{procedure_run.org_id}/"
                f"{procedure_run.id}/{procedure_run.procedure_run_id}/procedure_output.json"
            ),
            summarization_cost=response.cost,
        )

        task_evidences: list[Any] = [
            task_result["evidence"] for task_result in task_results
        ]  # TODO: Strongly type that Any later
        all_evidences: list[Evidence] = []
        for evidences in task_evidences:
            for evidence in evidences:
                all_evidences.append(Evidence.model_validate(evidence))

        result = ProcedureResult(
            procedure_id=str(procedure_run.id),
            procedure_run_id=procedure_run.procedure_run_id,
            executive_summary=json.loads(report_text)["executiveSummary"],
            evidence=all_evidences,
        ).model_dump(by_alias=True)

        logger.info(
            "Procedure %d | %s: returning result %s",
            procedure_run.id,
            procedure_run.procedure_run_id,
            result,
        )

        return result
    except ContentFilteringError as e:
        logger.exception(
            "Procedure %d | %s: %s raised",
            procedure_run.id,
            procedure_run.procedure_run_id,
            e.__class__.__name__,
        )
        notify_procedure_status(
            status=ProcedureStatus.FAILED,
            failed_reason=f"{e}\n{traceback.format_exc()}",
        )
        raise e
    except HTTPProblem as e:
        notify_procedure_status(
            status=ProcedureStatus.FAILED, failed_reason=e.problem.model_dump_json()
        )
    except Exception as e:
        logger.exception(
            "Procedure %d | %s: %s raised",
            procedure_run.id,
            procedure_run.procedure_run_id,
            e.__class__.__name__,
        )
        notify_procedure_status(
            status=ProcedureStatus.FAILED,
            failed_reason=f"{e}\n{traceback.format_exc()}",
        )
        raise e
