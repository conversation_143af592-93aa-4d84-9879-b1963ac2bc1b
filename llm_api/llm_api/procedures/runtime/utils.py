import json
import logging
import os
from typing import List, Optional

import botocore
import requests
import smart_open
from pydantic import SecretStr

from llm_api.blai_api.dtos import Message, MessageResponse
from llm_api.exceptions import ContentFilteringError
from llm_api.http_client import get_session
from llm_api.llm.constants import AzureOpenAIErrors
from llm_api.specs.component_spec import ComponentSpec

backend_url = os.environ["PUBLIC_BACKEND_URL"]

procedure_status_update_route = "/internal/webhooks/procedures"
procedure_name_update_route = "/internal/webhooks/procedures/name"
task_update_route = "/internal/webhooks/procedures/task"


logger = logging.getLogger(__name__)


def get_procedure_status_update_url() -> str:
    return f"{backend_url}{procedure_status_update_route}"


def get_procedure_name_update_url() -> str:
    return f"{backend_url}{procedure_name_update_route}"


def get_task_update_url() -> str:
    return f"{backend_url}{task_update_route}"


def get_task_artifact(path: str) -> dict | None:
    try:
        with smart_open.open(path, "r") as artifact_file:
            return json.load(artifact_file)
    except botocore.exceptions.ClientError as e:
        error_code = e.response.get("Error", {}).get("Code")

        if error_code == "NoSuchKey":
            return None

        raise e


def merge_context(prompt: str, context: List[str] | None) -> str:
    if not context:
        return prompt

    joined_context = "\n\n".join(context)
    return (
        f"{prompt}"
        "\n\n\n"
        "********** CONTEXT **********"
        "\n\n"
        f"{joined_context}"
        "\n\n"
        "********** CONTEXT ENDS**********"
    )


def make_llm_call(
    user_query: str,
    root_component: ComponentSpec,
    task_run_id: str,
    org_id: str,
    store_json_location: str,
    procedure_id: int,
    procedure_run_id: str,
    session: requests.Session = get_session(),
    local_timezone: Optional[str] = None,
) -> MessageResponse:
    payload = Message(
        message=user_query,
        messageId=task_run_id,
        root_component=root_component,
        aiFilteringPayload=None,
        organizationId=org_id,
        formatFinalAnswer=False,
        conversationId=None,
        storeJsonLocation=store_json_location,
    ).model_dump(
        exclude_none=True,
        exclude_unset=True,
        by_alias=True,
    )
    if local_timezone:
        payload["localTimeZone"] = local_timezone

    logger.info(
        "Procedure %d | %s > %s: making LLM call for task",
        procedure_id,
        procedure_run_id,
        task_run_id,
    )
    logger.debug(
        "Procedure %d | %s > %s: task payload %s",
        procedure_id,
        procedure_run_id,
        task_run_id,
        payload,
    )

    def reveal_secret_str(item):
        if isinstance(item, SecretStr):
            return item.get_secret_value()

    data = json.dumps(payload, default=reveal_secret_str)

    try:
        response = session.post(
            url=f"{os.environ['PROCEDURES__LLMAPP__URL']}/message",
            data=data,
        )

        logger.info(
            "Procedure %d | %s > %s: got response [%d] %s",
            procedure_id,
            procedure_run_id,
            task_run_id,
            response.status_code,
            response.text,
        )

        response.raise_for_status()

        return MessageResponse.model_validate(response.json())
    except requests.exceptions.HTTPError as err:
        logger.exception(
            "Procedure %d | %s > %s: couldn't perform LLM call",
            procedure_id,
            procedure_run_id,
            task_run_id,
        )

        try:
            resp_body = err.response.json()
            match resp_body["message"]:
                case AzureOpenAIErrors.AZURE_CONTENT_FILTER_TRIGGERED.name:
                    raise ContentFilteringError(
                        AzureOpenAIErrors.AZURE_CONTENT_FILTER_TRIGGERED
                    )
                case _:
                    raise err
        except requests.exceptions.JSONDecodeError:
            raise err
