from llm_api.llm.factory import default_3_5_gpt_spec_data
from llm_api.specs.component_spec import ComponentSpec, ComponentType
from llm_api.specs.llm_spec import LLMSpec, LLMType
from llm_api.specs.reporter_spec import ReportType

reporter_component = ComponentSpec(
    id="1",
    type=ComponentType.Coordinator,
    spec={
        "name": "Reporter specialist",
        "description": "",
        "prompt": (
            "You are a forwarding agent. "
            "Your task is to forward the CONTEXT to the reporting tool.\n"
            "Example:\n"
            "********* CONTEXT **********\n"
            "[context1]\n\n"
            "[context2]\n\n"
            "[context3]\n\n"
            "...\n"
            "********** CONTEXT ENDS**********"
        ),
        "llm": LLMSpec(
            type=LLMType.AzureChatOpenAI,
            data=default_3_5_gpt_spec_data,
        ).model_dump(),
    },
    children=[
        ComponentSpec(
            id="1",
            type=ComponentType.Reporter,
            spec={
                "report_type": ReportType.Short.value,
                "sections": [
                    {
                        "section_name": "executiveSummary",
                        "section_description": (
                            "This section should focus on the decision the report "
                            "is supporting and the highest level detail an executive "
                            "would care about. It should focus on the single largest "
                            "takeaway from the alert analysis and how it fits into the "
                            "larger risk landscape. This section should not summarize "
                            "the underlying reports used to create the analysis. "
                            "It should be able to convey the most important analysis "
                            "to the reader, so that they can skip the rest of the "
                            "report and still be able to take an informed action."
                        ),
                    },
                    {
                        "section_name": "keyFindings",
                        "section_description": (
                            "This section should focus on the key findings."
                        ),
                    },
                    {
                        "section_name": "outcome",
                        "section_description": (
                            "Conclusion or Final Outcome associated with the procedure. "
                            "It MUST be one of the given enumerated options reflecting "
                            "the outcome of all the tasks.\n\n"
                            "Options:\n"
                            "- True Positive\n"
                            "- False Positive\n"
                            "- Further investigation required\n"
                            "- Report generated\n\n"
                            "This section MUST be ONLY the OPTION chosen. "
                            "No description. DO NOT EXPLAIN YOURSELF IN THIS SECTION."
                        ),
                    },
                    {
                        "section_name": "actionsTaken",
                        "section_description": (
                            "A bulleted list of explicit actions taken as a part of "
                            "the process based on the context given.\n\n"
                            "Actions can be one or more of the following choices:\n"
                            "Actions Taken:\n"
                            "- Recommendations created\n"
                            "- Device containment initiated\n"
                            "- Slack notification sent\n"
                            "- Report generated\n\n"
                            "Think carefully and choose as many of the options listed "
                            "above as required. NO OTHER DETAILS."
                        ),
                    },
                ],
            },
            children=[],
        )
    ],
)
