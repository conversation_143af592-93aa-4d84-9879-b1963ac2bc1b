import enum
from enum import Enum
from typing import Optional

from pydantic import BaseModel, ConfigDict, Field

from llm_api.blai_api.dtos import Evidence
from llm_api.specs.component_spec import ComponentSpec


class TaskType(Enum):
    AICall = "ai_call"


class TaskRunConfiguration(BaseModel):
    model_config = ConfigDict(populate_by_name=True, use_enum_values=True)

    component: ComponentSpec = Field(
        description="The root component to be used in this task call.",
        alias="components",
    )
    prompt: str = Field(
        description="The prompt used in this task.",
    )
    inputs: list[str] = Field(
        description="The list of inputs expected by this task.",
    )
    task_type: TaskType = Field(
        description="Type of task. For now, just AITask.",
        default=TaskType.AICall,
    )


class TaskRun(BaseModel):
    model_config = ConfigDict(populate_by_name=True)

    id: int = Field(
        description="The ID of the task template.",
    )
    task_run_id: str = Field(
        description="The ID of the task run",
        alias="taskRunId",
    )
    configuration: TaskRunConfiguration = Field(
        description="The configuration of the task.",
    )
    dependencies: list[int] = Field(
        description="Context-based dependencies of this task."
    )


class ProcedureInput(BaseModel):
    model_config = ConfigDict(populate_by_name=True)

    name: str = Field(
        description="The name of the input variable",
        alias="Name",
    )
    value: str = Field(
        description="The value of the input variable",
        alias="Value",
    )


class ConsumerType(enum.IntEnum):
    NoType = -1
    User = 0
    ApiClient = 1


class ProcedureRun(BaseModel):
    model_config = ConfigDict(populate_by_name=True)

    id: int = Field(
        description="The ID of the procedure template",
    )
    procedure_run_id: str = Field(
        description="The ID of the procedure run",
        alias="procedureRunId",
    )
    inputs: list[ProcedureInput]
    tasks: list[TaskRun] = Field(
        description="The final task in the procedure",
    )
    org_id: str = Field(
        description="The ID of the organization triggering this procedure",
        alias="orgId",
    )
    consumer_id: str = Field(
        description="The ID of the initiator of the procedure run (used only for procedure as a tool cases)",
        alias="consumerId",
        # default value is empty since this value is only required
        # when running a procedure from a conversation.
        default="",
    )
    consumer_type: ConsumerType = Field(
        description="The type of consumer that triggered this run",
        alias="consumerType",
        default=ConsumerType.NoType,
    )
    local_timezone: Optional[str] = Field(
        description="The local time of the user",
        default=None,
        alias="localTimeZone",
    )

    message_correlation_id: str = Field(
        description="The correlation id used to match procedure runs and conversations",
        alias="messageCorrelationId",
        default="",
    )

    conversation_id: str = Field(
        description="The conversation id that started this run (in case it was started from a conversation)",
        alias="conversationId",
        default="",
    )


class ProcedureResult(BaseModel):
    procedure_id: str
    procedure_run_id: str
    executive_summary: str
    evidence: list[Evidence]


class ProcedureNameGeneration(BaseModel):
    org_id: str = Field(
        description="The ID of the organization triggering this procedure",
        alias="orgId",
    )
    id: int = Field(
        description="The ID of the procedure template",
        alias="procedureId",
    )
    procedure_run_id: str = Field(
        description="The ID of the procedure run",
        alias="procedureRunId",
    )
    procedure_name: str = Field(
        description="The original name of the procedure",
        alias="procedureName",
    )
    procedure_description: str = Field(
        description="The description of the procedure",
        alias="procedureDescription",
    )
    inputs: list[ProcedureInput] = Field(
        description="The inputs used throughout the procedure run",
        default=[],
    )
    consumer_id: str = Field(
        description="The ID of the initiator of the procedure run. (used only for procedure as a tool cases)",
        alias="consumerId",
    )
    consumer_type: ConsumerType = Field(
        description="The type of consumer that triggered this run.",
        alias="consumerType",
        default=ConsumerType.NoType,
    )
