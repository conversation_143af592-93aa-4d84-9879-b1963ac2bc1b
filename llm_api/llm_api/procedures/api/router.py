import asyncio
import logging
import os
from functools import partial

import redis
from celery import Signature, chain
from fastapi import APIRouter
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from llm_api.procedures.celery import tasks as celery_tasks
from llm_api.procedures.celery.tasks.workflow import (
    build_task_workflow,
    generate_procedure_run_celery_key,
)
from llm_api.procedures.types import (
    ProcedureNameGeneration,
    ProcedureResult,
    ProcedureRun,
)

logger = logging.getLogger(__name__)

router = APIRouter()

# Redis connection
redis_url = os.getenv("PROCEDURES__REDIS__URL", "localhost:6379")
redis_host, redis_port = redis_url.split(":")
redis_client = redis.Redis(host=redis_host, port=int(redis_port), db=0)


class ProcedureError(BaseModel):
    msg: str


@router.post("/start", responses={500: {"model": ProcedureError}})
async def start(procedure_run: ProcedureRun):
    logger.info(
        "Starting procedure %d | %s: %s",
        procedure_run.id,
        procedure_run.procedure_run_id,
        procedure_run,
    )

    tasks_workflow: Signature
    tasks_workflow = build_task_workflow(procedure_run)

    process_tasks_signature: Signature = celery_tasks.process_task_results.s(
        procedure_run_input=procedure_run.model_dump(mode="json")
    )
    process_tasks_signature.set(
        task_id=generate_procedure_run_celery_key(procedure_run)
    )

    chain(tasks_workflow, process_tasks_signature).apply_async()


@router.post("/start_sync", responses={500: {"model": ProcedureError}})
async def start_sync(procedure_run: ProcedureRun):
    logger.info(
        "Starting procedure %d | %s: %s",
        procedure_run.id,
        procedure_run.procedure_run_id,
        procedure_run,
    )

    try:
        tasks_workflow: Signature
        tasks_workflow = build_task_workflow(procedure_run)

        process_tasks_signature: Signature = celery_tasks.process_task_results.s(
            procedure_run_input=procedure_run.model_dump(mode="json")
        )
        process_tasks_signature.set(
            task_id=generate_procedure_run_celery_key(procedure_run)
        )

        # Use an executor to wait for the blocking get() call
        loop = asyncio.get_running_loop()
        result = chain(tasks_workflow, process_tasks_signature).apply_async()

        # wait for 10 minutes max
        procedure_result_output = await loop.run_in_executor(
            None, partial(result.get, timeout=600)
        )

        logger.info(
            "Procedure status for procedure %d | %s: %s",
            procedure_run.id,
            procedure_run.procedure_run_id,
            result.state,
        )

        procedure_result = ProcedureResult.model_validate(procedure_result_output)

        return JSONResponse(
            status_code=200, content=procedure_result.model_dump(by_alias=True)
        )
    except Exception as err:
        logger.error(
            "Procedure error %d | %s: %s",
            procedure_run.id,
            procedure_run.procedure_run_id,
            err,
        )
        return JSONResponse(
            status_code=500,
            content=ProcedureError(msg=str(err)).model_dump(),
        )


@router.post("/procedure-name")
async def generate_procedure_run_name(
    procedure_run_name_input: ProcedureNameGeneration,
):
    logger.info(
        "Generating name for procedure %d | %s: %s",
        procedure_run_name_input.id,
        procedure_run_name_input.procedure_run_id,
        procedure_run_name_input,
    )
    celery_tasks.generate_procedure_run_name.delay(
        procedure_run_name_spec=procedure_run_name_input.model_dump(
            by_alias=True, mode="json"
        )
    )


@router.post("/halt")
async def halt_procedure_processing():
    redis_client.set("procedure_processing_halted", "True")
    return JSONResponse(
        status_code=200, content={"message": "Procedure processing halted"}
    )


@router.post("/resume")
async def resume_procedure_processing():
    redis_client.set("procedure_processing_halted", "False")
    return JSONResponse(
        status_code=200, content={"message": "Procedure processing resumed"}
    )


@router.get("/healthcheck")
async def healthcheck():
    return JSONResponse(status_code=200, content={"status": "OK"})
