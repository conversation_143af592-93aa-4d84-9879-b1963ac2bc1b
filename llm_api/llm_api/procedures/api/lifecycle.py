import logging
from contextlib import asynccontextmanager

import fastapi

from llm_api.procedures.api.router import router

logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: fastapi.FastAPI):
    """
    Lifecycle method that allows settings things up at startup
    and tearing things down at shutdown.
    """

    await app_startup(app)

    yield

    await app_shutdown(app)


async def app_startup(app: fastapi.FastAPI):
    """
    Do startup things.
    """
    logger.info("APP STARTUP")
    app.include_router(router)


async def app_shutdown(_: fastapi.FastAPI):
    """
    Do shutdown things.
    """
    logger.info("APP SHUTDOWN")
    pass
