import os

from celery import Celery

from llm_api.utils import get_feature_flag_value

celery_app = Celery(
    broker=f"redis://{os.environ['PROCEDURES__REDIS__URL']}",
    backend=f"redis://{os.environ['PROCEDURES__REDIS__URL']}",
)
celery_app.conf.update(
    accept_content=["json"],
    broker_transport_options={"visibility_timeout": 900},
    enable_utc=True,
    result_serializer="json",
    task_acks_late=True,
    task_default_queue="celery",
    task_reject_on_worker_lost=True,
    task_send_sent_event=True,
    task_serializer="json",
    worker_prefetch_multiplier=1,
    worker_send_task_events=True,
)

if get_feature_flag_value("ENABLE_HEALTH_CHECKS"):
    from llm_api.procedures.celery import bootstraps

    celery_app.steps["worker"].add(bootstraps.DeadlockDetectionStep)
    celery_app.steps["worker"].add(bootstraps.LivenessProbeStep)
