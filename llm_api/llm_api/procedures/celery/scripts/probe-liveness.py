#!/usr/bin/env python
import os
import sys
from pathlib import Path
from time import time

DEADLOCK_TIMEOUT = int(os.getenv("PROCEDURES_WORKERS_DEADLOCK_TIMEOUT", "900"))
HEALTH_CHECK_DIR = os.getenv(
    "PROCEDURE_HEALTH_CHECK_DIR", "/tmp/procedure_health_check"
)
LIVENESS_FILE: str = f"{HEALTH_CHECK_DIR}/celery_alive"
LIVENESS_TTL = int(os.getenv("PROCEDURES_WORKERS_LIVENESS_TTL", "300"))
TASK_QUEUE_FILE: str = f"{HEALTH_CHECK_DIR}/task_queue_count"


def perform_liveness_check():
    if not Path(LIVENESS_FILE).is_file():
        print("Celery liveness file not found, exiting ...")
        sys.exit(1)

    now = int(time())
    last_modified_ts: int = int(Path(LIVENESS_FILE).stat().st_mtime)

    if now - last_modified_ts > LIVENESS_TTL:
        print(
            "Procedure workers: liveness file hasn't been touched for over "
            f"{LIVENESS_TTL} seconds. Exiting ..."
        )
        sys.exit(1)

    task_queue_count: int | None = None
    if Path(TASK_QUEUE_FILE).is_file():
        with open(TASK_QUEUE_FILE, "r") as f:
            task_queue_count = int(f.read())

    worker_last_active_files: list[Path] = list(
        Path(HEALTH_CHECK_DIR).glob("worker.*.active")
    )
    for worker_last_active in worker_last_active_files:
        worker_last_active_ts: int = int(worker_last_active.stat().st_mtime)
        worker_in_deadlock = now - worker_last_active_ts > DEADLOCK_TIMEOUT
        if worker_in_deadlock and task_queue_count is not None and task_queue_count > 0:
            worker_name = worker_last_active.name.split(".")[1]
            print(
                f"Procedure workers: celery worker {worker_name} has been idle "
                f"for over {int(DEADLOCK_TIMEOUT / 60)} minutes "
                f"while there are {task_queue_count} tasks in the queue. Exiting ..."
            )
            sys.exit(1)


ENABLE_HEALTH_CHECKS = bool(int(os.getenv("FF_ENABLE_HEALTH_CHECKS", "0")))

if ENABLE_HEALTH_CHECKS:
    perform_liveness_check()
