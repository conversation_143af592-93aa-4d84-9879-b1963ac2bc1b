#!/usr/bin/env python
import os
import sys
from pathlib import Path

READINESS_FILE: str = (
    f"{os.getenv('PROCEDURE_HEALTH_CHECK_DIR', '/tmp/procedure_health_check')}"
    "/celery_ready"
)


def perform_readiness_check():
    if not Path(READINESS_FILE).is_file():
        print("Procedure workers: readiness file not found. Exiting ...")
        sys.exit(1)


ENABLE_HEALTH_CHECKS = bool(int(os.getenv("FF_ENABLE_HEALTH_CHECKS", "0")))

if ENABLE_HEALTH_CHECKS:
    perform_readiness_check()
