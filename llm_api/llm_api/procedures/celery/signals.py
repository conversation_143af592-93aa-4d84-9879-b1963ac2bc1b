import logging
import logging.config
import os
from pathlib import Path

import redis
from celery.signals import (
    celeryd_init,
    setup_logging,
    task_prerun,
    task_received,
    task_retry,
    worker_ready,
    worker_shutdown,
)
from filelock import FileLock

from llm_api.log_utils import logging_config
from llm_api.procedures.celery.constants import (
    HEALTH_CHECK_DIR,
    READINESS_FILE,
    TASK_QUEUE_FILE,
)
from llm_api.utils import get_feature_flag_value

logger = logging.getLogger(__name__)


@setup_logging.connect
def setup_logging_fn(**_):
    logging.config.dictConfig(config=logging_config)


@task_retry.connect
def task_retried_handler(
    sender=None, task_id=None, args=None, kwargs=None, einfo=None, **other_kwargs
):
    exception = einfo.exception if einfo else "No exception info"
    logger.warning(
        "Task %s is retrying due to %s (%s) [%s]", task_id, exception, args, kwargs
    )


if get_feature_flag_value("ENABLE_HEALTH_CHECKS"):

    @celeryd_init.connect
    def create_health_check_folder(**_):
        Path(HEALTH_CHECK_DIR).mkdir(parents=True, exist_ok=True)

    @worker_ready.connect
    def create_readiness_file(**_):
        with FileLock(f"{READINESS_FILE}.lock"):
            Path(READINESS_FILE).touch()

    @task_prerun.connect
    def update_worker_last_active(task_id, task, *_, **__):
        worker_last_active = f"{HEALTH_CHECK_DIR}/worker.{task.request.hostname}.active"

        with FileLock(f"{worker_last_active}.lock"):
            Path(worker_last_active).touch()

    @worker_shutdown.connect
    def remove_worker_last_active_files(*_, **__):
        for path in Path(HEALTH_CHECK_DIR).glob("worker.*.active"):
            with FileLock(f"{HEALTH_CHECK_DIR}/{path.name}.lock"):
                path.unlink(missing_ok=True)

    @task_received.connect
    def update_task_queue_count(*_, **__):
        redis_client = redis.from_url(
            f"redis://{os.environ['PROCEDURES__REDIS__URL']}",
            db=0,
        )
        with FileLock(f"{TASK_QUEUE_FILE}.lock"):
            with open(TASK_QUEUE_FILE, "w") as f:
                f.write(str(redis_client.llen("celery")))
