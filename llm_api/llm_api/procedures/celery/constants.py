import os

HEALTH_CHECK_DIR = os.getenv(
    "PROCEDURE_HEALTH_CHECK_DIR", "/tmp/procedure_health_check"
)
LIVENESS_FILE = f"{HEALTH_CHECK_DIR}/celery_alive"
READINESS_FILE = f"{HEALTH_CHECK_DIR}/celery_ready"
TASK_QUEUE_FILE = f"{HEALTH_CHECK_DIR}/task_queue_count"

CELERY_PROCEDURE_REPORT_MAX_RETRIES = int(
    os.getenv("CELERY_PROCEDURE_REPORT_MAX_RETRIES", 3)
)
CELERY_PROCEDURE_TASK_MAX_RETRIES = int(
    os.getenv("CELERY_PROCEDURE_TASK_MAX_RETRIES", 3)
)
