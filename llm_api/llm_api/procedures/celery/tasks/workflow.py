import logging
from collections import defaultdict, deque

from celery import Signature, chain, group

from llm_api.procedures.celery.types import DependencyTask, ProcedureTaskCeleryInput
from llm_api.procedures.types import ProcedureRun, TaskRun

logger = logging.getLogger(__name__)


def generate_procedure_run_celery_key(procedure_run: ProcedureRun) -> str:
    return f"blai-p-{procedure_run.id}-pr-{procedure_run.procedure_run_id}"


def generate_procedure_run_task_celery_key(
    procedure_run: ProcedureRun, task_run: TaskRun
) -> str:
    return (
        f"blai-p-{procedure_run.id}-pr-{procedure_run.procedure_run_id}-"
        f"t-{task_run.id}-tr-{task_run.task_run_id}"
    )


def build_task_workflow(procedure_run: ProcedureRun) -> Signature:
    from llm_api.procedures.celery.tasks import start_task

    task_id_task_run_id_map: dict[int, str] = {
        task.id: task.task_run_id for task in procedure_run.tasks
    }

    # An inverted representation of tasks and the subtasks that depend on them
    children_tasks: dict[int, list[int]] = defaultdict(list)

    # A dictionary for keeping track of how many dependencies a task currently has
    task_dep_count: dict[int, int] = defaultdict(int)

    task_id_celery_signature_map: dict[int, Signature] = {}
    top_level_tasks: list[int] = []

    for task_run in procedure_run.tasks:
        if not task_run.dependencies:
            top_level_tasks.append(task_run.id)

        task_dep_count[task_run.id] = len(task_run.dependencies)

        for dep_id in task_run.dependencies:
            children_tasks[dep_id].append(task_run.id)

        task_input_spec = ProcedureTaskCeleryInput(
            procedure_id=procedure_run.id,
            procedure_run_id=procedure_run.procedure_run_id,
            procedure_inputs=procedure_run.inputs,
            task_run_spec=task_run,
            dependency_tasks=[
                DependencyTask(id=task_id, run_id=task_id_task_run_id_map[task_id])
                for task_id in task_run.dependencies
            ],
            org_id=procedure_run.org_id,
            consumer_id=procedure_run.consumer_id,
            consumer_type=procedure_run.consumer_type,
            message_correlation_id=procedure_run.message_correlation_id,
            conversation_id=procedure_run.conversation_id,
        ).model_dump(mode="json")
        task_input_spec["local_timezone"] = procedure_run.local_timezone

        celery_task_signature: Signature = start_task.s(
            task_input_spec=task_input_spec,
        )
        celery_task_signature.set(
            task_id=generate_procedure_run_task_celery_key(procedure_run, task_run)
        )
        task_id_celery_signature_map[task_run.id] = celery_task_signature

    # Queue for tasks with no dependencies. As the while loop progresses,
    # new tasks will be considered dependency-less and added to the queue.
    task_queue = deque(top_level_tasks)
    task_workflow_steps: list[Signature] = []

    while task_queue:
        # Execute all tasks in the current layer in parallel
        workflow_step: list[Signature] = []
        for _ in range(len(task_queue)):
            task_id = task_queue.popleft()
            workflow_step.append(task_id_celery_signature_map[task_id])
            for child_task_id in children_tasks[task_id]:
                task_dep_count[child_task_id] -= 1
                if not task_dep_count[child_task_id]:
                    task_queue.append(child_task_id)
        if len(workflow_step) > 1:
            task_workflow_steps.append(group(workflow_step))
        else:
            task_workflow_steps.append(workflow_step[0])

        logger.info(
            "Procedure %d | %s: step %d of workflow will run tasks %s",
            procedure_run.id,
            procedure_run.procedure_run_id,
            len(task_workflow_steps),
            [
                (
                    sig.kwargs["task_input_spec"]["task_run_spec"]["id"],
                    sig.kwargs["task_input_spec"]["task_run_spec"]["task_run_id"],
                )
                for sig in workflow_step
            ],
        )

    # Combine all steps into a chain
    return chain(*task_workflow_steps)
