import functools
import json
import logging
import os
import time

import redis
import requests
import smart_open
from langchain_core.messages import SystemMessage
from requests.adapters import <PERSON><PERSON><PERSON><PERSON>pter, Retry

import llm_api.blai_llm.chunk_traceability
from llm_api.exceptions import ContentFilteringError, HTTPProblem
from llm_api.http_client import get_session
from llm_api.procedures import exceptions
from llm_api.procedures.celery.app import celery_app
from llm_api.procedures.celery.constants import (
    CELERY_PROCEDURE_REPORT_MAX_RETRIES,
    CELERY_PROCEDURE_TASK_MAX_RETRIES,
)
from llm_api.procedures.celery.types import ProcedureTaskCeleryInput
from llm_api.procedures.notifications import TaskStatus
from llm_api.procedures.notifications import notify_task_status as _notify_task_status
from llm_api.procedures.runtime import run_procedure_task_results_processing
from llm_api.procedures.runtime.utils import (
    get_procedure_name_update_url,
    get_task_artifact,
    make_llm_call,
    merge_context,
)
from llm_api.procedures.types import ProcedureNameGeneration, ProcedureRun
from llm_api.specs.llm_spec import LLMSpec, LLMType
from llm_api.utils import get_feature_flag_value

logger = logging.getLogger(__name__)


@celery_app.task(
    acks_late=True,
    autoretry_for=(Exception,),
    dont_autoretry_for=(
        (
            exceptions.TaskRunNotModified,
            exceptions.TaskRunNotFound,
            *(
                (ContentFilteringError,)
                if get_feature_flag_value("RAISE_SPECIFIC_CONTENT_FILTERING_ERROR")
                else ()
            ),
        )
        if get_feature_flag_value("ENABLE_AUTORETRY_EXCEPTIONS")
        else ()
    ),
    bind=True,
    max_retries=CELERY_PROCEDURE_TASK_MAX_RETRIES,
    retry_backoff=60,
)
def start_task(self, *_, task_input_spec: dict):
    task_input: ProcedureTaskCeleryInput = ProcedureTaskCeleryInput.model_validate(
        task_input_spec
    )
    if "local_timezone" in task_input_spec:
        task_input.local_timezone = task_input_spec["local_timezone"]

    redis_client = redis.from_url(
        f"redis://{os.environ['PROCEDURES__REDIS__URL']}",
        db=0,
    )
    halted = redis_client.get("procedure_processing_halted")

    if halted and halted.decode("utf-8") == "True":
        logger.warning(
            "Procedure %d | %s > %s: processing is halted, retrying task later",
            task_input.procedure_id,
            task_input.procedure_run_id,
            task_input.task_run_spec.task_run_id,
        )
        raise llm_api.blai_llm.chunk_traceability.retry(
            exc=Exception("Processing is halted"), countdown=60
        )

    notify_task_status = functools.partial(
        _notify_task_status,
        task_input.procedure_id,
        task_input.procedure_run_id,
        task_input.task_run_spec,
        consumer_id=task_input.consumer_id,
        consumer_type=task_input.consumer_type,
        message_correlation_id=task_input.message_correlation_id,
        conversation_id=task_input.conversation_id,
        session=get_session(with_retries=True),
    )

    def _start_task(task_input: ProcedureTaskCeleryInput):
        task_output_basepath = os.environ["PROCEDURES__WORKERS__BASEPATH"]
        relative_procedure_run_path = (
            f"{os.environ['BLAI_ENV']}/{task_input.org_id}"
            f"/{task_input.procedure_id}/{task_input.procedure_run_id}"
        )
        full_procedure_run_path = (
            f"{task_output_basepath}/{relative_procedure_run_path}"
        )
        relative_task_output_path = (
            f"{relative_procedure_run_path}/{task_input.task_run_spec.task_run_id}.json"
        )
        full_task_output_path = (
            f"{full_procedure_run_path}/{task_input.task_run_spec.task_run_id}.json"
        )

        logger.info(
            "Procedure %d | %s > %s: starting AI task",
            task_input.procedure_id,
            task_input.procedure_run_id,
            task_input.task_run_spec.task_run_id,
        )

        start = time.perf_counter()

        notify_task_status(status=TaskStatus.RUNNING)

        # First, insert inputs into prompt
        formatted_prompt = task_input.task_run_spec.configuration.prompt.format(
            **{
                "procedureId": task_input.procedure_id,
                "procedureRunId": task_input.procedure_run_id,
                "taskId": task_input.task_run_spec.id,
                "taskRunId": task_input.task_run_spec.task_run_id,
                "orgId": task_input.org_id,
                **{
                    proc_input.name: proc_input.value
                    for proc_input in task_input.procedure_inputs
                },
            }
        )

        # Second, create the context, if it exists
        task_artifacts: list[dict | None] = [
            get_task_artifact(f"{full_procedure_run_path}/{dep_task.run_id}.json")
            for dep_task in task_input.dependency_tasks
        ]
        context_parts: list[str] = [
            artifact["response"]["answer"] for artifact in task_artifacts if artifact
        ]

        logger.info(
            "Procedure %d | %s > %s: got %d inputs",
            task_input.procedure_id,
            task_input.procedure_run_id,
            task_input.task_run_spec.task_run_id,
            len(context_parts),
        )

        logger.info(
            "Procedure %d | %s > %s: got context %s",
            task_input.procedure_id,
            task_input.procedure_run_id,
            task_input.task_run_spec.task_run_id,
            context_parts,
        )
        final_prompt = merge_context(
            formatted_prompt,
            context=context_parts,
        )
        logger.info(
            "Procedure %d | %s > %s: final prompt %s",
            task_input.procedure_id,
            task_input.procedure_run_id,
            task_input.task_run_spec.task_run_id,
            final_prompt,
        )
        response = make_llm_call(
            user_query=final_prompt,
            root_component=task_input.task_run_spec.configuration.component,
            task_run_id=task_input.task_run_spec.task_run_id,
            org_id=task_input.org_id,
            store_json_location=full_task_output_path.rsplit("/", 1)[0],
            procedure_id=task_input.procedure_id,
            procedure_run_id=task_input.procedure_run_id,
            local_timezone=task_input.local_timezone,
        )
        end = time.perf_counter()

        output_obj = {}
        with smart_open.open(full_task_output_path, "w") as f:
            output_obj["response"] = response.model_dump(by_alias=True, mode="json")
            output_obj["input"] = {
                "prompt": final_prompt,
                "procedure_inputs": [
                    pi.model_dump(mode="json") for pi in task_input.procedure_inputs
                ],
                "configuration": task_input.task_run_spec.configuration.model_dump(
                    mode="json"
                ),
            }
            output_obj["context"] = context_parts
            output_obj["duration_sec"] = end - start
            output_obj["cost"] = response.cost if response else 0

            json.dump(output_obj, f, indent=2)

        notify_task_status(
            status=TaskStatus.FINISHED,
            outcome_location=relative_task_output_path,
            llm_cost=response.cost,
            sources=response.sources,
        )

        response = output_obj["response"]
        return {
            "answer": response["answer"],
            "evidence": response["evidence"],
        }

    try:
        result = _start_task(ProcedureTaskCeleryInput.model_validate(task_input_spec))
        return result
    except ContentFilteringError as e:
        logger.exception(
            "Procedure %d | %s > %s: raised %s",
            task_input.procedure_id,
            task_input.procedure_run_id,
            task_input.task_run_spec.task_run_id,
            e.__class__.__name__,
        )
        notify_task_status(status=TaskStatus.FAILED, failed_reason=str(e))
        raise e
    except HTTPProblem as e:
        if e.problem.status == 429:
            logger.exception(
                "Procedure %d | %s > %s: raised %s",
                task_input.procedure_id,
                task_input.procedure_run_id,
                task_input.task_run_spec.task_run_id,
                e.__class__.__name__,
            )
            notify_task_status(
                status=TaskStatus.FAILED, failed_reason=e.problem.model_dump_json()
            )
            raise e
        if e.problem.status == 413:
            logger.exception(
                "Procedure %d | %s > %s: raised %s",
                task_input.procedure_id,
                task_input.procedure_run_id,
                task_input.task_run_spec.task_run_id,
                e.__class__.__name__,
            )
            notify_task_status(
                status=TaskStatus.FAILED, failed_reason=e.problem.model_dump_json()
            )
            raise e
        notify_task_status(
            status=TaskStatus.FAILED, failed_reason=e.problem.model_dump_json()
        )
    except Exception as e:
        logger.exception(
            "Procedure %d | %s > %s: raised %s",
            task_input.procedure_id,
            task_input.procedure_run_id,
            task_input.task_run_spec.task_run_id,
            e.__class__.__name__,
        )
        notify_task_status(status=TaskStatus.FAILED, failed_reason=str(e))
        raise e


@celery_app.task(
    acks_late=True,
    autoretry_for=(Exception,),
    dont_autoretry_for=(
        (
            exceptions.ProcedureRunNotModified,
            exceptions.ProcedureRunNotFound,
            *(
                (ContentFilteringError,)
                if get_feature_flag_value("RAISE_SPECIFIC_CONTENT_FILTERING_ERROR")
                else ()
            ),
        )
        if get_feature_flag_value("ENABLE_AUTORETRY_EXCEPTIONS")
        else ()
    ),
    bind=True,
    max_retries=CELERY_PROCEDURE_REPORT_MAX_RETRIES,
    retry_backoff=60,
    soft_time_limit=840,  # 14 minutes
    time_limit=870,  # 14.5 minutes
)
def process_task_results(self, *_, procedure_run_input: dict):
    procedure_run = ProcedureRun.parse_obj(procedure_run_input)
    if "local_timezone" in procedure_run_input:
        procedure_run.local_timezone = procedure_run_input["local_timezone"]

    redis_client = redis.from_url(
        f"redis://{os.environ['PROCEDURES__REDIS__URL']}",
        db=0,
    )
    halted = redis_client.get("procedure_processing_halted")

    if halted and halted.decode("utf-8") == "True":
        logger.warning(
            "Procedure %d | %s: processing is halted, retrying task later",
            procedure_run.id,
            procedure_run.procedure_run_id,
        )
        raise llm_api.blai_llm.chunk_traceability.retry(
            exc=Exception("Processing is halted"), countdown=60
        )

    return run_procedure_task_results_processing(
        base_path=os.environ["PROCEDURES__WORKERS__BASEPATH"],
        procedure_run=procedure_run,
    )


@celery_app.task(
    acks_late=True,
    autoretry_for=(Exception,),
    max_retries=5,
    retry_backoff=60,
)
def generate_procedure_run_name(procedure_run_name_spec: dict):
    from llm_api.llm.factory import default_4_gpt_spec_data, get_model_from_spec

    procedure_name_generation = ProcedureNameGeneration.parse_obj(
        procedure_run_name_spec
    )
    llm = get_model_from_spec(
        spec=LLMSpec(
            type=LLMType.AzureChatOpenAI,
            data=default_4_gpt_spec_data,
        ),
    )
    formatted_inputs: str = "\n".join(
        [
            f"Key: {input.name}\nValue: {input.value}"
            for input in procedure_name_generation.inputs
        ]
    )
    message: str = (
        "You are a naming assistant. Your SOLE responsibility is to give a title "
        "to a procedure run in 2-3 words based on the NAME "
        "of the procedure itself, its DESCRIPTION and the INPUTS provided for that "
        "particular run. The name and description tell you about what the process "
        "is for while the INPUT values are what differentiate one run from another "
        "and IDENTIFY individual procedure runs. Here are the details:\n\n"
        "Procedure Name:\n"
        f"{procedure_name_generation.procedure_name}\n\n"
        "Procedure Description:\n"
        f"{procedure_name_generation.procedure_description}\n\n"
        "Inputs:\n"
        f"{formatted_inputs}\n\n"
        "Reply with ONLY ONE SPECIFIC NAME WITHOUT ANY FORMATTING`"
    )

    logger.info(
        "Procedure %d | %s: generating name using LLM",
        procedure_name_generation.id,
        procedure_name_generation.procedure_run_id,
    )
    llm_response = llm.invoke([SystemMessage(content=message)])

    logger.info(
        "Procedure %d | %s: prompt %s",
        procedure_name_generation.id,
        procedure_name_generation.procedure_run_id,
        message,
    )

    logger.info(
        "Procedure %d | %s: got response: %s",
        procedure_name_generation.id,
        procedure_name_generation.procedure_run_id,
        llm_response.content,
    )

    session = get_session(with_retries=True)

    webhook_payload = {
        "procedureRunId": procedure_name_generation.procedure_run_id,
        "procedureRunName": llm_response.content,
    }
    logger.info(
        "Procedure %d | %s: notify backend about procedure PATCH %s: %s",
        procedure_name_generation.id,
        procedure_name_generation.procedure_run_id,
        get_procedure_name_update_url(),
        webhook_payload,
    )

    webhook_resp = session.patch(
        url=get_procedure_name_update_url(),
        json=webhook_payload,
        timeout=int(os.environ.get("PROCEDURES__WORKERS__REQUEST__TIMEOUT", "10")),
    )
    try:
        webhook_resp.raise_for_status()
    except requests.exceptions.HTTPError:
        logger.exception(
            "Procedure %d | %s: couldn't notify backend about procedure PATCH",
            procedure_name_generation.id,
            procedure_name_generation.procedure_run_id,
        )
