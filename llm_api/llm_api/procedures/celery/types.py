from typing import Optional

from pydantic import BaseModel, <PERSON>

from llm_api.procedures.types import ConsumerType, ProcedureInput, TaskRun


class DependencyTask(BaseModel):
    id: int = Field(description="Task template ID")
    run_id: str = Field(description="Task run ID")


class ProcedureTaskCeleryInput(BaseModel):
    procedure_id: int = Field(description="Parent procedure template ID")
    procedure_run_id: str = Field(description="Parent procedure run ID")
    procedure_inputs: list[ProcedureInput] = Field(
        description="The inputs submitted on procedure run"
    )
    task_run_spec: TaskRun = Field(description="The task run spec")
    dependency_tasks: list[DependencyTask] = Field(
        description="The list of tasks this task needs to be already finished"
    )
    org_id: str = Field(description="The organization ID")
    consumer_id: str = Field(
        description="The ID of the initiator of the procedure run (used only for procedure as a tool cases)",
        # default value is empty since this value is only required
        # when running a procedure from a conversation.
        default="",
    )
    consumer_type: ConsumerType = Field(
        description="The type of consumer that triggered this run",
        default=ConsumerType.NoType,
    )
    message_correlation_id: str = Field(
        description="The correlation ID used to match procedure runs and conversations",
        default="",
    )
    conversation_id: str = Field(
        description="The conversation ID that started this run (in case it was started from a conversation)",
        default="",
    )
    local_timezone: Optional[str] = Field(
        description="The local time of the user",
        default=None,
    )
