{"id": 1, "procedureRunId": "aa76a7f3-b161-4992-a0be-df47aac47d3d", "inputs": [{"Name": "input1", "Value": "CWE-78"}], "tasks": [{"id": 1, "taskRunId": "edf62a79-c0ed-4c29-af25-bddbd15814a3", "configuration": {"components": {"id": "c0bd9fae-9fa8-4b9f-926d-bc0ca9b004b0", "type": "coordinator", "spec": {"name": "Cybersecurity Manager Agent", "description": "Useful for answering questions about Overall Strategy, Policy Development and Risk Management.", "prompt": " I want you to act as a Cybersecurity Manager. You are designed to be able to assist with \nquestion-answering tasks using a list of trusty tools that are more reliable resources than you. \nUse the information retrieved to create a DETAILED report.\nYou should ALWAYS USE at least 2 tools to try and answer the question. \nCREATE a PLAN with 2 tools you would like to use and then execute them.\n \nYou must THINK and decide which information out of all of the answers retrieved is relevent.\nIf NONE of them have relevant information, TRY answering the question yourself\n\nYou MUST follow a list of rules while answering any question asked of you:\n\n***** RULES *****\n\nRule 1) Whenever possible you MUST use the tools to retrieve relevant information. \n\nRule 2) You MUST CREATE A PLAN to answer the questions regardless of how simple they are first, and then execute the plan step by step. Always ask follow-up questions to better your research. Be curious about the impact of the previous question on a company, if there are any patterns associated with it, et cetera.\n\nRule 3) For complex questions, some might require the use of more than one tool. You MUST break the task down into atomic tasks and solve the main task step by step.\n\n*****************\n\nIt is recommended that you follow the blueprint below to improve the quality of the answer:\n\n*** BLUEPRINT ***\n\n1) Always INCLUDE as many details as possible.\n\n2) The answers must be in an itemized list if possible\n\n*****************\n\nOverall, you are a powerful system that can help with a wide range of tasks and provide valuable insights and information on a wide range of topics. You are here to assist. \nMake sure to think step by step.\n"}, "children": [{"id": "1", "type": "plugin", "spec": {"name": "<PERSON><PERSON><PERSON>ly<PERSON>", "description": "\n                    useful for analyzing specific domains, ip addresses, host-names or CVEs using the alienvault otx api.\n                    Input should be a fully formed question.\n                    CORRECT Examples are: 'Can you analyze example.com?' or 'Is the IP Address 67.45.123.345 considered malicious?'\n                    WRONG Examples are: 'What is an Indicator of Compromise (IOC)?'\n                    All responses should be considered sourced from [source: \"AlienVault API\"]\n                ", "credentials": {"type": "api_key", "data": {"key": "286e923c9e8d4f7364874a3bf92daf0e7b7e1ad15d775d593787c186eafb4c1c", "header": "X-OTX-API-KEY"}}, "urls": [{"base_url": "https://otx.alienvault.com", "method": "GET", "endpoints": [{"path": "/api/v1/indicators/IPv4/[value]/general", "definition": "This endpoint should retrieve information related to the IPv4 indicator specified by the IP address.", "arguments": {"IPv4 Address": {"examples": ["*******", "**********"]}}}, {"path": "/api/v1/indicators/IPv6/[value]/general", "definition": "This endpoint should retrieve information related to the IPv6 indicator specified by the IP address.", "arguments": {"IPv6 Address": {"examples": ["2001:db8:3333:4444:5555:6666:7777:8888", "2001:db8::", "2001:0db8:0001:0000:0000:0ab9:C0A8:0102"]}}}, {"path": "/api/v1/indicators/domain/[value]/general", "definition": "This endpoint should retrieve information related to the domain indicator specified by the domain name.", "arguments": {"Domain Name": {"examples": ["example.com", "spywaresite.info"]}}}, {"path": "/api/v1/indicators/hostname/[value]/general", "definition": "This endpoint should retrieve information related to the hostname indicator specified by the hostname.", "arguments": {"Host Name": {"examples": ["otx.alienvault.com", "bad-guys.no-ip.org", "alpha.beta.google.co.uk"]}}}, {"path": "/api/v1/indicators/file/[value]/general", "definition": "This endpoint should retrieve information related to the file indicator specified by the file hash.", "arguments": {"File Hash": {"examples": ["6c5360d41bd2b14b1565f5b18e5c203cf512e493"]}}}, {"path": "/api/v1/indicators/url/[value]/general", "definition": "This endpoint should retrieve information related to the URL indicator specified by the URL.", "arguments": {"URL": {"examples": ["http://www.fotoidea.com/sport/4x4_san_ponso/slides/IMG_0068.html"]}}}, {"path": "/api/v1/indicators/cve/[value]/general", "definition": "This endpoint should retrieve information related to the Common Vulnerabilities and Exposures (CVE) indicator specified by the CVE identifier.", "arguments": {"CVE": {"examples": ["CVE-2014-0160"]}}}], "select_endpoint_prompt": "\n                            You are an api composing assistant that composes an API URL by following the steps below:\n                            Step 1) Analyze the input and identify the relevant entity.\n                            Here are a list of options and examples-\n                            {examples}\n                            Step 2) Pick the correct endpoint from the list of options below based on the intent of the user and the description of the end-point. Options:\n                            {options}\n                            \nStep 3) Compose the API by replacing [value] in the correct end-point with the actual value of the entity identified.\n                            Here are some examples:\n                            Example 1)\n                            [Input] Analyze bricklayer.ai\n                            [Output] /api/v1/indicators/domain/bricklayer.ai/general\n                            Example 2)\n                            [Input] Is ************* malicious?\n                            [Output] /api/v1/indicators/IPv4/*************/general\n                            Example 3)\n                            [Input] Does 2a03:2880:10:1f02:face:b00c::25 have any issues?\n                            [Output] /api/v1/indicators/IPv6/2a03:2880:10:1f02:face:b00c::25/general\n                            Generate an [Output] for the submitted [Input] using the steps mentioned. MAKE SURE to include only the ENDPOINT in the [Output]\n                        ", "select_endpoint_validation_pattern": "\\/api\\/[0-9a-zA-Z\\/\\-\\.]*", "interpret_response_prompt": "\n                            I want you to act as a cybersecurity analyst that summarises an input json string\n                            recieved from an endpoint {endpoint} of the AlienVault Open Threat Exchange Platform\n                            and returns a detailed list of information including all links for further information.\n                            Do not include explanations of the JSON but rather focus on the inferences drawn from it.\n                            The json string is the information on a specific Indicator of Compromise (IOC)\n                            Try answering questions such as whether the Indicator seems malicious, what associated threat actors are,\n                            what devices are typically affected and its description\n                        "}]}}]}, "prompt": "Tell me about {input1}", "inputs": ["input1"]}, "dependencies": []}, {"id": 2, "taskRunId": "6db79802-f630-4ad1-ba8d-20cbb50d03ec", "configuration": {"components": {"id": "c0bd9fae-9fa8-4b9f-926d-bc0ca9b004b0", "type": "coordinator", "spec": {"name": "Cybersecurity Manager Agent", "description": "Useful for answering questions about Overall Strategy, Policy Development and Risk Management.", "prompt": " I want you to act as a Cybersecurity Manager. You are designed to be able to assist with \nquestion-answering tasks using a list of trusty tools that are more reliable resources than you. \nUse the information retrieved to create a DETAILED report.\nYou should ALWAYS USE at least 2 tools to try and answer the question. \nCREATE a PLAN with 2 tools you would like to use and then execute them.\n \nYou must THINK and decide which information out of all of the answers retrieved is relevent.\nIf NONE of them have relevant information, TRY answering the question yourself\n\nYou MUST follow a list of rules while answering any question asked of you:\n\n***** RULES *****\n\nRule 1) Whenever possible you MUST use the tools to retrieve relevant information. \n\nRule 2) You MUST CREATE A PLAN to answer the questions regardless of how simple they are first, and then execute the plan step by step. Always ask follow-up questions to better your research. Be curious about the impact of the previous question on a company, if there are any patterns associated with it, et cetera.\n\nRule 3) For complex questions, some might require the use of more than one tool. You MUST break the task down into atomic tasks and solve the main task step by step.\n\n*****************\n\nIt is recommended that you follow the blueprint below to improve the quality of the answer:\n\n*** BLUEPRINT ***\n\n1) Always INCLUDE as many details as possible.\n\n2) The answers must be in an itemized list if possible\n\n*****************\n\nOverall, you are a powerful system that can help with a wide range of tasks and provide valuable insights and information on a wide range of topics. You are here to assist. \nMake sure to think step by step.\n"}, "children": [{"id": "794cb422-abed-46b5-8e69-46ba3871e1d4", "type": "plugin", "spec": {"name": "NVD Analyzer", "description": "useful for getting common vulnerability information about specific CPEs, CWEs, Device Names, Date Ranges and CVEIDs using the NVD (National Vulnerability) api.\n Input should be a fully formed question including a specific ID such as a CWE, CVE or CPE. \n CORRECT Examples are: 'Can you tell me the vulnerabilities associated with CWE-89?' or 'What do we know about CVE-2023-23397?' \n INCORRECT Examples are: 'Can you tell me about vulnerabilities?'\n All responses should be considered sourced from [source: \"NVD API\"]", "urls": [{"base_url": "https://services.nvd.nist.gov", "method": "GET", "endpoints": [{"path": "/rest/json/cves/2.0?cpeName=[value]", "definition": "This endpoint should retrieve all Common Vulnerabilities (CVE) associated with a specific CPE identified by its CPE name. A CPE Name is a string of characters comprised of 13 colon separated values that describe a product. In CPEv2.3 the first two values are always \"cpe\" and \"2.3\". The 11 values that follow are referred to as the CPE components. When filtering by cpeName the part, vendor, product, and version components are REQUIRED to contain values other than \"*\".", "arguments": {"CPE Name": {"examples": ["cpe:2.3:o:microsoft:windows_10:1607:*:*:*:*:*:*:*", "cpe:2.3:a:microsoft:internet_explorer:8.0.6001:beta:*:*:*:*:*:*"]}}}, {"path": "/rest/json/cves/2.0?cveId=[value]", "definition": "This endpoint returns information about a specific vulnerability identified by its unique Common Vulnerabilities and Exposures identifier (the CVE ID).", "arguments": {"CVE ID": {"examples": ["CVE-2019-1010218", "CVE-2022-22954"]}}}, {"path": "/rest/json/cves/2.0?cvssV2Metrics=[value]", "definition": "This endpoint returns only the CVEs that match the provided CVSSv2 vector string. Either full or partial vector strings may be used.", "arguments": {"CVSS V2 Vector string": {"examples": ["AV:N/AC:H/Au:N/C:C/I:C/A:C", "AV:L/AC:H/Au:M/C:N/I:N/A:N"]}}}, {"path": "/rest/json/cves/2.0?cvssV3Metrics=[value]", "definition": "This endpoint returns only the CVEs that match the provided CVSS V3 vector string. Either full or partial vector strings may be used.", "arguments": {"CVSS V3 vector string": {"examples": ["AV:L/AC:L/PR:L/UI:R/S:U/C:N/I:L/A:L"]}}}, {"path": "/rest/json/cves/2.0?cweId=[value]", "definition": "This endpoint returns only the CVE that includes a weakness identified by Common Weakness Enumeration using the provided CWE ID.", "arguments": {"CWE ID": {"examples": ["CWE-287", "CWE-89"]}}}, {"path": "/rest/json/cves/2.0?keywordSearch=[value]", "definition": "This endpoint returns only the CVEs where a word or phrase is found in the current description.", "arguments": {"Keywords": {"examples": ["Microsoft", "Debian"]}}}, {"path": "/rest/json/cves/2.0/?pubStartDate=[value 1]&pubEndDate=[value 2]", "definition": "This endpoint returns only the CVEs that were added to the NVD (i.e., published) during the specified period defaulting to GMT. If filtering by the published date, both pubStartDate and pubEndDate are REQUIRED. The maximum allowable range when using any date range parameters is 120 consecutive days. Values must be entered in the extended ISO-8061 date/time format: [YYYY][\"-\"][MM][\"-\"][DD][\"T\"][HH][\":\"][MM][\":\"][SS][Z]. The \"T\" is a literal to separate the date from the time. The Z indicates an optional offset-from-UTC. ", "arguments": {"Start Date": {"examples": ["2021-08-04T00:00:00.000", "2021-10-22T00:00:00.000"]}, "End Date": {"examples": ["2021-08-04T00:00:00.000", "2021-10-22T00:00:00.000"]}}}], "select_endpoint_prompt": "\nYou are an api composing assistant that composes an API URL by following the steps below:\n Step 1) Analyze the input and identify the relevant entity. \n Here are a list of options and examples- \n {examples}\n Step 2) Pick the correct endpoint from the list of options below based on the intent of the user and the description of the end-point. Options: {options}\n \nStep 3) Compose the API by replacing [value] in the correct end-point with the actual value of the entity identified.\n \n Here are some examples:\n \nExample 1)\n [Input] Tell me all the vulnerabilities that were published between 1st Jan 2021 and 14th Jan 2021 end of day in Easter time (GMT -5 hours)? \n [Output] /rest/json/cves/2.0/?pubStartDate=2020-01-01T00: 00: 00.000-05: 00&pubEndDate=2020-01-14T23: 59: 59.999-05: 00\n \nExample 2) \n [Input] What vulnerabilities were published between the 4th August 2021 and 22nd October 2021 GMT? \n [Output] /rest/json/cves/2.0/?pubStartDate=2021-08-04T00: 00: 00.000&pubEndDate=2021-10-22T00: 00: 00.000\n \nExample 3)\n [Input] What do we know about vulnerabilities associated with Microsoft Windows 10 Version 1607?\n [Output] /rest/json/cves/2.0?cpeName=2.3:o:microsoft:windows_10_1607:-:*:*:*:*:*:*\n \n Generate an [Output] for the submitted [Input] using the steps mentioned. MAKE SURE to include only the ENDPOINT in the [Output]\n ", "select_endpoint_validation_pattern": "\\/rest\\/json\\/cves\\/2.0\\?[0-9a-zA-Z_\\/\\-:&=\\.\\*]*", "interpret_response_prompt": "\n I want you to act as a cybersecurity analyst that summarizes an input json string \n received from an endpoint {endpoint} of the National Vulnerability Database \n and returns a detailed list of information including all links for further information.\n Do not include explanations of the JSON but rather focus on the inferences drawn from it. \n The json string is the information on common vulnerabilities.\n Try answering questions such as what are the vulnerabilities, what is their description, what associated threat actors are,\n what devices are typically affected and its description.\n "}]}, "children": []}, {"id": "bf4065f8-3445-445e-b0b9-70d3615d0a6b", "type": "tidal", "spec": {"name": "Tidal Agent"}, "children": []}]}, "prompt": "Please summarize the context", "inputs": []}, "dependencies": [1]}]}