import enum
import json
import logging
import os
from typing import Any

import requests

from llm_api.blai_api.dtos import Evidence
from llm_api.http_client import get_session
from llm_api.procedures import exceptions as procedure_exceptions
from llm_api.procedures.types import ConsumerType, ProcedureRun, TaskRun

logger = logging.getLogger(__name__)


class ProcedureStatus(enum.Enum):
    RUNNING = "RUNNING"
    FINISHED = "FINISHED"
    FAILED = "FAILED"


class TaskStatus(enum.Enum):
    PENDING = "PENDING"
    RUNNING = "RUNNING"
    FINISHED = "FINISHED"
    FAILED = "FAILED"


def notify_procedure_status(
    status: ProcedureStatus,
    procedure_spec: ProcedureRun,
    outcome_location: str = "",
    failed_reason: str = "",
    summarization_cost: float | None = None,
    session: requests.Session = get_session(with_retries=True),
):
    from llm_api.procedures.runtime.utils import get_procedure_status_update_url

    notify_payload: dict[str, Any] = {
        "procedureRunId": procedure_spec.procedure_run_id,
        "status": status.value,
    }
    if outcome_location:
        notify_payload["outcomeLocation"] = outcome_location
    if summarization_cost:
        notify_payload["summarizationCost"] = summarization_cost
    if failed_reason:
        notify_payload["failedReason"] = failed_reason
    if procedure_spec.consumer_id:
        notify_payload["consumerId"] = procedure_spec.consumer_id
    if procedure_spec.consumer_type >= 0:
        notify_payload["consumerType"] = procedure_spec.consumer_type
    if procedure_spec.message_correlation_id:
        notify_payload["messageCorrelationId"] = procedure_spec.message_correlation_id
    if procedure_spec.conversation_id:
        notify_payload["conversationId"] = procedure_spec.conversation_id

    logger_args = (
        "Procedure %d | %s: notify backend about procedure PATCH %s: %s",
        procedure_spec.id,
        procedure_spec.procedure_run_id,
        get_procedure_status_update_url(),
        notify_payload,
    )

    if status == ProcedureStatus.FAILED:
        logger.error(*logger_args)
    else:
        logger.info(*logger_args)

    resp = session.patch(
        url=get_procedure_status_update_url(),
        json=notify_payload,
        timeout=int(os.environ.get("PROCEDURES__WORKERS__REQUEST__TIMEOUT", "10")),
    )

    try:
        resp.raise_for_status()

        if resp.status_code == 204:
            raise procedure_exceptions.ProcedureRunNotModified
    except requests.exceptions.HTTPError as e:
        if e.response.status_code == 404:
            raise procedure_exceptions.ProcedureRunNotFound

        logger.exception(
            "Procedure %d | %s: couldn't notify backend about procedure PATCH %s",
            procedure_spec.id,
            procedure_spec.procedure_run_id,
            notify_payload,
        )
        raise e


def notify_task_status(
    procedure_id: int,
    procedure_run_id: str,
    task_run: TaskRun,
    status: TaskStatus,
    llm_cost: float | None = None,
    outcome_location: str = "",
    sources: list[str] = [],
    consumer_id: str | None = None,
    consumer_type: ConsumerType = ConsumerType.NoType,
    message_correlation_id: str = "",
    conversation_id: str = "",
    failed_reason: str = "",
    session: requests.Session = get_session(with_retries=True),
):
    from llm_api.procedures.runtime.utils import get_task_update_url

    update_payload: dict[str, Any] = {
        "taskRunId": task_run.task_run_id,
        "procedureRunId": procedure_run_id,
        "status": status.value,
    }
    if llm_cost:
        update_payload["llmCost"] = llm_cost
    if outcome_location:
        update_payload["outcomeLocation"] = outcome_location
    if sources:
        sources = [json.loads(source) for source in sources]
        update_payload["sources"] = []
        for source in sources:
            source_entry = {
                "name": source["name"],
                "s3Path": source["evidence_location"],
                "origin": source["origin"],
            }

            if "trace" in source and source["trace"]:
                source_entry["trace"] = source["trace"]

            if "page_title" in source and source["page_title"]:
                source_entry["pageTitle"] = source["page_title"]
            update_payload["sources"].append(source_entry)

    if consumer_id:
        update_payload["consumerId"] = consumer_id
    if consumer_type >= 0:
        update_payload["consumerType"] = consumer_type
    if message_correlation_id:
        update_payload["messageCorrelationId"] = message_correlation_id
    if conversation_id:
        update_payload["conversationId"] = conversation_id
    if failed_reason:
        update_payload["failedReason"] = failed_reason

    task_update_url = get_task_update_url()
    logger.info(
        "Procedure %d | %s > %s: notify backend about task PATCH %s: %s",
        procedure_id,
        procedure_run_id,
        task_run.task_run_id,
        task_update_url,
        update_payload,
    )

    resp = session.patch(
        url=task_update_url,
        json=update_payload,
        timeout=int(os.environ.get("PROCEDURES__WORKERS__REQUEST__TIMEOUT", "10")),
    )

    try:
        resp.raise_for_status()

        if resp.status_code == 204:
            raise procedure_exceptions.TaskRunNotModified
    except requests.exceptions.HTTPError as e:
        if e.response.status_code == 404:
            raise procedure_exceptions.TaskRunNotFound

        logger.exception(
            "Procedure %d | %s > %s: couldn't notify backend about task PATCH %s",
            procedure_id,
            procedure_run_id,
            task_run.task_run_id,
            update_payload,
        )
        raise e
