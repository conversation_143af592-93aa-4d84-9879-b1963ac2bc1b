import os
from io import By<PERSON><PERSON>
from typing import Dict, Optional

import cairosvg
import markdown
import requests
from bs4 import BeautifulSoup
from docx import Document
from docx.enum.table import WD_TABLE_ALIGNMENT
from docx.oxml import OxmlElement
from docx.oxml.ns import qn
from docx.shared import Inches, Pt
from fpdf import FPDF, HTMLMixin

# Global Configuration Variables
PDF_FONT = "Arial"
PDF_FONT_PATH = os.path.join(os.path.dirname(__file__), "assets", "fonts", "arial.ttf")
PDF_TITLE_FONT_SIZE = 18  # Font size for H1
PDF_SECTION_FONT_SIZE = 16  # Font size for H2
PDF_CONTENT_FONT_SIZE = 12
PDF_MARGIN = 10
DOCX_TITLE_HEADING_LEVEL = 1  # H1
DOCX_SECTION_HEADING_LEVEL = 2  # H2
HEADER_COLOR = "#309ba3"

LOGO_COLOR_PATH = os.path.join(os.path.dirname(__file__), "assets", "Logo Color.svg")
LOGO_REVERSE_PATH = os.path.join(
    os.path.dirname(__file__), "assets", "Logo Reverse.svg"
)
LOGO_WIDTH = 138  # pixels
LOGO_HEIGHT = 22  # pixels
LEFT_MARGIN = 10
TOP_MARGIN = 10


def hex_to_rgb(hex_color: str):
    hex_color = hex_color.lstrip("#")
    return tuple(int(hex_color[i : i + 2], 16) for i in (0, 2, 4))


def pixels_to_mm(pixels):
    # Assuming 96 dpi (pixels per inch)
    mm_per_inch = 25.4
    dpi = 96
    return pixels * mm_per_inch / dpi


def docx_mm_to_emus(mm):
    # 1 inch = 25.4 mm
    # 1 inch = 914400 EMUs
    emus_per_inch = 914400
    mm_per_inch = 25.4
    return int(mm * emus_per_inch / mm_per_inch)


class MyFPDF(FPDF, HTMLMixin):
    pass


from bs4 import BeautifulSoup
from docx import Document
from docx.oxml import OxmlElement
from docx.oxml.ns import qn
from docx.shared import Pt


def add_hyperlink(paragraph, url, text):
    """
    Adds a hyperlink to a Word paragraph.

    :param paragraph: The paragraph to add the hyperlink to.
    :param url: The URL for the hyperlink.
    :param text: The display text for the hyperlink.
    """
    # Get the document's relationship part
    part = paragraph.part

    # Create a new relationship for the hyperlink
    r_id = part.relate_to(
        url,
        "http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink",
        is_external=True,
    )

    # Create the hyperlink element
    hyperlink = OxmlElement("w:hyperlink")
    hyperlink.set(qn("r:id"), r_id)

    # Create a run for the hyperlink text
    run = OxmlElement("w:r")
    run_properties = OxmlElement("w:rPr")

    # Add formatting: blue color and underline
    color = OxmlElement("w:color")
    color.set(qn("w:val"), "0000FF")  # Blue
    run_properties.append(color)

    underline = OxmlElement("w:u")
    underline.set(qn("w:val"), "single")
    run_properties.append(underline)

    run.append(run_properties)

    # Add the text to the run
    text_element = OxmlElement("w:t")
    text_element.text = text
    run.append(text_element)

    hyperlink.append(run)

    # Add the hyperlink to the paragraph
    paragraph._element.append(hyperlink)


def add_html_to_docx(doc, html_content):
    html_content = sanitize_html(html_content)
    print("html_content", html_content)

    def parse_node(node, parent_paragraph=None):
        # If this is a NavigableString, just add text to the parent paragraph
        if node.name is None:
            text = str(node)
            if text and parent_paragraph is not None:
                parent_paragraph.add_run(text)
            return

        # If the node has children, parse them in a depth-first manner
        children = list(node.children)

        # Handle tags
        if node.name in ["h1", "h2", "h3", "h4", "h5", "h6"]:
            level = int(node.name[-1]) if node.name[-1].isdigit() else 1
            heading_paragraph = doc.add_heading("", level=level)
            for child in children:
                parse_node(child, heading_paragraph)
        elif node.name == "p":
            paragraph = doc.add_paragraph()
            for child in children:
                parse_node(child, paragraph)
        elif node.name == "strong":
            # Use a new run with bold set
            if parent_paragraph is None:
                parent_paragraph = doc.add_paragraph()
            run = parent_paragraph.add_run()
            run.bold = True
            for child in children:
                if child.name is None:
                    run.add_text(str(child))
                else:
                    parse_node(child, parent_paragraph)
        elif node.name == "em":
            # Use a new run with italic set
            if parent_paragraph is None:
                parent_paragraph = doc.add_paragraph()
            run = parent_paragraph.add_run()
            run.italic = True
            for child in children:
                if child.name is None:
                    run.add_text(str(child))
                else:
                    parse_node(child, parent_paragraph)
        elif node.name == "u":
            if parent_paragraph is None:
                parent_paragraph = doc.add_paragraph()
            run = parent_paragraph.add_run()
            run.underline = True
            for child in children:
                if child.name is None:
                    run.add_text(str(child))
                else:
                    parse_node(child, parent_paragraph)
        elif node.name == "a":
            if parent_paragraph is None:
                parent_paragraph = doc.add_paragraph()
            href = node.get("href", "")
            link_text = node.get_text(strip=True)
            if href:
                add_hyperlink(parent_paragraph, href, link_text)
            else:
                parent_paragraph.add_run(link_text)
        elif node.name in ["ul", "ol"]:
            # For lists, parse each li as a separate paragraph
            for child in children:
                if child.name == "li":
                    if node.name == "ul":
                        paragraph = doc.add_paragraph("• ", style=None)
                    else:
                        # for 'ol' we can number them, but a standard approach:
                        paragraph = doc.add_paragraph(style="List Number")
                    parse_node(child, paragraph)
        elif node.name == "li":
            # For sub-lists inside li, or text directly
            for child in children:
                parse_node(child, parent_paragraph)
        elif node.name in ["blockquote", "cite"]:
            quote_paragraph = doc.add_paragraph()
            quote_paragraph.style = "Intense Quote"
            for child in children:
                parse_node(child, quote_paragraph)
        elif node.name in ["pre", "code"]:
            # We'll treat code as a separate paragraph with a monospace style
            code_paragraph = doc.add_paragraph()
            code_paragraph.style = "No Spacing"
            run = code_paragraph.add_run()
            run.font.name = "Arial"
            for child in children:
                if child.name is None:
                    run.add_text(child)
                else:
                    parse_node(child, code_paragraph)
        elif node.name == "hr":
            doc.add_page_break()
        else:
            for child in children:
                parse_node(child, parent_paragraph)

    from bs4 import BeautifulSoup

    soup = BeautifulSoup(html_content, "html.parser")
    for element in soup.children:
        parse_node(element)


def sanitize_html(html_string: str) -> str:
    soup = BeautifulSoup(html_string, "html.parser")

    for li in soup.find_all("li"):
        # Filter out whitespace or newline-only nodes
        actual_children = [
            c for c in li.children if c.name or (isinstance(c, str) and c.strip())
        ]

        if len(actual_children) == 1 and actual_children[0].name == "p":
            actual_children[0].unwrap()

    return str(soup)


def convert_report_to_pdf(
    report_data: Dict[str, str], title: Optional[str] = None
) -> bytes:
    pdf = MyFPDF()
    pdf.add_page()

    pdf.add_font(PDF_FONT, "", PDF_FONT_PATH, uni=True)
    pdf.add_font(PDF_FONT, "B", PDF_FONT_PATH, uni=True)
    pdf.add_font(PDF_FONT, "I", PDF_FONT_PATH, uni=True)
    pdf.add_font(PDF_FONT, "BI", PDF_FONT_PATH, uni=True)

    pdf.set_auto_page_break(auto=True, margin=PDF_MARGIN)
    pdf.set_margins(LEFT_MARGIN, TOP_MARGIN, PDF_MARGIN)

    try:
        svg_data = open(LOGO_REVERSE_PATH).read()
        png_data = cairosvg.svg2png(bytestring=svg_data)
        logo_stream = BytesIO(png_data)
    except requests.RequestException as e:
        raise RuntimeError(f"Failed to download logo: {e}")
    except cairosvg.CairoSVGError as e:
        raise RuntimeError(f"Failed to convert SVG to PNG: {e}")

    logo_width_mm = pixels_to_mm(LOGO_WIDTH)
    logo_height_mm = pixels_to_mm(LOGO_HEIGHT)

    header_height = logo_height_mm + TOP_MARGIN * 2
    pdf.set_fill_color(*hex_to_rgb(HEADER_COLOR))
    pdf.rect(0, 0, pdf.w, header_height, "F")

    import tempfile

    logo_stream.seek(0)
    try:
        with tempfile.NamedTemporaryFile(suffix=".png", delete=True) as temp_file:
            temp_file.write(logo_stream.read())
            temp_file.flush()
            pdf.image(
                temp_file.name,
                x=LEFT_MARGIN,
                y=TOP_MARGIN,
                w=logo_width_mm,
                h=logo_height_mm,
            )
    except Exception as e:
        raise RuntimeError(f"Failed to add image to PDF: {e}")

    pdf.set_y(header_height + TOP_MARGIN)

    if title:
        pdf.set_font(PDF_FONT, "B", PDF_TITLE_FONT_SIZE)
        pdf.ln(10)
        pdf.cell(0, 10, title, ln=True, align="C")
        pdf.ln(10)

    for section_title, section_content in report_data.items():
        html_content = markdown.markdown(section_content)
        pdf.set_font(PDF_FONT, "B", PDF_SECTION_FONT_SIZE)
        pdf.cell(0, 10, section_title, ln=True)
        pdf.set_font(PDF_FONT, "", PDF_CONTENT_FONT_SIZE)
        pdf.write_html(html_content)
        pdf.ln(5)

    try:
        pdf_bytes = pdf.output(dest="S")
    except Exception as e:
        raise RuntimeError(f"Failed to generate PDF: {e}")

    return pdf_bytes


def convert_report_to_docx(
    report_data: Dict[str, str], title: Optional[str] = None
) -> bytes:
    doc = Document()

    # Access the header
    section = doc.sections[0]
    header = section.header

    # Create a table in the header
    table = header.add_table(rows=1, cols=1, width=Inches(5))
    table.alignment = WD_TABLE_ALIGNMENT.LEFT

    # Set table cell width
    table_cell = table.cell(0, 0)
    table.columns[0].width = (
        section.page_width - section.left_margin - section.right_margin
    )

    # Apply background color to the cell
    shading_elm = OxmlElement("w:shd")
    shading_elm.set(qn("w:val"), "clear")
    shading_elm.set(qn("w:color"), "auto")
    shading_elm.set(qn("w:fill"), HEADER_COLOR.lstrip("#"))  # Set header color here
    table_cell._element.get_or_add_tcPr().append(shading_elm)

    # Download and convert the logo to PNG
    try:
        svg_data = open(LOGO_COLOR_PATH).read()
        png_data = cairosvg.svg2png(bytestring=svg_data)
        logo_stream = BytesIO(png_data)
    except requests.RequestException as e:
        raise RuntimeError(f"Failed to download logo: {e}")
    except cairosvg.CairoSVGError as e:
        raise RuntimeError(f"Failed to convert SVG to PNG: {e}")

    # Insert the logo with padding
    paragraph = table_cell.paragraphs[0]
    paragraph_format = paragraph.paragraph_format
    paragraph_format.space_before = Pt(10)  # Top margin
    paragraph_format.space_after = Pt(10)  # Bottom margin
    run = paragraph.add_run()
    try:
        run.add_picture(
            logo_stream, width=Inches(LOGO_WIDTH / 96)
        )  # Convert pixels to inches (assuming 96 DPI)
    except Exception as e:
        raise RuntimeError(f"Failed to add picture to DOCX: {e}")

    # Content
    # If title is provided, add it as H1 (Heading level 1)
    if title:
        doc.add_heading(title, level=DOCX_TITLE_HEADING_LEVEL)

    # Section headings as H2 (Heading level 2)
    for section_title, section_content in report_data.items():
        doc.add_heading(section_title, level=DOCX_SECTION_HEADING_LEVEL)
        # Convert markdown content to HTML
        html_content = markdown.markdown(
            section_content,
            extensions=[
                "sane_lists",
                "extra",
            ],
            output_format="html5",
        )
        add_html_to_docx(doc, html_content)

    try:
        doc_bytes = _doc_to_bytes(doc)
    except Exception as e:
        raise RuntimeError(f"Failed to generate DOCX: {e}")

    return doc_bytes


def _doc_to_bytes(doc: Document) -> bytes:
    file_stream = BytesIO()
    doc.save(file_stream)
    file_stream.seek(0)
    return file_stream.read()
