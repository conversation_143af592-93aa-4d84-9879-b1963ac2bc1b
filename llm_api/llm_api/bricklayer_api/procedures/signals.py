import django.dispatch

# Initial procedure created
dynamic_procedure_init = django.dispatch.Signal()

# Gathering tools
dynamic_procedure_tool_gathering = django.dispatch.Signal()

# Tool breakdown
dynamic_procedure_tool_breakdown = django.dispatch.Signal()

# Gathering agents
dynamic_procedure_coordinator_gathering = django.dispatch.Signal()

# Planning
dynamic_procedure_plan_start = django.dispatch.Signal()

# Parsing query or process_doc
dynamic_procedure_requirement_parsing = django.dispatch.Signal()

# Start
dynamic_procedure_plan_init = django.dispatch.Signal()

# Each iteration
dynamic_procedure_plan_review = django.dispatch.Signal()

# Validation
dynamic_procedure_plan_validate = django.dispatch.Signal()

# Feedback summary
dynamic_procedure_plan_judge = django.dispatch.Signal()

# Improvement
dynamic_procedure_plan_improve = django.dispatch.Signal()

# Stop condition / Final pass
dynamic_procedure_plan_complete = django.dispatch.Signal()

# Initializing Tasks
dynamic_procedure_task_creation_start = django.dispatch.Signal()

# Adding Tasks
dynamic_procedure_task_add = django.dispatch.Signal()

# Assigning procedure to agent
dynamic_procedure_specialist_assignment = django.dispatch.Signal()
