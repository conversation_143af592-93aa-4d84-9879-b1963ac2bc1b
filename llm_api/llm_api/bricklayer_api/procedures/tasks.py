import logging

from celery import shared_task
from procedures import dynamic_procedures
from procedures.models import DynamicProcedureCreationJob

logger = logging.getLogger(__name__)


@shared_task
def build_dynamic_procedure(job_id: int, organization_id: str):
    job = DynamicProcedureCreationJob.objects.get(pk=job_id)
    dynamic_procedures.create_procedure(
        organization_id,
        procedure_creation_job=job,
        query=job.process_description,
    )
