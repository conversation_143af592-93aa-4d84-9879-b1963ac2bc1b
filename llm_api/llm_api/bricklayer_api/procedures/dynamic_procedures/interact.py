import logging

from langchain_core.messages import HumanMessage, SystemMessage

from llm_api.llm.factory import (
    default_4_gpt_spec_data_json_enabled,
    get_model_from_spec,
)
from llm_api.specs.llm_spec import LLMSpec, LLMType

logger = logging.getLogger(__name__)


def send_ai_message(system, human) -> str:
    logger.info("send_ai_message[SYSTEM]: %s", system)
    logger.info("send_ai_message[HUMAN]: %s", human)

    llm = get_model_from_spec(
        spec=LLMSpec(
            type=LLMType.AzureChatOpenAI,
            data=default_4_gpt_spec_data_json_enabled,
        )
    )
    ai_response: str = llm.invoke(
        [SystemMessage(content=system), HumanMessage(content=human)]
    ).content

    logger.info("send_ai_message[RESPONSE]: %s", ai_response)

    return ai_response
