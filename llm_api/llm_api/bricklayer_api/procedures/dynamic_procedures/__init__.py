import json
import logging

from core.constants import UI_SPEC_PROCEDURE_WRAPPER_TOOL, LLMType, ToolType
from core.models import Procedure, Tool
from procedures import signals as procedure_signals
from procedures.dynamic_procedures.interact import send_ai_message
from procedures.dynamic_procedures.plan import plan_procedure
from procedures.dynamic_procedures.prompts import create_procedure_details_prompt
from procedures.dynamic_procedures.utility_functions import (
    add_all_tasks,
    assign_procedure_to_specialist,
    correct_json,
    create_all_tasks,
    solve_custom_report_if_any,
)
from procedures.models import DynamicProcedureCreationJob

logger = logging.getLogger(__name__)


def create_procedure_details(organization_id: str, query: str) -> Procedure:
    prompt = create_procedure_details_prompt()

    raw_response = send_ai_message(prompt, query)
    logger.info(raw_response)
    try:
        data = json.loads(raw_response)
    except Exception as e:
        data = correct_json(raw_response, e)

    logger.info("Procedure details data [%s]: %s", type(data), data)
    inputs = data.pop("inputs")
    procedure = Procedure.objects.create(
        **data,
        inputs=inputs,
        long_term_memory=False,
        organization_id=organization_id,
    )
    Tool.objects.create(
        id=procedure.procedure_tool_id,
        display_name=procedure.name,
        display_description=f"Service that runs procedure {procedure.name}",
        type=ToolType.SERVICE,
        llm_type=LLMType.PROCEDURE,
        configured_by=organization_id,
        spec=json.dumps(
            {
                "procedureId": procedure.pk,
                "inputs": procedure.inputs,
                "procedureOverview": procedure.llm_description,
                "procedureName": procedure.name,
            }
        ),
        configurable_spec_fields=UI_SPEC_PROCEDURE_WRAPPER_TOOL,
        img_url="",
        disabled_in_organizations={},
    )

    return procedure


def create_procedure(
    organization_id: str,
    procedure_creation_job: DynamicProcedureCreationJob,
    query="Create a procedure for alert triage. You will recieve a raw json alert as input and the process must generate a report on its triage.",
) -> int:
    # initializing
    procedure: Procedure = create_procedure_details(organization_id, query)
    procedure_creation_job.created_procedure = procedure
    procedure_creation_job.save()

    procedure_signals.dynamic_procedure_init.send(
        sender=procedure_creation_job.__class__,
        procedure_creation_job=procedure_creation_job,
        procedure_id=procedure.pk,
    )

    procedure_signals.dynamic_procedure_tool_gathering.send(
        sender=procedure_creation_job.__class__,
        procedure_creation_job=procedure_creation_job,
    )

    enabled_tools = (
        Tool.objects.exclude(disabled_in_organizations__contains=organization_id)
        .exclude(
            llm_type=LLMType.PROCEDURE,
        )
        .exclude(
            type=ToolType.COORDINATOR,
        )
        .filter(
            configured_by=organization_id,
        )
    )

    procedure_signals.dynamic_procedure_tool_breakdown.send(
        sender=procedure_creation_job.__class__,
        procedure_creation_job=procedure_creation_job,
        tools=enabled_tools,
    )

    enabled_specialists = (
        Tool.objects.coordinators()
        .exclude(disabled_in_organizations__contains=organization_id)
        .filter(
            configured_by__in=("public", organization_id),
        )
    )

    procedure_signals.dynamic_procedure_coordinator_gathering.send(
        sender=procedure_creation_job.__class__,
        procedure_creation_job=procedure_creation_job,
        coordinators=enabled_specialists,
    )

    # planning

    procedure_signals.dynamic_procedure_plan_start.send(
        sender=procedure_creation_job.__class__,
        procedure_creation_job=procedure_creation_job,
    )

    plan = plan_procedure(
        query,
        procedure,
        enabled_specialists,
        enabled_tools,
        procedure_creation_job,
    )

    procedure_signals.dynamic_procedure_plan_complete.send(
        sender=procedure_creation_job.__class__,
        procedure_creation_job=procedure_creation_job,
    )

    if isinstance(plan, str):
        try:
            plan = json.loads(plan)
        except Exception as e:
            plan = correct_json(plan, e)

    plan = solve_custom_report_if_any(organization_id, query, plan)

    logger.info("-----tasks below------")

    # retrieving tools again in case the new customreport tool was created
    enabled_tools = (
        Tool.objects.exclude(disabled_in_organizations__contains=organization_id)
        .exclude(llm_type=LLMType.PROCEDURE)
        .exclude(type=ToolType.COORDINATOR)
        .filter(
            configured_by__in=("public", organization_id),
        )
    )

    # creating tasks
    procedure_signals.dynamic_procedure_task_creation_start.send(
        sender=procedure_creation_job.__class__,
        procedure_creation_job=procedure_creation_job,
    )

    logger.info(
        "Creating tasks with inputs [%s]: %s", type(procedure.inputs), procedure.inputs
    )
    tasks = create_all_tasks(plan, enabled_specialists, enabled_tools, procedure.inputs)

    # adding tasks
    add_all_tasks(tasks, procedure.pk, procedure_creation_job)

    # assigning procedure to specialist
    chosen_specialist: Tool = assign_procedure_to_specialist(
        procedure, enabled_specialists
    )

    procedure_signals.dynamic_procedure_specialist_assignment.send(
        sender=procedure_creation_job.__class__,
        procedure_creation_job=procedure_creation_job,
        chosen_specialist=chosen_specialist,
    )

    return procedure.pk
