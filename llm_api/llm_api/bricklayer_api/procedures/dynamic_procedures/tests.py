import json
import logging

from core.models import Tool
from django.db.models import QuerySet

logger = logging.getLogger(__name__)


def test_required_fields(plan):
    required_fields = [
        "Sr No",
        "ActionType",
        "Instruction",
        "Role",
        "Tool",
        "Dependant task",
        "Description",
    ]
    errors = []
    for task in plan:
        sr_no = task.get("Sr No", "UNKNOWN")
        for field in required_fields:
            if field not in task:
                errors.append(f"Missing field '{field}' in task {sr_no}")
    return errors


def test_action_types(plan):
    valid_action_types = {"AnalyticalTask", "InformationLookup", "Plugin", "Report"}
    return [
        f"Invalid ActionType in task {task['Sr No']}: {task.get('ActionType')}"
        for task in plan
        if "ActionType" in task and task.get("ActionType") not in valid_action_types
    ]


def test_tool_logic(plan):
    errors = []
    for task in plan:

        if not all(k in task for k in ("Sr No", "ActionType", "Tool")):
            continue  # skip incomplete task

        sr_no = task["Sr No"]
        action = task.get("ActionType")
        tool = task.get("Tool")
        if action == "AnalyticalTask" and tool != "None":
            errors.append(f"AnalyticalTask in task {sr_no} should not use a tool")
        if action in {"Plugin", "InformationLookup", "Report"} and tool == "None":
            errors.append(f"{action} in task {sr_no} must specify a tool")
    return errors


def test_markdown_in_description(plan):
    errors = []
    markdown_symbols = ["**", "__", "_", "`"]
    for task in plan:
        desc = task.get("Description", "")
        sr_no = task.get("Sr No", "UNKNOWN")
        if any(sym in desc for sym in markdown_symbols):
            errors.append(f"Task {sr_no} description contains markdown formatting")
    return errors


def test_sr_no_mentions(plan, all_sr_nos):
    errors = []
    sr_no_strings = {str(n) for n in all_sr_nos}
    for task in plan:
        sr_no = task.get("Sr No", "UNKNOWN")
        instruction = task.get("Instruction", "")
        if any(num in instruction for num in sr_no_strings):
            errors.append(
                f"Task {sr_no} instruction mentions prior task Sr No directly (it is unaware of their existence and only knows CONTEXT. Refer to the tasks by referring to the CONTEXT below instead"
            )
    return errors


def test_report_task_rules(plan, sr_nos):
    report_tasks = [task for task in plan if task.get("ActionType", "") == "Report"]
    errors = []
    if len(report_tasks) > 1:
        errors.append("There should be only one Report task")
    elif report_tasks:
        final_task = max(sr_nos)
        report_task_sr = report_tasks[0].get("Sr No", "UNKNOWN")
        if report_task_sr != final_task:
            errors.append(f"Report task {report_task_sr} must be the final task")
    return errors


def test_dependency_references(plan, sr_nos):
    errors = []
    for task in plan:
        sr_no = task.get("Sr No", "UNKNOWN")
        deps = [
            x.strip() for x in task.get("Dependant task", "").split(",") if x.strip()
        ]
        for d in deps:
            if not d.isdigit() or int(d) not in sr_nos:
                errors.append(f"Task {sr_no} has invalid dependant task reference: {d}")
    return errors


def test_roles_are_valid(plan, specialists: QuerySet[Tool]):
    specialist_list = [
        s.display_name for s in specialists if s.configured_by == "public"
    ] + [
        s.display_name
        for s in specialists
        if s.configured_by != "public"  # implicitly of this org
    ]

    errors = []
    for task in plan:
        role = task.get("Role", "UNKNOWN")
        if role not in specialist_list:
            errors.append(f"Invalid Role '{role}' in task {task['Sr No']}")
    return errors


def test_tools_are_valid(plan, tools: QuerySet[Tool]):
    valid_tools = {tool.display_name for tool in tools}
    valid_tools.add("CustomReport")  # allow 'CustomReport' as special case
    valid_tools.add("None")  # allow 'None' as special case

    errors = []
    for task in plan:
        tools = task.get("Tool", [])
        if isinstance(tools, str):
            tools = [tools]
        for tool in tools:
            if tool not in valid_tools:
                errors.append(
                    f"Invalid Tool '{tool}' in task {task.get('Sr No','UNKNOWN')}"
                )
    return errors


def test_duplicate_sr_nos(plan):
    errors = []
    seen = set()
    for task in plan:
        sr_no = task.get("Sr No", "UNKNOWN")
        if sr_no in seen:
            errors.append(f"Duplicate Sr No: {sr_no}")
        seen.add(sr_no)
    return errors


def validate_plan_structure(plan, specialists: QuerySet[Tool], tools: QuerySet[Tool]):
    """
    Validates a Bricklayer task plan (list or JSON string) with structural and value checks.
    """

    # Parse if it's a string
    if isinstance(plan, str):
        try:
            plan = json.loads(plan)
        except json.JSONDecodeError as e:
            return {"is_valid": False, "errors": [f"Invalid JSON: {e}"], "warnings": []}

    logger.info("Plan: %s", plan)

    # Rest of the validation as before...
    errors = []
    warnings = []
    sr_nos = set()

    sr_nos = {task.get("Sr No", "UNKNOWN") for task in plan}
    errors += test_duplicate_sr_nos(plan)

    # Run all modular checks
    errors += test_required_fields(plan)
    errors += test_action_types(plan)
    errors += test_tool_logic(plan)
    errors += test_markdown_in_description(plan)
    errors += test_sr_no_mentions(plan, sr_nos)
    errors += test_report_task_rules(plan, sr_nos)
    errors += test_dependency_references(plan, sr_nos)
    errors += test_roles_are_valid(plan, specialists)
    errors += test_tools_are_valid(plan, tools)

    return {"is_valid": len(errors) == 0, "errors": errors, "warnings": warnings}
