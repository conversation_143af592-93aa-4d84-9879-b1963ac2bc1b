from core.managers import ProcedureQuerySet
from core.models import Procedure


def create_procedure_details_prompt() -> str:
    return """
    Write a JSON object with the following format:

    FORMAT
    -------

    {
        "name": "<a relevant name for the procedure>",
        "ui_description": "<a short description of the procedure and what it is for - It should contain NO MARKDOWN>",
        "llm_description": "<a simple description of what the procedure is useful for - usually starts with \'Useful for ...\'>",
        "inputs": [
            {
                "Name": "<name of first relevant input if any ...>",
                "Description": "<description of first relevant input if any ...>"
            }
            // Add more inputs here if needed
        ]
    }

    FORMAT ENDS

    Ensure that your response is plain text and NO FORMATTERS such as *bold* or _italics_ are used. ENSURE that Your response MUST START with the { and MUST END with the } otherwise you will cause downstream errors
    """


def create_initial_plan_creation_prompt(
    procedure: Procedure,
    specialist_list: list[str],
    datastores: list[str],
    plugins: list[str],
    reports: list[str],
    example_procedures: ProcedureQuerySet | list[Procedure],
    example_procedure_instructions: list[list[dict]],
) -> str:
    example_procedures_str = "EXAMPLES\n________\n\n"
    for i in range(len(example_procedures)):
        example_procedure: Procedure = example_procedures[i]
        example_instructions = example_procedure_instructions[i]
        steps = "STEPS\n"
        for instruction in example_instructions:
            steps += f"Role: {instruction['Role']}\nInstruction: {instruction['Instruction']}\n"
        example_procedures_str += (
            f"{example_procedure.name} : {example_procedure.ui_description}\n{steps}"
        )

    example_procedures_prompt = (
        f"Here are some examples of what good instructions in a plan look like \n{example_procedures_str}"
        if len(example_procedures) > 0
        else ""
    )

    instructions = f"""
    You must reply with a detailed plan. This plan will be used to instruct your cybersecurity team members in a step by step manner. 
    Like any good planner, you must break the plan into tasks that need to be carried out. Before planning you must consider the assumptions listed below as the core truths of your world.
    Thus, the plan you create must adhere to these assumptions. 

    ASSUMPTIONS
    -----------

    1. You have access to the information list below ONLY. You MAY NOT ask for any other information outside of it.

        {procedure.inputs}

    2. Your employees can only perform 4 types of actions. These are:
        - AnalyticalTask: These are thinking based tasks where the team member follows your instructions and gets back to you with a reply without using ANY tool. Instructions are typically to summarize something, reach some determination or conclusion, etc.
        - InformationLookup: This is when employees use a source of information such as cybersecurity blogs or organization documents to answer the question using a tool.
        - Plugin: This is when employees use an external tool to either retrieve some information or perform some action.
        - Report: This is when employees use a report writing tool to create a report.

    3. Your employees only have access to the information provided in your instructions and the outcomes of the tasks their task was dependant on as a part of their tasks CONTEXT.

    4. The roles on your team are: {specialist_list}

    5. The tools your team members have access to are: 
        - For InformationLookup: {datastores}
        - Plugins: {plugins}
        - Reports: {reports}

    RULES
    -----

    Each step must contain the following information:

    Sr No: <A serial number for the task>
    ActionType: <Which of the 4 action types is this task?>
    Instruction: <The precise instruction I will provide to my team member for the task via our messaging app. Embed any of the inputs I need to share in the instruction itself as "{{name of the input}}" with the CURLY BRACES enclosing the input name as if it were a variable. The CURLY BRACES are IMPORTANT>
    Role: <Which member of my team should perform this task>
    Tool: <Should the team member use any tool to perform this task. If yes, state the tool here. If no, state None>
    Dependant task: <The serial numbers of the tasks this task depends on being completed before it is performed. List ALL the individual tasks that are blockers. Leave it empty if it can be performed independantly in parallel>
    Description: <A high level description of the task in a single concise sentence or phrase. Description must be plain text and contain NO MARKDOWN ELEMENTS>

    {example_procedures_prompt}

    The TASKS do NOT need to be carried out sequentially as they are independant units and could potentially be worked on in parallel. 
    Multiple tasks can use the same tool if required.
    Your response must be a plan object containing the list of tasks only as JSON objects with the relevant fields.

    FORMAT

    {{"plan": [{{"Sr No": 1, "ActionType": "", "Instruction": "", "Role": "", "Tool": "", "Dependant task": "", "Description": ""}}]}}
    """
    # Your response MUST be a LIST of the TASKS ONLY as JSONs with the relevant fields.
    return instructions


def create_judge_plan_prompt(
    procedure: Procedure,
    specialist_list: list[dict],
    datastores: list[str],
    plugins: list[dict],
    reports: list[str],
    example_procedures: ProcedureQuerySet | list[Procedure],
    example_procedure_instructions: list[list[dict]],
) -> str:
    example_procedures_str = "EXAMPLES\n________\n\n"
    for i in range(len(example_procedures)):
        example_procedure: Procedure = example_procedures[i]
        example_instructions = example_procedure_instructions[i]
        steps = "STEPS\n"
        for j in example_instructions:
            steps += f"Role: {j['Role']}\nInstruction: {j['Instruction']}\n"
        example_procedures_str += (
            f"{example_procedure.name} : {example_procedure.ui_description}\n{steps}"
        )

    example_procedures_prompt = (
        f"""
        Here are some examples of what good instructions in a plan look like. Use them as a benchmark to judge the goodness of the submitted plan.
        {example_procedures_str}
        """
        if len(example_procedures) > 0
        else ""
    )

    critique_prompt = f"""
    You are an expert cybersecurity task planner. Your job is to critically evaluate the quality of a proposed cybersecurity plan that will be executed by a team using the given resources.

    The roles on the team are: {specialist_list}

    The tools the team members have access to are: 
    - For InformationLookup: {datastores}
    - Plugins: {plugins}
    - Reports: {reports}

    The employees can only perform 4 types of actions. These are:
        - AnalyticalTask: These are thinking based tasks where the team member follows your instructions and gets back to you with a reply without using ANY tool. Instructions are typically to summarize something, reach some determination or conclusion, etc.
        - InformationLookup: This is when employees use a source of information such as cybersecurity blogs or organization documents to answer the question using a tool.
        - Plugin: This is when employees use an external tool to either retrieve some information or perform some action.
        - Report: This is when employees use a report writing tool to create a report.

    This is the goal of the plan:

    {procedure.ui_description}

    Please assess the plan according to the following criteria:

    1. ENSURE any reporting task MUST contain ALL the relevant prior tasks as dependant tasks.
    2. Are the instructions written in a way that can be directly understood and followed by the assigned specialist?
    3. Are the tools used appropriate for the type of action?
    4. Is the plan extremely thorough? Does it look up indicators of compromise across multiple sources? Does it use other relevant tools?
    5. Are any important tasks missing that should be included?
    6. Are any instructions too complex and would benefit from being broken down into smaller subtasks?
    7. Are the tasks clear, well-defined and assigned to the right role?
    8. Tasks should be broken into multiple if they involve multiple actions. For example if a task requires looking up an IOC in VirusTotal and then triaging based on it, it should be broken into 2 tasks: the 1st for the IOC Look Up and 2nd to analyse the information retrieved.
    9. If a tool is to be used in different ways, it should be broken into different tasks. For example if I want to use Blogs to look up information on a threat actor - it should be broken into multiple tasks: 1st could ask the employee to tell us about the TTPs the threat actor uses, 2nd could be asking the employee what do we know about the threat actor in general, 3rd could ask about what industries they typically target, etc.
    10. Task serial numbers must NEVER be mentioned in the instructions. They should ONLY be referred to as CONTEXT. For example. If the instruction is to analyze the findings from task 1,2 and 3, the instruction must say analyze the findings from the CONTEXT below.

    {example_procedures_prompt}

    Your output must be a JSON object in the following format:
    {{
        "feedback": "<A newline chracter separated detailed list of critical analysis>",
        "score": <an integer between 0 and 10 - typically 1 - 3 if any formatting issues or role/tool assignment issues, 4 - 6 if tasks or instructions could be broken down into multiple, 7 - 10 looks good. BE VERY CRITICAL>,
        "pass": <true if the score is >= 9, otherwise false>
    }}

    Be honest, constructive, and critical. If you think it needs improvement, say so clearly.
    """
    return critique_prompt


def create_improve_plan_system_prompt(
    procedure: Procedure,
    specialist_list: list[str],
    datastores: list[str],
    plugins: list[str],
    reports: list[str],
    example_procedures: ProcedureQuerySet | list[Procedure],
    example_procedure_instructions: list[list[dict]],
) -> str:
    example_procedures_str = "EXAMPLES\n________\n\n"
    for i in range(len(example_procedures)):
        example_procedure = example_procedures[i]
        example_instructions = example_procedure_instructions[i]
        steps = "STEPS\n"
        for j in example_instructions:
            steps += f"Role: {j['Role']}\nInstruction: {j['Instruction']}\n"
        example_procedures_str += (
            f"{example_procedure.name} : {example_procedure.ui_description}\n{steps}"
        )

    example_procedures_prompt = (
        f"""
        Here are some examples of what good instructions in a plan look like
        {example_procedures_str}
        """
        if len(example_procedures) > 0
        else ""
    )

    prompt = f"""
    You are an expert at designing structured cybersecurity task plans for execution inside the Bricklayer platform.

    You are given:
    - The constraints you are working with
    - Your goal
    - A previous plan that needs improvement
    - Feedback and Errors that critique the quality of the plan. You MUST correct the errors and act on the feedback.

    You must rewrite the plan in JSON format, directly incorporating the feedback and improving the quality accordingly.

    CONSTRAINTS:
    The roles on the team are: {specialist_list}

    The tools the team members have access to are: 
    - For InformationLookup: {datastores}
    - Plugins: {plugins}
    - Reports: {reports}

    The employees can only perform 4 types of actions. These are:
        - AnalyticalTask: These are thinking based tasks where the team member follows your instructions and gets back to you with a reply without using ANY tool. Instructions are typically to summarize something, reach some determination or conclusion, etc.
        - InformationLookup: This is when employees use a source of information such as cybersecurity blogs or organization documents to answer the question using a tool.
        - Plugin: This is when employees use an external tool to either retrieve some information or perform some action.
        - Report: This is when employees use a report writing tool to create a report.

    GOAL:
    {procedure.ui_description}

    INSTRUCTIONS:
    - Fix any issues mentioned in the feedback.
    - Add missing tasks if necessary.
    - Split or simplify any instructions that are too complex.
    - Ensure each task uses an appropriate ActionType, Role, and Tool.
    - Use clean, clear, and concise instructions.
    - Do NOT include any extra explanation, headers, or commentary — just return the updated plan as a JSON array.

    {example_procedures_prompt}

    Your response must be a plan object containing the list of tasks only as JSON objects with the relevant fields.

    FORMAT

    {{"plan": [{{"Sr No": 1, "ActionType": "", "Instruction": "", "Role": "", "Tool": "", "Dependant task": "", "Description": ""}}]}}
    """
    # Begin your response with "[" and end with "]". Your output MUST be a valid JSON string that can be parsed with a JSON.loads().
    return prompt


def create_improve_plan_human_prompt(plan_str, feedback, errors, process_text) -> str:
    return f"""

    PREVIOUS PLAN:
    {plan_str}

    HARD ERRORS:
    {errors}


    FEEDBACK:
    {feedback}

    SUGGESTED PROCESS:
    {process_text}

    Please return an IMPROVED PLAN.
    """


def create_report_system_prompt() -> str:
    return """
    You are an expert report creator. You make use of a report creation tool that expects a valid JSON input. For this purpose generate a JSON in the format given below.
    The JSON must contain a display_name, display_description, spec consisting of a title and a list of sections the report should have.

    Your input is the process that is to be created that the report will be used for.

    FORMAT
    ______

    {
    "display_name": "<name of the report to be displayed in the UI>",
    "display_description": "<a description of the report to be displayed in the UI>",
    "spec": {
            "sections": [
            {
                "section_name": "<name of section 1>",
                "section_description": "<a description of the contents expected in section 1>"
            },
            {
                "section_name": "<name of section 2>",
                "section_description": "<a description of the contents expected in section 2>"
            },
            {
                "section_name": "<name of section 3>",
                "section_description": "<a description of the contents expected in section 3>"
            }, 
            ...
            ],
            "title": "title"
        }
    }

    Your output MUST be a valid JSON string that can be parsed with a JSON.loads()
    """


def create_correct_json_system_prompt() -> str:
    return """
    Your SOLE responsibility is to receive invalid JSON Strings along with the exceptions they raised and to reply with a valid JSON string version of them.
    Your output MUST be a valid JSON string that can be directly parsed with a JSON.loads()!!!
    """


def create_correct_json_human_prompt(json, error) -> str:
    return f"""
    The invalid JSON is quoted below:

    "{json}"

    This raises the following exception:

    "{error}"

    """
