import json
import logging

from core.constants import ToolType
from core.managers import ProcedureQuerySet
from core.models import Procedure, Tool
from django.db.models import QuerySet
from procedures import signals as procedure_signals
from procedures.dynamic_procedures.interact import send_ai_message
from procedures.dynamic_procedures.prompts import (
    create_improve_plan_human_prompt,
    create_improve_plan_system_prompt,
    create_initial_plan_creation_prompt,
    create_judge_plan_prompt,
)
from procedures.dynamic_procedures.tests import validate_plan_structure
from procedures.dynamic_procedures.utility_functions import (
    correct_json,
    create_procedure_examples,
    strip_json_fence,
)
from procedures.models import DynamicProcedureCreationJob

logger = logging.getLogger(__name__)


def create_initial_plan(
    query: str,
    procedure: Procedure,
    specialists: QuerySet[Tool],
    tools: QuerySet[Tool],
    process_text: str,
    example_procedure_instructions: list[list[dict]],
    example_procedures: ProcedureQuerySet | list[Procedure],
) -> str:
    """
    Creates initial plan draft
    """

    public_plugin_names: list[str] = [
        tool.display_name
        for tool in tools
        if tool.type == ToolType.PLUGIN and tool.configured_by == "public"
    ]
    configured_plugin_names: list[str] = [
        tool.display_name
        for tool in tools
        if tool.type == ToolType.PLUGIN
        and tool.configured_by == procedure.organization_id
    ]
    public_datasource_names: list[str] = [
        tool.display_name
        for tool in tools
        if tool.type == ToolType.PUBLIC_DATA_SOURCE and tool.configured_by == "public"
    ]
    configured_datastore_names: list[str] = [
        tool.display_name
        for tool in tools
        if tool.type == ToolType.DATASTORE
        and tool.configured_by == procedure.organization_id
    ]
    public_reporter_names: list[str] = [
        tool.display_name
        for tool in tools
        if tool.type == ToolType.REPORTER and tool.configured_by == "public"
    ]
    configured_reporter_names: list[str] = [
        tool.display_name
        for tool in tools
        if tool.type == ToolType.REPORTER
        and tool.configured_by == procedure.organization_id
    ]
    custom_report = "CustomReport"

    specialist_list = [specialist.display_name for specialist in specialists]
    instructions = create_initial_plan_creation_prompt(
        procedure,
        specialist_list,
        datastores=public_datasource_names + configured_datastore_names,
        plugins=public_plugin_names + configured_plugin_names,
        reports=public_reporter_names + configured_reporter_names + [custom_report],
        example_procedures=example_procedures,
        example_procedure_instructions=example_procedure_instructions,
    )
    if process_text:
        response = send_ai_message(
            instructions, query + "Playbook\n__________\n" + process_text
        )
    else:
        response = send_ai_message(instructions, query)
    return json.dumps(json.loads(response)["plan"])


def judge_plan(
    plan,
    procedure: Procedure,
    specialists: QuerySet[Tool],
    tools: QuerySet[Tool],
    process_text: str,
    example_procedure_instructions: list[list[dict]],
    example_procedures: ProcedureQuerySet | list[Procedure],
):
    """
    Critiques the plan for quality and completeness.
    Returns (feedback: str, score: int, pass_fail: bool)
    """
    plugins = [
        {tool.display_name: tool.display_description}
        for tool in tools
        if tool.type == ToolType.PLUGIN
    ]
    datastores = [
        tool.display_name
        for tool in tools
        if (tool.type == ToolType.PUBLIC_DATA_SOURCE and tool.configured_by == "public")
        or (tool.type == ToolType.DATASTORE and tool.configured_by != "public")
    ]
    reports = [
        tool.display_name for tool in tools if tool.type == ToolType.REPORTER
    ] + ["CustomReport"]
    specialist_list = [
        {specialist.display_name: specialist.display_description}
        for specialist in specialists
    ]

    if isinstance(plan, list):
        plan_str = json.dumps(plan, indent=2)
    else:
        plan_str = plan

    critique_prompt = create_judge_plan_prompt(
        procedure,
        specialist_list,
        datastores,
        plugins,
        reports,
        example_procedures,
        example_procedure_instructions,
    )

    raw_answer = send_ai_message(
        critique_prompt,
        f"""
        You are reviewing the following PLAN:
        {plan_str}

        Here is the playbook describing the process this plan should follow:

        {process_text}
        """,
    )
    try:
        logger.info("------FEEDBACK--------")
        logger.info(raw_answer)

        parsed = json.loads(raw_answer)
        return parsed["feedback"], parsed["score"], parsed["pass"]
    except Exception as e:
        parsed = correct_json(raw_answer, e)
        return parsed["feedback"], parsed["score"], parsed["pass"]


def improve_plan(
    plan,
    errors,
    feedback,
    procedure: Procedure,
    specialists: QuerySet[Tool],
    tools: QuerySet[Tool],
    process_text: str,
    example_procedure_instructions: list[list[dict]],
    example_procedures: ProcedureQuerySet | list[Procedure],
):
    """
    Uses LLM to improve a plan based on given feedback.

    Args:
        plan (str or list): The original plan (as a list of tasks or JSON string)
        feedback (str): The qualitative feedback from judge_plan()

    Returns:
        list: The improved plan as a list of tasks
    """

    plugins = [tool.display_name for tool in tools if tool.type == ToolType.PLUGIN]
    datastores = [
        tool.display_name
        for tool in tools
        if (tool.type == ToolType.PUBLIC_DATA_SOURCE and tool.configured_by == "public")
        or (tool.type == ToolType.DATASTORE and tool.configured_by != "public")
    ]
    reports = [
        tool.display_name for tool in tools if tool.type == ToolType.REPORTER
    ] + ["CustomReport"]
    specialist_list = [specialist.display_name for specialist in specialists]

    if isinstance(plan, list):
        plan_str = json.dumps(plan, indent=2)
    else:
        plan_str = plan

    system_prompt = create_improve_plan_system_prompt(
        procedure,
        specialist_list,
        datastores,
        plugins,
        reports,
        example_procedures,
        example_procedure_instructions,
    )
    human_prompt = create_improve_plan_human_prompt(
        plan_str, feedback, errors, process_text
    )
    raw_answer = send_ai_message(system_prompt, human_prompt)

    try:
        logger.info("------PLAN--------")
        logger.info(raw_answer)

        improved_plan = json.loads(raw_answer)["plan"]
        return improved_plan
    except Exception as e:
        raw_answer = strip_json_fence(raw_answer)
        try:
            improved_plan = json.loads(raw_answer)
            return improved_plan
        except Exception as e:
            improved_plan = correct_json(raw_answer, e)
            return improved_plan


def plan_procedure(
    query: str,
    procedure: Procedure,
    specialists: QuerySet[Tool],
    tools: QuerySet[Tool],
    procedure_creation_job: DynamicProcedureCreationJob,
):
    procedure_signals.dynamic_procedure_requirement_parsing.send(
        sender=procedure_creation_job.__class__,
        procedure_creation_job=procedure_creation_job,
    )

    process_text = procedure_creation_job.parsed_process_document or ""
    example_procedure_instructions, example_procedures = create_procedure_examples(
        procedure, query
    )

    procedure_signals.dynamic_procedure_plan_init.send(
        sender=procedure_creation_job.__class__,
        procedure_creation_job=procedure_creation_job,
    )

    plan = create_initial_plan(
        query,
        procedure,
        specialists,
        tools,
        process_text,
        example_procedure_instructions,
        example_procedures,
    )
    good_enough = False
    improvement_count = 0
    while not good_enough:
        procedure_signals.dynamic_procedure_plan_review.send(
            sender=procedure_creation_job.__class__,
            procedure_creation_job=procedure_creation_job,
        )

        errors = validate_plan_structure(plan, specialists, tools)

        procedure_signals.dynamic_procedure_plan_validate.send(
            sender=procedure_creation_job.__class__,
            procedure_creation_job=procedure_creation_job,
        )

        logger.info(errors)

        # feedback
        feedback = judge_plan(
            plan,
            procedure,
            specialists,
            tools,
            process_text,
            example_procedure_instructions,
            example_procedures,
        )

        procedure_signals.dynamic_procedure_plan_judge.send(
            sender=procedure_creation_job.__class__,
            procedure_creation_job=procedure_creation_job,
            score=feedback[1],
            passed=feedback[2],
        )

        good_enough = feedback[2]
        if good_enough:
            break
        else:
            # refining plan or incorporating feedback
            procedure_signals.dynamic_procedure_plan_improve.send(
                sender=procedure_creation_job.__class__,
                procedure_creation_job=procedure_creation_job,
            )

            plan = improve_plan(
                plan,
                errors,
                feedback,
                procedure,
                specialists,
                tools,
                process_text,
                example_procedure_instructions,
                example_procedures,
            )
            improvement_count += 1
        if improvement_count >= 3:
            break

    return plan
