import json
import logging
import re
from typing import Any

from core.constants import ToolType
from core.managers import ProcedureQuerySet
from core.models import CategoryTemplate, Procedure, Task, Tool, ToolTemplate
from django.db.models import Prefetch, QuerySet
from langchain_community.document_loaders.parsers import PyPDFParser
from langchain_core.documents.base import Blob
from procedures import signals as procedure_signals
from procedures.dynamic_procedures.interact import send_ai_message
from procedures.dynamic_procedures.prompts import (
    create_correct_json_human_prompt,
    create_correct_json_system_prompt,
    create_report_system_prompt,
)
from procedures.models import DynamicProcedureCreationJob

logger = logging.getLogger(__name__)

# JSON CLEANUP UTILITY FUNCTIONS


def strip_json_fence(raw):
    """
    Removes leading and trailing ```json or ``` markdown fencing from a JSON-like string.
    """
    return re.sub(r"^```json\s*|```$", "", raw.strip(), flags=re.MULTILINE)


def correct_json(incorrect_json, error):
    system = create_correct_json_system_prompt()
    human = create_correct_json_human_prompt(incorrect_json, error)
    corrected_json = send_ai_message(system, human)
    try:
        corrected_json = json.loads(corrected_json)
        return corrected_json
    except Exception as e:
        raise e


# ASSIGN PROCEDURE TO SPECIALIST UTILITY FUNCTIONS


def choose_specialist(
    procedure: Procedure, specialist_list: list[dict[str, str]]
) -> str:
    procedure_details = {
        "name": procedure.name,
        "ui_description": procedure.ui_description,
        "llm_description": procedure.llm_description,
        "inputs": procedure.inputs,
    }
    choice = json.loads(
        send_ai_message(
            f"""
        Pick the correct specialist to oversee the process given as input. Your options are:

        {specialist_list}

        Your reply MUST be a JSON object containing only the name of the correct specialist

        FORMAT

        {{"specialist": <Name of the chosen specialist>}}

        END FORMAT
        """,
            f"{procedure_details}",
        )
    )
    return choice["specialist"]


def assign_procedure_to_specialist(
    procedure: Procedure, specialists: QuerySet[Tool]
) -> Tool:
    chosen_specialist_name: str = choose_specialist(
        procedure,
        [
            {specialist.display_name: specialist.display_description}
            for specialist in specialists
            if specialist.display_name != "Reporter"
        ],
    )
    chosen_specialist: Tool | None = find_specialist_by_name(
        chosen_specialist_name, specialists
    )
    chosen_specialist.add_children(procedure.procedure_tool_id)

    return chosen_specialist


# CUSTOM REPORT CREATION UTILITY FUNCTIONS


def create_report_spec(goal) -> dict:
    spec_answer = send_ai_message(create_report_system_prompt(), goal)

    try:
        spec = json.loads(spec_answer)
    except Exception as e:
        spec = correct_json(spec_answer, e)

    logger.info(spec)
    return spec


def create_report(organization_id: str, goal: str) -> str:
    # NOTE: 2 is ID of the long report category template
    long_report_category_template = CategoryTemplate.objects.get(pk=2)
    payload = create_report_spec(goal)
    payload = {
        **payload,
        "spec": json.dumps(payload["spec"]),
        "img_url": long_report_category_template.img_url,
        "type": long_report_category_template.type,
        "llm_type": long_report_category_template.llm_type,
        "configurable_spec_fields": long_report_category_template.configurable_spec_fields,
        "configured_by": organization_id,
    }
    tool_template = ToolTemplate.objects.create(
        **{
            **payload,
            "configurable_spec_fields": json.dumps(
                long_report_category_template.configurable_spec_fields
            ),
        },
    )
    long_report_tool = Tool.objects.create(**payload, template=tool_template)

    return long_report_tool.display_name


# CUSTOM REPORT ADDITION TO DYNAMIC PROCEDURE UTILITY FUNCTIONS


def is_custom_report_required(plan):
    """
    Returns the index of the task that uses the tool 'CustomReport'. If not found, returns None.
    Args:    tasks (list): List of task dictionaries
    Returns: int or None: Index of the task (0-based) or None if not found
    """
    indices = []
    for i, task in enumerate(plan):
        tool = task.get("Tool")
        if isinstance(tool, str):
            if tool == "CustomReport":
                indices.append(i)
        elif isinstance(tool, list):
            if "CustomReport" in tool:
                indices.append(i)
    return indices


def replace_custom_report(name, taskNumbers, plan):
    for taskNumber in taskNumbers:
        tool = plan[taskNumber].get("Tool")
        if isinstance(tool, str):
            if tool == "CustomReport":
                plan[taskNumber]["Tool"] = name
        elif isinstance(tool, list):
            if "CustomReport" in tool:
                tool.remove("CustomReport")
                tool.append("CustomReport")
                plan[taskNumber]["Tool"] = tool
    return plan


def solve_custom_report_if_any(
    organization_id: str, goal: str, plan: dict | list
):  # TODO: Check plan type
    custom_report_task_ids = is_custom_report_required(plan)
    if custom_report_task_ids:
        name = create_report(organization_id, goal)
        plan = replace_custom_report(name, custom_report_task_ids, plan)

    return plan


# IMPORTANT EXAMPLE PROCEDURES SELECTION UTILITY FUNCTIONS


def select_top_3_procedures(
    goal: str, procedures: ProcedureQuerySet
) -> ProcedureQuerySet | list[Procedure]:
    """
    Selects the top (up to 3) most relevant procedures based on a given goal,
    by sending procedure metadata and the goal to an LLM.
    """

    procedure_map = {p.pk: p for p in procedures}

    if len(procedure_map) <= 3:
        return procedures

    system_prompt = """
    You are a cybersecurity assistant. Your task is to help select the most relevant procedures 
    for a specific goal. You will be given a list of available procedures (with ids, names, and descriptions) 
    and a user goal. You must pick the 3 most relevant procedures — the ones that most effectively help achieve the goal. 
    Output ONLY the top 3 procedure IDs in order of relevance as a JSON object with an `top3` key, like: {"top3": [245, 250, 292]}.
    """

    human_prompt = f"""
    The user's goal is:

    {goal}

    Here are the available procedures:

    {json.dumps(
        [
            {"id": p.pk, "name": p.name, "description": p.ui_description}
            for p in procedures if p.pk < 300
        ],
        indent=2
    )}

    Please return ONLY the top 3 procedure IDs (or fewer, if fewer are relevant) as a JSON object with a `top3` key, like: {{"top3": [245, 250, 292]}}
    """

    response = send_ai_message(system_prompt, human_prompt)
    try:
        top_ids = json.loads(response)["top3"]
    except Exception as e:
        top_ids = correct_json(response, e)["top3"]

    return [procedure_map[top_id] for top_id in top_ids if top_id in procedure_map]


# --- Prompt Extractor ---
def get_procedure_prompts_by_ui_order(
    procedures: ProcedureQuerySet | list[Procedure],
) -> list[list[dict]]:
    """
    Fetch procedures and return a mapping of {procedure_name: [prompt1, prompt2, ...]}
    """
    procedure_prompts = []

    for procedure in procedures:
        try:
            sorted_tasks = sorted(
                procedure.task_set.all(), key=lambda task: task.ui_order
            )
            prompts = []
            for task in sorted_tasks:
                specialists = [
                    tool.display_name
                    for tool in task.tools.all()
                    if tool.type == ToolType.COORDINATOR
                ]
                prompts.append({"Role": specialists, "Instruction": task.prompt})
            procedure_prompts.append(prompts)

        except Exception as e:
            logger.exception(
                f"⚠️ Failed to fetch or process procedure {procedure.pk}: {e}"
            )

    return procedure_prompts


def create_procedure_examples(
    procedure: Procedure, goal: str
) -> tuple[list[list[dict]], ProcedureQuerySet | list[Procedure]]:
    procedures = (
        Procedure.active_objects.prefetch_related(
            Prefetch("task_set", queryset=Task.active_objects.all()), "task_set__tools"
        )
        .filter(organization_id=procedure.organization_id)
        .exclude(pk=procedure.pk)
    )
    top_3_procedures = select_top_3_procedures(goal, procedures)

    return get_procedure_prompts_by_ui_order(top_3_procedures), top_3_procedures


# TASK CREATION AND ADDITION UTILITY FUNCTIONS


def find_tool_ids(tool_names_str, tool_lookup) -> list[str]:
    tool_ids = []
    if tool_names_str and tool_names_str.lower() != "none":
        names = [name.strip() for name in tool_names_str.split(",")]
        for name in names:
            tool_id = tool_lookup.get(name)
            if tool_id:
                tool_ids.append(tool_id)
    return tool_ids


def find_specialist_id_by_tools(
    tool_ids: list[str], specialists: QuerySet[Tool]
) -> Tool | None:
    for tool_id in tool_ids:
        for specialist in specialists:
            if any(child.pk == tool_id for child in specialist.children_tools):
                return specialist
    return None


def find_specialist_by_name(name: str, specialists: QuerySet[Tool]) -> Tool | None:
    for specialist in specialists:
        if name.lower() in specialist.display_name.lower():
            return specialist
    return None


def extract_and_replace_inputs(
    instruction: str | Any, known_inputs: list[dict]
) -> tuple[str, list[str]]:
    """
    Finds known input variables in an instruction that are wrapped in punctuation,
    replaces them with {inputName}, and returns both the updated instruction and
    the list of found input names.

    Args:
        instruction (str): The original instruction string.
        known_inputs (list): List of dicts with at least a 'name' field.

    Returns:
        (str, list): (updated_instruction, list_of_input_names)
    """
    input_names = [inp["Name"] for inp in known_inputs]
    found_inputs = set()

    updated_instruction = instruction

    for name in input_names:
        # Match the name wrapped in any non-alphanumeric characters (punctuation), e.g., **_input_**, {input}, !!input!!
        pattern = rf"(?:[^\w\s]|_)+{re.escape(name)}(?:[^\w\s]|_)+"

        matches = re.finditer(pattern, updated_instruction)

        for match in matches:
            match_text = match.group(0)
            # Only replace if the match includes surrounding punctuation (not exact word)
            if name != match_text.strip():
                updated_instruction = updated_instruction.replace(
                    match_text, f"{{{name}}}"
                )
                found_inputs.add(name)

    return updated_instruction, list(found_inputs)


def parse_dependencies(dep_string) -> list[int]:
    if not dep_string:
        return []
    return [int(x.strip()) for x in dep_string.split(",") if x.strip().isdigit()]


def create_structured_task(
    task, tool_lookup: dict[str, str], specialists: QuerySet[Tool], known_inputs
):
    tool_ids = find_tool_ids(task["Tool"], tool_lookup)
    specialist = find_specialist_id_by_tools(
        tool_ids, specialists
    ) or find_specialist_by_name(task["Role"], specialists)
    updated_instruction, inputs = extract_and_replace_inputs(
        task["Instruction"], known_inputs
    )

    return {
        "specialist": specialist.pk,
        "tools": tool_ids,
        "parent_tasks": parse_dependencies(task["Dependant task"]),
        "prompt": updated_instruction,
        "inputs": inputs,
        "description": task["Description"],
    }


# --- Usage Example ---
def create_all_tasks(
    tasks,
    specialists: QuerySet[Tool],
    tools: QuerySet[Tool],
    known_inputs,
) -> list[dict]:
    tool_lookup = {tool.display_name: tool.pk for tool in tools}
    structured_tasks = [
        create_structured_task(task, tool_lookup, specialists, known_inputs)
        for task in tasks
    ]
    logger.info(json.dumps(structured_tasks, indent=2))
    return structured_tasks


def add_all_tasks(
    structured_tasks, procedure_id, procedure_creation_job: DynamicProcedureCreationJob
):
    """
    Adds structured tasks to Bricklayer under the given procedure_id.
    Replaces parentTasks (Sr No) with the corresponding created task IDs.

    Args:
        structured_tasks (list): List of structured tasks (from create_all_tasks)
        procedure_id (str): The procedure ID to attach tasks to

    Returns:
        list: List of created task responses
    """
    task_srno_id_map: dict[int, int] = {}  # Maps serial numbers to Bricklayer task IDs
    logger.info("In the adding function")
    logger.info(structured_tasks)

    ui_order_index: int = 1
    for serial_number, structured_task in enumerate(structured_tasks, start=1):
        # Replace Sr No references with actual task IDs in parent_tasks
        # NOTE: We assume that the parent task IDs are always already mapped
        if structured_task["parent_tasks"]:
            resolved_parent_tasks = [
                task_srno_id_map[parent_srno]
                for parent_srno in structured_task["parent_tasks"]
            ]
            structured_task["parent_tasks"] = resolved_parent_tasks

        task = Task.objects.create(
            description=structured_task["description"],
            inputs_names=(
                f"{{{','.join(structured_task['inputs'])}}}"
                if structured_task["inputs"]
                else {}
            ),
            procedure_id=procedure_id,
            prompt=structured_task["prompt"],
            ui_order=ui_order_index,
        )

        if structured_task["parent_tasks"]:
            task.add_parents(*structured_task["parent_tasks"])

        task.connect_tools(structured_task["specialist"], *structured_task["tools"])

        procedure_signals.dynamic_procedure_task_add.send(
            sender=procedure_creation_job.__class__,
            procedure_creation_job=procedure_creation_job,
            task_no=ui_order_index,
        )

        task_srno_id_map[serial_number] = task.pk
        ui_order_index += 1
        logger.info(f"Created task {serial_number}: {task.pk}")
