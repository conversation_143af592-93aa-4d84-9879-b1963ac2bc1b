# Generated by Django 5.2 on 2025-04-29 12:28

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("core", "0009_tasksrelationship_a_tasksrelationship_b_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="DynamicProcedureCreationJob",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("celery_task_id", models.TextField(blank=True, default="")),
                ("process_description", models.TextField(blank=True, default="")),
                (
                    "process_document",
                    models.FileField(
                        blank=True, null=True, upload_to="dynamic_procedures/"
                    ),
                ),
                (
                    "created_procedure",
                    models.OneToOneField(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="core.procedure",
                    ),
                ),
            ],
        ),
    ]
