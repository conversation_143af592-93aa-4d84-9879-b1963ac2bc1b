from procedures.api.serializers import (
    DynamicProcedureSerializer,
    ProcedureRankingSerializer,
)
from rest_framework import generics, status
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON>, MultiPartParser
from rest_framework.response import Response


class ProcedureRankingAPIView(generics.CreateAPIView):
    """
    View to rank an organization's procedures based on a given prompt.
    """

    serializer_class = ProcedureRankingSerializer

    def create(self, request, *args, **kwargs):
        prompt = request.data.get("prompt")
        organization_id = request.query_params.get("organization_id")
        serializer = self.get_serializer(
            data={"organization": organization_id, "prompt": prompt}
        )
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)

        return Response(serializer.data, status=status.HTTP_200_OK, headers=headers)


class DynamicProcedureCreateAPIView(generics.CreateAPIView):
    parser_classes = [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser]
    serializer_class = DynamicProcedureSerializer

    def create(self, request, *args, **kwargs):
        organization_id = request.query_params.get("organization_id")

        serializer = self.get_serializer(
            data={**request.data.dict(), "organization": organization_id}
        )
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)

        return Response(
            serializer.data,
            status=status.HTTP_201_CREATED,
            headers=self.get_success_headers(serializer.data),
        )
