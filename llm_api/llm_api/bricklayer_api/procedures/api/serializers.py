import json
import logging

from core.models import Organization, Procedure, Tool
from langchain_core.messages import HumanMessage, SystemMessage
from procedures.models import DynamicProcedureCreationJob
from procedures.tasks import build_dynamic_procedure
from rest_framework import serializers

from llm_api.llm.factory import (
    default_4_gpt_spec_data_json_enabled,
    get_model_from_spec,
)
from llm_api.specs.llm_spec import LLMSpec, LLMType

logger = logging.getLogger(__name__)


class ToolCardSerializer(serializers.ModelSerializer):
    class Meta:
        model = Tool
        fields = ("id", "displayName", "type")
        extra_kwargs = {
            "displayName": {"source": "display_name"},
        }


class ProcedureCardSerializer(serializers.ModelSerializer):
    isEnabled = serializers.BooleanField(source="is_enabled")
    taskCount = serializers.IntegerField(source="task_count")
    thumbsUpCount = serializers.IntegerField(source="thumbs_up_count")
    totalProcedureRuns = serializers.IntegerField(source="total_procedure_runs")
    totalRatingsCount = serializers.IntegerField(source="total_ratings_count")
    totalTaskRuns = serializers.IntegerField(source="total_task_runs")
    tools = serializers.SerializerMethodField()

    class Meta:
        extra_kwargs = {
            "uiDescription": {"source": "ui_description"},
        }
        fields = (
            "id",
            "isEnabled",
            "name",
            "taskCount",
            "thumbsUpCount",
            "totalProcedureRuns",
            "totalRatingsCount",
            "totalTaskRuns",
            "uiDescription",
            "tools",
        )
        model = Procedure

    @staticmethod
    def get_tools(obj: Procedure):
        tools = {
            tool.pk: tool for task in obj.task_set.all() for tool in task.tools.all()
        }
        return ToolCardSerializer(tools.values(), many=True).data


class ProcedureRankingSelectionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Procedure
        fields = ("id", "name", "ui_description")


class ProcedureRankingSerializer(serializers.Serializer):
    organization = serializers.PrimaryKeyRelatedField(
        queryset=Organization.objects.all(), write_only=True
    )
    prompt = serializers.CharField(write_only=True)
    ranked_procedures = ProcedureCardSerializer(many=True, read_only=True)
    procedures = serializers.ListField(
        child=serializers.DictField(), write_only=True, required=False
    )

    class Meta:
        fields = "__all__"

    def create(self, validated_data):
        prompt = validated_data.pop("prompt")
        organization = validated_data.pop("organization")
        procedures = validated_data.pop("procedures", None)

        if procedures:
            procedure_briefs = ProcedureRankingSelectionSerializer(
                procedures, many=True
            )
        else:
            org_procedures = Procedure.active_objects.filter(
                organization_id=organization.pk
            )
            procedure_briefs = ProcedureRankingSelectionSerializer(
                org_procedures, many=True
            )

        llm = get_model_from_spec(
            spec=LLMSpec(
                type=LLMType.AzureChatOpenAI,
                data=default_4_gpt_spec_data_json_enabled,
            )
        )

        llm_response = llm(
            [
                SystemMessage(
                    content=(
                        """
                        Your goal is to select procedures that are relevant
                        to the user's query. You receive a list of items with name,
                        description, and ID. The matching criteria should not be an
                        exact match. You should select items related
                        to the user's needs.
                        # RESPONSE FORMAT:
                        Output a JSON object with your reasoning and a list of IDs
                        of the selected items in the key 'selected_procedures'.
                        Example:
                        {
                            "reasoning": "The procedures with IDs 1, 2, 3 are all related to 'Foo'",
                            "selected_procedures": [1, 2, 3]
                        }
                        """
                    )
                ),
                HumanMessage(
                    content=(
                        f"""
                        User prompt: {prompt}
                        Items:
                        {procedure_briefs.data}
                        """
                    )
                ),
            ]
        )
        structured_response = json.loads(llm_response.content)

        logger.info("LLM ranking prompt: %s", prompt)
        logger.info("LLM ranking response: %s", structured_response)

        if procedures is not None:
            org_procedures = [
                p
                for p in procedures
                if p["id"] in structured_response["selected_procedures"]
            ]
        else:
            org_procedures = (
                Procedure.objects.filter(
                    organization_id=organization.pk,
                    id__in=structured_response["selected_procedures"],
                )
                .prefetch_related(
                    "task_set",
                    "task_set__tools",
                    "procedurerun_set__procedurerunrating_set",
                    "procedurerun_set__taskrun_set",
                )
                .with_task_count()
                .with_thumbs_up_count()
                .with_procedure_run_count()
                .with_ratings_count()
                .with_task_run_count()
                .with_tools()
            )

        return {
            **self.validated_data,
            "ranked_procedures": org_procedures,
        }


class DynamicProcedureSerializer(serializers.ModelSerializer):
    organization = serializers.PrimaryKeyRelatedField(
        queryset=Organization.objects.all(), write_only=True
    )

    class Meta:
        fields = ("id", "organization", "processDescription", "processDocument")
        extra_kwargs = {
            "processDescription": {"source": "process_description", "write_only": True},
            "processDocument": {"source": "process_document", "write_only": True},
        }
        model = DynamicProcedureCreationJob

    def validate(self, data):
        if not data.get("process_description") and not data.get("process_document"):
            raise serializers.ValidationError(
                "Either `process_description` or `process_document` must be provided."
            )
        return data

    def create(self, validated_data):
        organization: Organization = validated_data.pop("organization")
        job = DynamicProcedureCreationJob.objects.create(**validated_data)
        build_dynamic_procedure.delay(
            job.pk,
            organization.pk,
        )
        return job
