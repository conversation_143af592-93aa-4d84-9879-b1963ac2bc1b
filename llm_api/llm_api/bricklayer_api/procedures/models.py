from core.models import Procedure
from django.db import models
from langchain_community.document_loaders.parsers import PyPDFParser
from langchain_core.documents.base import Blob


class DynamicProcedureCreationJob(models.Model):
    celery_task_id = models.TextField(blank=True, default="")
    created_procedure = models.OneToOneField(
        Procedure, blank=True, on_delete=models.SET_NULL, null=True
    )
    process_description = models.TextField(blank=True, default="")
    process_document = models.FileField(
        blank=True, null=True, upload_to="dynamic_procedures/"
    )

    @property
    def parsed_process_document(self) -> str | None:
        if self.process_document:
            blob = Blob.from_data(self.process_document.read())
            pages = PyPDFParser().parse(blob)
            return "".join([p.page_content for p in pages])

        return None
