import logging

import requests
from core.constants import ToolType
from django.conf import settings
from django.dispatch import receiver
from procedures import signals as procedure_signals
from procedures.models import DynamicProcedureCreationJob

logger = logging.getLogger(__name__)


@receiver(
    procedure_signals.dynamic_procedure_init,
    sender=DynamicProcedureCreationJob,
)
def handle_dynamic_procedure_init(
    sender, procedure_creation_job, procedure_id, **kwargs
):
    logger.info("procedure_signals.dynamic_procedure_init")

    try:
        response = requests.post(
            f"{settings.PUBLIC_BACKEND_URL}/procedures/dynamic/{procedure_creation_job.pk}/event",
            json={
                "type": "_DYNAMIC_PROCEDURE_INIT",
                "message": "Initial procedure created.",
                "procedure_id": procedure_id,
            },
        )
        response.raise_for_status()
    except:
        logger.exception(
            "[procedure_signals.dynamic_procedure_init] Dynamic procedure notification failed"
        )


@receiver(
    procedure_signals.dynamic_procedure_tool_gathering,
    sender=DynamicProcedureCreationJob,
)
def handle_dynamic_procedure_tool_gathering(sender, procedure_creation_job, **kwargs):
    logger.info("procedure_signals.dynamic_procedure_tool_gathering")

    try:
        response = requests.post(
            f"{settings.PUBLIC_BACKEND_URL}/procedures/dynamic/{procedure_creation_job.pk}/event",
            json={
                "type": "DYNAMIC_PROCEDURE_TOOL_GATHERING",
                "message": "Retrieving active tools configured in your environment.",
            },
        )
        response.raise_for_status()
    except:
        logger.exception(
            "[procedure_signals.dynamic_procedure_tool_gathering] Dynamic procedure notification failed"
        )


@receiver(
    procedure_signals.dynamic_procedure_tool_breakdown,
    sender=DynamicProcedureCreationJob,
)
def handle_dynamic_procedure_tool_breakdown(
    sender, procedure_creation_job, tools, **kwargs
):
    logger.info("procedure_signals.dynamic_procedure_tool_breakdown")

    plugin_count: int = len([tool for tool in tools if tool.type == ToolType.PLUGIN])
    data_source_count: int = len(
        [tool for tool in tools if tool.type == ToolType.DATASTORE]
    )
    reporter_count: int = len(
        [tool for tool in tools if tool.type == ToolType.REPORTER]
    )

    message = f"Identified "
    message += f"{plugin_count} plugin{'s' if plugin_count > 1 else ''}, "
    message += f"{data_source_count} data source{'s' if data_source_count > 1 else ''} "
    message += (
        f"and {reporter_count} report generator{'s' if reporter_count > 1 else ''}"
    )
    message += " available for use."

    try:
        response = requests.post(
            f"{settings.PUBLIC_BACKEND_URL}/procedures/dynamic/{procedure_creation_job.pk}/event",
            json={
                "type": "DYNAMIC_PROCEDURE_TOOL_BREAKDOWN",
                "message": message,
            },
        )
        response.raise_for_status()
    except:
        logger.exception(
            "[procedure_signals.dynamic_procedure_tool_breakdown] Dynamic procedure notification failed"
        )


@receiver(
    procedure_signals.dynamic_procedure_coordinator_gathering,
    sender=DynamicProcedureCreationJob,
)
def handle_dynamic_procedure_coordinator_gathering(
    sender, procedure_creation_job, coordinators, **kwargs
):
    logger.info("procedure_signals.dynamic_procedure_coordinator_gathering")

    if len(coordinators):
        try:
            response = requests.post(
                f"{settings.PUBLIC_BACKEND_URL}/procedures/dynamic/{procedure_creation_job.pk}/event",
                json={
                    "type": "DYNAMIC_PROCEDURE_COORDINATOR_GATHERING",
                    "message": f"Retrieving agents configured in your environment. Identified {len(coordinators)} agents available for use.",
                },
            )
            response.raise_for_status()
        except:
            logger.exception(
                "[procedure_signals.dynamic_procedure_coordinator_gathering] Dynamic procedure notification failed"
            )


@receiver(
    procedure_signals.dynamic_procedure_plan_start,
    sender=DynamicProcedureCreationJob,
)
def handle_dynamic_procedure_plan_start(sender, procedure_creation_job, **kwargs):
    logger.info("procedure_signals.dynamic_procedure_plan_start")

    try:
        response = requests.post(
            f"{settings.PUBLIC_BACKEND_URL}/procedures/dynamic/{procedure_creation_job.pk}/event",
            json={
                "type": "DYNAMIC_PROCEDURE_PLAN_START",
                "message": "Initiating collaborative procedure planning using your AI agents.",
            },
        )
        response.raise_for_status()
    except:
        logger.exception(
            "[procedure_signals.dynamic_procedure_plan_start] Dynamic procedure notification failed"
        )


@receiver(
    procedure_signals.dynamic_procedure_requirement_parsing,
    sender=DynamicProcedureCreationJob,
)
def handle_dynamic_procedure_requirement_parsing(
    sender, procedure_creation_job, **kwargs
):
    logger.info("procedure_signals.dynamic_procedure_requirement_parsing")

    try:
        response = requests.post(
            f"{settings.PUBLIC_BACKEND_URL}/procedures/dynamic/{procedure_creation_job.pk}/event",
            json={
                "type": "DYNAMIC_PROCEDURE_REQUIREMENT_PARSING",
                "message": "Processing the input process documentation and absorbing process context.",
            },
        )
        response.raise_for_status()
    except:
        logger.exception(
            "[procedure_signals.dynamic_procedure_requirement_parsing] Dynamic procedure notification failed"
        )


@receiver(
    procedure_signals.dynamic_procedure_plan_init,
    sender=DynamicProcedureCreationJob,
)
def handle_dynamic_procedure_plan_init(sender, procedure_creation_job, **kwargs):
    logger.info("procedure_signals.dynamic_procedure_plan_init")

    try:
        response = requests.post(
            f"{settings.PUBLIC_BACKEND_URL}/procedures/dynamic/{procedure_creation_job.pk}/event",
            json={
                "type": "DYNAMIC_PROCEDURE_PLAN_INIT",
                "message": "Generating initial procedure plan based on your input and available organizational context.",
            },
        )
        response.raise_for_status()
    except:
        logger.exception(
            "[procedure_signals.dynamic_procedure_plan_init] Dynamic procedure notification failed"
        )


@receiver(
    procedure_signals.dynamic_procedure_plan_review,
    sender=DynamicProcedureCreationJob,
)
def handle_dynamic_procedure_plan_review(sender, procedure_creation_job, **kwargs):
    logger.info("procedure_signals.dynamic_procedure_plan_review")

    try:
        response = requests.post(
            f"{settings.PUBLIC_BACKEND_URL}/procedures/dynamic/{procedure_creation_job.pk}/event",
            json={
                "type": "DYNAMIC_PROCEDURE_PLAN_REVIEW",
                "message": "Reviewing the proposed plan to ensure structural completeness and practical relevance.",
            },
        )
        response.raise_for_status()
    except:
        logger.exception(
            "[procedure_signals.dynamic_procedure_plan_review] Dynamic procedure notification failed"
        )


@receiver(
    procedure_signals.dynamic_procedure_plan_validate,
    sender=DynamicProcedureCreationJob,
)
def handle_dynamic_procedure_plan_validate(sender, procedure_creation_job, **kwargs):
    logger.info("procedure_signals.dynamic_procedure_plan_validate")

    try:
        response = requests.post(
            f"{settings.PUBLIC_BACKEND_URL}/procedures/dynamic/{procedure_creation_job.pk}/event",
            json={
                "type": "DYNAMIC_PROCEDURE_PLAN_VALIDATE",
                "message": "Validating procedure draft.",  # TODO: Parametrize it
            },
        )
        response.raise_for_status()
    except:
        logger.exception(
            "[procedure_signals.dynamic_procedure_plan_validate] Dynamic procedure notification failed"
        )


@receiver(
    procedure_signals.dynamic_procedure_plan_judge,
    sender=DynamicProcedureCreationJob,
)
def handle_dynamic_procedure_plan_judge(
    sender, procedure_creation_job, score, passed, **kwargs
):
    logger.info("procedure_signals.dynamic_procedure_plan_judge")

    try:
        response = requests.post(
            f"{settings.PUBLIC_BACKEND_URL}/procedures/dynamic/{procedure_creation_job.pk}/event",
            json={
                "type": "DYNAMIC_PROCEDURE_PLAN_JUDGE",
                "message": f"AI agent feedback score: {score}/10. Plan meets criteria: {'pass' if passed else 'fail'}.",
            },
        )
        response.raise_for_status()
    except:
        logger.exception(
            "[procedure_signals.dynamic_procedure_plan_judge] Dynamic procedure notification failed"
        )


@receiver(
    procedure_signals.dynamic_procedure_plan_improve,
    sender=DynamicProcedureCreationJob,
)
def handle_dynamic_procedure_plan_improve(sender, procedure_creation_job, **kwargs):
    logger.info("procedure_signals.dynamic_procedure_plan_improve")

    try:
        response = requests.post(
            f"{settings.PUBLIC_BACKEND_URL}/procedures/dynamic/{procedure_creation_job.pk}/event",
            json={
                "type": "DYNAMIC_PROCEDURE_PLAN_IMPROVE",
                "message": "Refining the plan based on validation findings and agent feedback.",
            },
        )
        response.raise_for_status()
    except:
        logger.exception(
            "[procedure_signals.dynamic_procedure_plan_improve] Dynamic procedure notification failed"
        )


@receiver(
    procedure_signals.dynamic_procedure_plan_complete,
    sender=DynamicProcedureCreationJob,
)
def handle_dynamic_procedure_plan_complete(sender, procedure_creation_job, **kwargs):
    logger.info("procedure_signals.dynamic_procedure_plan_complete")

    try:
        response = requests.post(
            f"{settings.PUBLIC_BACKEND_URL}/procedures/dynamic/{procedure_creation_job.pk}/event",
            json={
                "type": "DYNAMIC_PROCEDURE_PLAN_COMPLETE",
                "message": "All required checks complete. Plan finalized after iterative review and agent validation.",
            },
        )
        response.raise_for_status()
    except:
        logger.exception(
            "[procedure_signals.dynamic_procedure_plan_complete] Dynamic procedure notification failed"
        )


@receiver(
    procedure_signals.dynamic_procedure_task_creation_start,
    sender=DynamicProcedureCreationJob,
)
def handle_dynamic_procedure_task_creation_start(
    sender, procedure_creation_job, **kwargs
):
    logger.info("procedure_signals.dynamic_procedure_task_creation_start")

    try:
        response = requests.post(
            f"{settings.PUBLIC_BACKEND_URL}/procedures/dynamic/{procedure_creation_job.pk}/event",
            json={
                "type": "DYNAMIC_PROCEDURE_TASK_CREATION_START",
                "message": "Initializing tasks from the approved plan, including roles, tools, and dependencies.",
            },
        )
        response.raise_for_status()
    except:
        logger.exception(
            "[procedure_signals.dynamic_procedure_task_creation_start] Dynamic procedure notification failed"
        )


@receiver(
    procedure_signals.dynamic_procedure_task_add,
    sender=DynamicProcedureCreationJob,
)
def handle_dynamic_procedure_task_add(
    sender, procedure_creation_job, task_no, **kwargs
):
    logger.info("procedure_signals.dynamic_procedure_task_add")

    try:
        response = requests.post(
            f"{settings.PUBLIC_BACKEND_URL}/procedures/dynamic/{procedure_creation_job.pk}/event",
            json={
                "type": "DYNAMIC_PROCEDURE_TASK_ADD",
                "message": f"Created Task {task_no}...",
            },
        )
        response.raise_for_status()
    except:
        logger.exception(
            "[procedure_signals.dynamic_procedure_task_add] Dynamic procedure notification failed"
        )


@receiver(
    procedure_signals.dynamic_procedure_specialist_assignment,
    sender=DynamicProcedureCreationJob,
)
def handle_dynamic_procedure_specialist_assignment(
    sender, procedure_creation_job, chosen_specialist, **kwargs
):
    logger.info("procedure_signals.dynamic_procedure_specialist_assignment")

    try:
        response = requests.post(
            f"{settings.PUBLIC_BACKEND_URL}/procedures/dynamic/{procedure_creation_job.pk}/event",
            json={
                "type": "DYNAMIC_PROCEDURE_SPECIALIST_ASSIGNMENT",
                "message": f"Assigning the procedure to the {chosen_specialist.display_name}",
            },
        )
        response.raise_for_status()
    except:
        logger.exception(
            "[procedure_signals.dynamic_procedure_specialist_assignment] Dynamic procedure notification failed"
        )
