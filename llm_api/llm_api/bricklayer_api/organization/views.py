import json
from core.models import (
    Organization,
    TemplateProcedure,
    TemplateTask,
    TemplateTasksPublicToolsRelationship,
    TemplateTasksTemplateToolsRelationship,
    ToolTemplate,
    Procedure,
    Task,
    TasksToolsRelationship,
    TemplateTasksRelationship,
    Tool
)
from django.db.models import Q
from django.http import JsonResponse
from django.shortcuts import get_object_or_404
from django.utils import timezone
from organization.models import GoalSuggestion
from organization.onboarding_files import create_datastores
from organization.utils.onboarding_completion import (
    create_procedures_for_onboarding,
    create_tools_agents,
    mark_onboarding_complete,
    get_template_procedure_details,
    create_procedures_for_onboarding_v2
)

from organization.utils.onboarding_updation import (
    update_procedure_details,
    update_tools_for_task_details,
    diff_enabled_vs_cached_proc_ids,
    update_task_fields

)

from organization.utils.onboarding_deletion import (
    remove_task_from_procedure
)
from organization.utils.onboarding_creation import (
    add_task_to_procedure,
    get_minimal_tool_template_info,
    collect_required_tools_and_used_by_map_from_db
)


from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView
import logging
from procedures.api.serializers import ProcedureRankingSerializer
from core.enums import ToolType
from copy import deepcopy
from rest_framework.response import Response
from django.db.models import Q


logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


class OnboardingSessionView(APIView):
    authentication_classes = []  # Add authentication classes if needed
    permission_classes = []

    def get(self, request):
        org_id = request.GET.get("organization_id")
        if not org_id:
            return Response(
                {"error": "Missing organization_id"}, status=status.HTTP_400_BAD_REQUEST
            )

        org = get_object_or_404(Organization, id=org_id)

        # Initialize onboarding_data if it's None or empty
        if not org.onboarding_data:
            org.onboarding_data = {"steps_data": {}, "current_step": "goal"}
            org.save(update_fields=["onboarding_data"])

        onboarding_complete = org.onboarding_complete
        current_step = org.onboarding_data.get("current_step", "goal")

        return Response(
            {"onboarding_complete": onboarding_complete, "current_step": current_step}
        )


class OnboardingStepView(APIView):
    authentication_classes = []
    permission_classes = []

    def sanitize_text(self, text):
        import re
        # Remove things that can confuse the LLM or cause filtering
        if text:
            text = re.sub(r"[{}\[\]<>]", "", text)  # remove brackets
            text = text.replace("system", "").replace("jailbreak", "")
            text = text.replace("ignore previous", "")
        return text

    def transform_ui_description_to_description(self, procedures):
        """Move 'ui_description' into 'description' and delete 'ui_description'."""
        return [
            {
                **proc,
                "description": proc.get("ui_description", ""),
            }
            for proc in procedures
        ]

    def get(self, request, step):
        org_id = request.GET.get("organization_id")
        if not org_id:
            return Response(
                {"error": "Missing organization_id"}, status=status.HTTP_400_BAD_REQUEST
            )

        org = get_object_or_404(Organization, id=org_id)
        # Dispatch to the relevant handler
        handler = getattr(self, f"handle_{step}", None)
        if handler and callable(handler):
            return handler(org)
        else:
            return Response({"error": f"Unsupported step: {step}"}, status=400)

    def handle_goal(self, org):
        # Directly return the goal step from JSON
        step_data = org.onboarding_data.get("steps_data", {}).get("goal", {})
        return Response(step_data)

    def handle_procedures(self, org):
        onboarding_data = org.onboarding_data or {}
        steps_data = onboarding_data.get("steps_data", {})
        procedures_data = steps_data.get("procedures", {})

        def serialize(procedure):
            if isinstance(procedure, dict):
                return {
                    "id": procedure["id"],
                    "name": procedure["name"],
                    "ui_description": procedure.get("ui_description", ""),  # important
                    "enabled": procedure.get("enabled", False),
                }
            else:
                return {
                    "id": procedure.id,
                    "name": procedure.name,
                    "ui_description": procedure.ui_description,
                    "enabled": procedure.id in procedures_data.get("enabled_ids", []),
                }

        raw_goals = steps_data.get("goal", {}).get("rawGoals", "").strip()

        if not raw_goals:
            raw_goals = "Select top three procedures"

        if procedures_data.get("recommended") and procedures_data.get("all"):
            all_templates = procedures_data["recommended"] + procedures_data["all"]
            all_templates = sorted(all_templates, key=lambda p: p["id"])

            procedures_payload = [
                {
                    "id": p["id"],
                    "name": self.sanitize_text(p["name"]),
                    "ui_description": self.sanitize_text(p.get("ui_description", "")),
                }
                for p in all_templates
            ]
        else:
            all_templates = TemplateProcedure.objects.filter().order_by("id")
            procedures_payload = [
                {
                    "id": p.id,
                    "name": self.sanitize_text(p.name),
                    "ui_description": self.sanitize_text(p.ui_description),
                }
                for p in all_templates
            ]

        goal_data = steps_data.get("goal", {})
        should_rank = goal_data.get("ranking", True)  # if missing, assume True

        if not should_rank and procedures_data.get("recommended") and procedures_data.get("all"):
            logger.debug("Skipping re-ranking, showing existing recommended and all")
            return Response({
                "recommended": procedures_data["recommended"],
                "all": procedures_data["all"],
            })

        serializer = ProcedureRankingSerializer(
            data={
                "prompt": raw_goals,
                "procedures": procedures_payload,
                "organization": org.id,
            }
        )
        serializer.is_valid(raise_exception=True)
        ranked_data = serializer.save()
        ranked_procedures = ranked_data["ranked_procedures"]
        recommended_ids = {p["id"] for p in ranked_procedures}

        if isinstance(all_templates[0], dict):
            procedure_template_map = {p["id"]: p for p in all_templates}
        else:
            procedure_template_map = {p.id: p for p in all_templates}

        ranked_full_procedures = [
            procedure_template_map[proc["id"]]
            for proc in ranked_procedures
            if proc["id"] in procedure_template_map
        ]

        recommended = [serialize(p) for p in ranked_full_procedures]

        if all_templates and isinstance(all_templates[0], dict):
            all_items = [
                serialize(p) for p in all_templates if p["id"] not in recommended_ids
            ]
        else:
            all_items = [
                serialize(p) for p in all_templates if p.id not in recommended_ids
            ]

        recommended = self.transform_ui_description_to_description(recommended)
        all_items = self.transform_ui_description_to_description(all_items)

        steps_data["goal"]["ranking"] = False

        # Save newly ranked procedures
        steps_data["procedures"] = {
            "recommended": recommended,
            "all": all_items,
        }
        onboarding_data["steps_data"] = steps_data
        org.onboarding_data = onboarding_data
        org.save(update_fields=["onboarding_data"])

        return Response({"recommended": recommended, "all": all_items})

    def build_cached_proc_map(self, required_tool_data):
        proc_map = {}
        for tool in required_tool_data:
            for entry in tool.get("usedInProcedures", []):
                proc = entry.get("unconfiguredProcedureDetails")
                if proc and isinstance(proc, dict) and "id" in proc:
                    proc_map[proc["id"]] = proc
        return proc_map

    def serialize_tool(self, tool, used_by, tool_enabled, proc_ids, cached_procedures, existing_tool=None):
        used_in_procs = []

        for pid in proc_ids:
            if pid in cached_procedures:
                used_in_procs.append({
                    "unconfiguredProcedureDetails": deepcopy(cached_procedures[pid])
                })
            else:
                fetched = get_template_procedure_details(pid)
                used_in_procs.append({
                    "unconfiguredProcedureDetails": fetched["unconfiguredProcedureDetails"]
                })

        # ✅ Preserve configuration and enabled if already set
        preserved_config = None
        preserved_enabled = tool_enabled
        if existing_tool:
            preserved_config = existing_tool.get("configuration")
            preserved_enabled = existing_tool.get("enabled", tool_enabled)

        return {
            "id": tool.id,
            "name": tool.display_name,
            "version": tool.spec.get("version") if isinstance(tool.spec, dict) else None,
            "auth_type": tool.spec.get("auth_type") if isinstance(tool.spec, dict) else None,
            "description": tool.display_description,
            "usedBy": list(used_by),
            "enabled": preserved_enabled,
            "configuration": preserved_config,
            "configurationForm": {
                "spec": getattr(tool, "spec", {}),
                "configurableSpecFields": getattr(tool, "configurable_spec_fields", {}),
            },
            "type": tool.type,
            "usedInProcedures": used_in_procs
        }

    def handle_tools(self, org):
        onboarding_data = org.onboarding_data or {}
        steps_data = onboarding_data.get("steps_data", {})
        tools_data = steps_data.get("tools", {})
        procedures_data = steps_data.get("procedures", {})
        enabled_procedures = procedures_data.get("all", [])
        recommended_procedures = procedures_data.get("recommended", [])

        enabled_proc_objs = [proc for proc in enabled_procedures + recommended_procedures if proc.get("enabled")]
        enabled_proc_ids = [proc["id"] for proc in enabled_proc_objs]

        existing_tools_data = tools_data if tools_data else {"required": [], "all": []}
        existing_map = {tool["id"]: tool for tool in
                        existing_tools_data.get("required", []) + existing_tools_data.get("all", [])}

        used_by_map = {}
        required_tool_ids = set()

        if not existing_map:
            required_tool_ids, used_by_map = collect_required_tools_and_used_by_map_from_db(enabled_proc_ids)
        else:
            newly_added_proc_ids, removed_proc_ids = diff_enabled_vs_cached_proc_ids(existing_tools_data,
                                                                                     enabled_proc_ids)
            if newly_added_proc_ids:
                new_tool_ids, new_used_by_map = collect_required_tools_and_used_by_map_from_db(newly_added_proc_ids)
                required_tool_ids.update(new_tool_ids)
                for tid, names in new_used_by_map.items():
                    used_by_map.setdefault(tid, set()).update(names)

            for proc in enabled_proc_objs:
                proc_name = proc["name"]
                proc_id = proc["id"]
                if proc_id in removed_proc_ids:
                    continue

                for tool in existing_tools_data.get("required", []):
                    tool_id = tool["id"]
                    for proc_info in tool.get("usedInProcedures", []):
                        proc_detail = proc_info.get("unconfiguredProcedureDetails")
                        if not proc_detail or proc_detail["id"] != proc_id:
                            continue
                        used_by_map.setdefault(tool_id, set()).add(proc_name)
                        for task in proc_detail.get("childrenUnconfiguredTasks", []):
                            for t in task.get("tools", []):
                                nested_tool_id = int(t["id"])
                                required_tool_ids.add(nested_tool_id)
                                used_by_map.setdefault(nested_tool_id, set()).add(proc_name)

        all_templates = ToolTemplate.objects.filter(configured_by='public').exclude(type='coordinator')
        cached_proc_map = self.build_cached_proc_map(existing_tools_data.get("required", []))

        new_required = []
        new_all = []

        for tool in all_templates:
            used_by = used_by_map.get(tool.id, set())
            existing_tool = existing_map.get(tool.id)
            proc_ids = [p.id for p in TemplateProcedure.objects.filter(name__in=used_by)]

            tool_enabled = False
            if existing_tool and existing_tool.get("enabled"):
                tool_enabled = True
            elif tool.spec and isinstance(tool.spec, dict) and tool.spec.get("auth_type") is None:
                tool_enabled = True

            serialized_tool = self.serialize_tool(
                tool, used_by, tool_enabled, proc_ids,
                cached_procedures=cached_proc_map,
                existing_tool=existing_tool
            )

            if tool.id in required_tool_ids:
                new_required.append(serialized_tool)
            else:
                new_all.append(serialized_tool)

        steps_data["tools"] = {
            "required": new_required,
            "all": new_all,
        }
        onboarding_data["steps_data"] = steps_data
        org.onboarding_data = onboarding_data
        org.save(update_fields=["onboarding_data"])

        return Response({"required": new_required, "all": new_all})

    def handle_agents(self, org):
        onboarding_data = org.onboarding_data or {}
        steps_data = onboarding_data.get("steps_data", {})
        agents_data = steps_data.get("agents", {})

        def serialize_agents(tool, used_by, enabled=False):
            return {
                "id": tool.id,
                "name": tool.display_name,
                "description": tool.display_description,
                "usedBy": list(used_by),
                "enabled": enabled,  # Default to False; toggled via separate view
                "configuration": None,
                "configurationForm": {
                    "spec": getattr(tool, "spec", {}),
                    "configurableSpecFields": getattr(
                        tool, "configurable_spec_fields", {}
                    ),
                },
                "type": tool.type,
            }


        procedures_data = steps_data.get("procedures", {})
        enabled_procedures = procedures_data.get("all", [])
        required_procedures = procedures_data.get("recommended", [])

        enabled_proc_ids_all = [
            proc["id"] for proc in enabled_procedures if proc.get("enabled")
        ]
        enabled_proc_ids_required = [
            proc["id"] for proc in required_procedures if proc.get("enabled")
        ]
        enabled_proc_ids = enabled_proc_ids_required + enabled_proc_ids_all

        used_by_map = {}
        required_tool_ids = set()

        for proc in TemplateProcedure.objects.filter(id__in=enabled_proc_ids):
            tasks = TemplateTask.objects.filter(template_procedure=proc)

            for task in tasks:
                # Get ToolTemplate IDs
                template_ids = (
                    TemplateTasksTemplateToolsRelationship.objects.filter(
                        a=task
                    ).values_list("b_id", flat=True)
                )
                for tid in template_ids:
                    required_tool_ids.add(tid)
                    used_by_map.setdefault(tid, set()).add(proc.name)

                # Get Public Tool IDs
                public_ids = TemplateTasksPublicToolsRelationship.objects.filter(
                    a=task
                ).values_list("b_id", flat=True)
                for pid in public_ids:
                    required_tool_ids.add(pid)
                    used_by_map.setdefault(pid, set()).add(proc.name)

        all_templates = ToolTemplate.objects.filter(type="coordinator")

        existing_agents_data = agents_data if agents_data else {"required": [], "all": []}

        existing_map = {
            agent["id"]: agent
            for agent in existing_agents_data.get("required", []) + existing_agents_data.get("all", [])
        }

        new_required = []
        new_all = []
        for tool in all_templates:
            used_by = used_by_map.get(tool.id, set())

            existing_tool = existing_map.get(tool.id)
            if existing_tool:
                existing_tool["usedBy"] = list(set(existing_tool.get("usedBy", [])) | used_by)
                if tool.id in required_tool_ids:
                    new_required.append(existing_tool)
                else:
                    new_all.append(existing_tool)
            else:
                tool_enabled = tool.id in required_tool_ids
                serialized_tool = serialize_agents(tool, used_by, tool_enabled)
                if tool.id in required_tool_ids:
                    new_required.append(serialized_tool)
                else:
                    new_all.append(serialized_tool)


        # Save to onboarding_data
        steps_data["agents"] = {
            "required": new_required,
            "all": new_all,
        }
        onboarding_data["steps_data"] = steps_data
        org.onboarding_data = onboarding_data
        org.save(update_fields=["onboarding_data"])

        return Response({"required": new_required, "all": new_all})

    def handle_files(self, org):
        onboarding_data = org.onboarding_data or {}
        steps_data = onboarding_data.get("steps_data", {})
        context_data = steps_data.get("context", {})
        files = context_data.get("files", [])

        return Response({"files": files}, status=200)

    def handle_team(self, org):
        onboarding_data = org.onboarding_data or {}
        steps_data = onboarding_data.get("steps_data", {})
        team_data = steps_data.get("team", {})
        emails = team_data.get("emails", [])
        return Response({"emails": emails}, status=status.HTTP_200_OK)

    def handle_review(self, org):
        onboarding_data = org.onboarding_data or {}
        steps_data = onboarding_data.get("steps_data", {})

        # Extract team emails
        team_emails = steps_data.get("team", {}).get("emails", [])
        files = steps_data.get("context", {}).get("files", [])

        # Initialize reverse mappings
        tool_used_by = {}
        agent_used_by = {}

        # Combine 'all' and 'required' tools
        tools_data = steps_data.get("tools", {})
        all_tools = tools_data.get("all", [])
        required_tools = tools_data.get("required", [])
        required_tool_filter = [tool["id"] for tool in  required_tools]
        combined_tools_dict = {tool["id"]: tool for tool in all_tools + required_tools}
        combined_tools = combined_tools_dict.values()

        tools = []
        for tool in combined_tools:
            tool_id = tool.get("id")
            tool_name = tool.get("name")
            used_by = tool.get("usedBy", [])
            enabled = tool.get("enabled", [])
            tool_type = tool.get("type", [])
            configuration = tool.get("configuration", [])
            used_in_procedures = tool.get("usedInProcedures", [])

            if (enabled is False) and (tool_id not in required_tool_filter):
                continue

            for proc_name in used_by:
                tool_used_by.setdefault(proc_name, []).append(tool_name)
            tools.append(
                {
                    "id": tool_id,
                    "name": tool_name,
                    "description": tool.get("description", ""),
                    "usedBy": used_by,
                    "type": tool_type,
                    "enabled": enabled,
                    "configuration": configuration,
                    "usedInProcedures": used_in_procedures
                }
            )

        # Combine 'all' and 'required' agents
        agents_data = steps_data.get("agents", {})
        all_agents = agents_data.get("all", [])
        required_agents = agents_data.get("required", [])
        combined_agents_dict = {
            agent["id"]: agent for agent in all_agents + required_agents
        }
        combined_agents = combined_agents_dict.values()

        agents = []
        for agent in combined_agents:
            agent_id = agent.get("id")
            agent_name = agent.get("name")
            used_by = agent.get("usedBy", [])
            enabled = agent.get("enabled", [])
            agent_type = agent.get("type", [])
            if enabled is False:
                continue
            for proc_name in used_by:
                agent_used_by.setdefault(proc_name, []).append(agent_name)
            agents.append(
                {
                    "id": agent_id,
                    "name": agent_name,
                    "description": agent.get("description", ""),
                    "usedBy": used_by,
                    "type": agent_type,
                }
            )

        procedures_all = steps_data.get("procedures", {}).get("all", [])
        procedures_recommended = steps_data.get("procedures", {}).get("recommended", [])
        procedures_raw = procedures_all + procedures_recommended
        procedures = []
        for proc in procedures_raw:
            if proc.get("enabled"):
                proc_name = proc.get("name")
                proc_id = proc.get("id")
                procedures.append(
                    {
                        "id": proc_id,
                        "name": proc_name,
                        "description": proc.get("description", ""),
                        "agents": agent_used_by.get(proc_name, []),
                        "tools": tool_used_by.get(proc_name, []),
                    }
                )

        review_data = {
            "procedures": procedures,
            "tools": tools,
            "agents": agents,
            "files": files,
            "team": team_emails,
        }

        steps_data["review"] = review_data
        onboarding_data["steps_data"] = steps_data
        org.onboarding_data = onboarding_data

        org.save(update_fields=["onboarding_data"])

        return Response(review_data, status=status.HTTP_200_OK)


class GoalSuggestionsView(APIView):
    permission_classes = []  # Auth removed

    def get(self, request):
        suggestions = GoalSuggestion.objects.all().values("id", "name", "description")
        return Response({"suggestions": list(suggestions)})


class CompleteStepView(APIView):
    authentication_classes = []
    permission_classes = []

    def get_next_step(self, current_step):
        onboarding_steps = [
            "goal",
            "procedures",
            "tools",
            "agents",
            "context",
            "team",
            "review",
        ]
        try:
            current_index = onboarding_steps.index(current_step)
            return onboarding_steps[current_index + 1]
        except (ValueError, IndexError):
            return "review"

    def post(self, request):
        data = request.data
        org_id = data.get("organization_id")
        step = data.get("step")
        step_data = data.get("data")
        if not org_id or not step:
            return Response(
                {"status": "error", "error": "Missing organization_id, step or data"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        org = get_object_or_404(Organization, id=org_id)
        onboarding = org.onboarding_data or {"steps_data": {}, "current_step": "goal"}
        steps_data = onboarding.get("steps_data", {})
        # STEP: GOAL
        if step == "goal":
            previous_goal_text = steps_data.get("goal", {}).get("rawGoals", "").strip()
            current_goal_text = step_data.get("rawGoals", "").strip()

            # Now compare
            ranking_needed = previous_goal_text != current_goal_text

            # Set updated goal inside steps_data
            steps_data["goal"] = step_data

            # Set ranking flag based on comparison
            steps_data["goal"]["ranking"] = ranking_needed

            # Save onboarding data
            onboarding["steps_data"] = steps_data

        elif step == "procedures":
            pass
        elif step == "tools":
            tools_data = steps_data.get("tools", {})

            # if data.get('bypass') is None:
            #     for category in ["required", "all"]:
            #         for tool in tools_data.get(category, []):
            #             if category in ['required'] and tool.get("enabled") is False:
            #                 return Response(
            #                     {
            #                         "error": f"Required Tools should be configured"
            #                     },
            #                     status=status.HTTP_400_BAD_REQUEST
            #                 )
            #
            #             if tool.get("enabled"):
            #                 expected_auth_type = (
            #                     tool.get("configurationForm", {})
            #                     .get("spec", {})
            #                     .get("auth_type")
            #                 )
            #                 tool_config = tool.get("configuration", {}).get('data', {})
            #                 if expected_auth_type:
            #                     if expected_auth_type not in tool_config or not tool_config.get(expected_auth_type):
            #                         return Response(
            #                             {
            #                                 "error": f"auth_type mismatch for Tool '{tool.get('name')}'. "
            #                                          f"Expected field: '{expected_auth_type}'"
            #                             },
            #                             status=status.HTTP_400_BAD_REQUEST
            #                         )
        elif step == "agents":
            pass
        elif step == "context":
            pass

        onboarding["current_step"] = self.get_next_step(step)
        org.onboarding_data = onboarding
        org.updated_at = timezone.now()
        org.save()

        return Response({"status": "success", "next_step": onboarding["current_step"]})


class ToggleProcedureView(APIView):
    authentication_classes = []
    permission_classes = []

    def put(self, request):
        data = request.data
        org_id = data.get("organization_id")
        procedure_id = data.get("procedure_id")

        if not org_id or procedure_id is None:
            return Response(
                {"error": "organization_id and procedure_id are required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        org = get_object_or_404(Organization, id=org_id)

        onboarding_data = org.onboarding_data or {}
        steps_data = onboarding_data.get("steps_data", {})
        procedures_data = steps_data.get("procedures", {})

        if "all" not in procedures_data:
            return Response(
                {
                    "error": "Procedure list not initialized. Please fetch step view first."
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        for proc in procedures_data["all"]:
            if proc["id"] == procedure_id:
                proc["enabled"] = not proc.get("enabled", False)
                break

        for proc in procedures_data.get("recommended", []):
            if proc["id"] == procedure_id:
                proc["enabled"] = not proc.get("enabled", False)
                break

        steps_data["procedures"] = procedures_data
        onboarding_data["steps_data"] = steps_data
        org.onboarding_data = onboarding_data
        org.save(update_fields=["onboarding_data"])

        return Response({"status": "ok", "message": "Procedure toggled"}, status=200)


class ToolConfigureView(APIView):
    """
    API endpoint to configure a tool during the onboarding process.
    """

    def post(self, request):
        data = request.data
        required_fields = ["organization_id", "type", "id", "data"]

        # Validate required fields
        for field in required_fields:
            if field not in data:
                return Response(
                    {"error": f"Missing required field: {field}"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        organization_id = data["organization_id"]
        tool_id = data["id"]
        tool_type = data["type"]
        tool_data = data["data"]
        # label_name = data["labelName"]

        # Validate tool type
        if tool_type != "customCredentials":
            return Response(
                {"error": 'Only "customCredentials" type is supported.'},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Simulate encryption (replace with actual encryption logic)
        encrypted_data = {"data": tool_data}

        # Retrieve organization
        org = get_object_or_404(Organization, id=organization_id)
        onboarding_data = org.onboarding_data or {
            "steps_data": {},
            "current_step": "goal",
        }
        steps_data = onboarding_data.get("steps_data", {})
        tools_data = steps_data.get("tools", {})

        # Update tool configuration in onboarding data
        updated = False
        for category in ["required", "all"]:
            for tool in tools_data.get(category, []):
                if str(tool.get("id")) == str(tool_id):
                    expected_auth_type = (
                        tool.get("configurationForm", {})
                        .get("spec", {})
                        .get("auth_type")
                    )
                    data_extract = encrypted_data.get('data')

                    # if expected_auth_type and data.get("bypass") is None:
                    #     if expected_auth_type not in data_extract or not data_extract[expected_auth_type]:
                    #         return Response(
                    #             {
                    #                 "error": f"auth_type mismatch. Please provide {expected_auth_type}'"
                    #             },
                    #             status=status.HTTP_400_BAD_REQUEST
                    #         )

                    tool["configuration"] = encrypted_data
                    tool["configuration"]["type"] = tool_type
                    tool["enabled"] = True
                    # tool["configuration"]["label"] = label_name
                    updated = True
                    break
            if updated:
                break

        if not updated:
            return Response(
                {"error": f"Tool with id {tool_id} not found in onboarding data."},
                status=status.HTTP_404_NOT_FOUND,
            )

        for category in ["required", "all"]:
            for tool in tools_data.get(category, []):
                if str(tool.get("id")) == str(tool_id):
                    break

        # Save updated onboarding data
        steps_data["tools"] = tools_data
        onboarding_data["steps_data"] = steps_data
        org.onboarding_data = onboarding_data
        org.updated_at = timezone.now()
        org.save(update_fields=["onboarding_data", "updated_at"])

        org.refresh_from_db()
        updated_tools_data = org.onboarding_data.get("steps_data", {}).get("tools", {})
        for category in ["required", "all"]:
            for tool in updated_tools_data.get(category, []):
                if str(tool.get("id")) == str(tool_id):
                    break

        return Response({"status": "success"}, status=status.HTTP_201_CREATED)


class ToggleToolView(APIView):
    authentication_classes = []
    permission_classes = []

    def put(self, request):
        data = request.data
        org_id = data.get("organization_id")
        tool_id = data.get("tool_id")

        if not org_id or tool_id is None:
            return Response(
                {"error": "organization_id and tool_id are required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        org = get_object_or_404(Organization, id=org_id)
        onboarding_data = org.onboarding_data or {}
        steps_data = onboarding_data.get("steps_data", {})
        tools_data = steps_data.get("tools", {})

        updated = False
        for tool_list in ["required", "all"]:
            for tool in tools_data.get(tool_list, []):
                if str(tool["id"]) == str(tool_id):
                    tool["enabled"] = not tool.get("enabled", False)
                    updated = True
                    break

        if not updated:
            return Response(
                {"error": f"Tool with id {tool_id} not found in onboarding data."},
                status=status.HTTP_404_NOT_FOUND,
            )

        steps_data["tools"] = tools_data
        onboarding_data["steps_data"] = steps_data
        org.onboarding_data = onboarding_data
        org.save(update_fields=["onboarding_data"])

        return Response({"status": "ok", "message": "Tool toggled"}, status=200)



class ToggleAgentView(APIView):
    authentication_classes = []
    permission_classes = []

    def put(self, request):
        data = request.data
        org_id = data.get("organization_id")
        agent_id = data.get("agent_id")
        if not org_id or agent_id is None:
            return Response(
                {"error": "organization_id and agent_id are required"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        org = get_object_or_404(Organization, id=org_id)
        onboarding_data = org.onboarding_data or {}
        steps_data = onboarding_data.get("steps_data", {})
        agents_data = steps_data.get("agents", {})

        updated = False
        for agent_list in ["required", "all"]:
            for agent in agents_data.get(agent_list, []):
                if str(agent["id"]) == str(agent_id):

                    agent["enabled"] = not agent.get("enabled", False)
                    updated = True
                    break
        if not updated:
            return Response(
                {"error": f"Agent with id {agent_id} not found in onboarding data."},
                status=status.HTTP_404_NOT_FOUND,
            )

        steps_data["agents"] = agents_data
        onboarding_data["steps_data"] = steps_data
        org.onboarding_data = onboarding_data
        org.save(update_fields=["onboarding_data"])

        return Response({"status": "ok", "message": "Agent toggled"}, status=200)


class ClearOnboardingView(APIView):
    """
    Clears the onboarding data for an organization.
    """

    authentication_classes = []  # Add authentication classes if needed
    permission_classes = []  # Add permission classes if needed

    def post(self, request):
        data = request.data
        org_id = data.get("organization_id")
        if not org_id:
            return Response(
                {"error": "Missing organization_id"}, status=status.HTTP_400_BAD_REQUEST
            )
        org = get_object_or_404(Organization, id=org_id)
        org.onboarding_complete = False
        org.onboarding_data = {}
        org.save(update_fields=["onboarding_complete", "onboarding_data"])

        return Response({"status": "onboarding reset", "organization_id": org_id})


class OnboardingBlobDebugView(APIView):
    """
    Debug
    """

    authentication_classes = []  # Add auth if needed
    permission_classes = []

    def get(self, request):
        org_id = request.GET.get("organization_id")
        if not org_id:
            return Response(
                {"error": "Missing organization_id"}, status=status.HTTP_400_BAD_REQUEST
            )

        org = get_object_or_404(Organization, id=org_id)
        return Response(
            {
                "organization_id": org.id,
                "onboarding_data": org.onboarding_data or {},
                "onboarding_complete": org.onboarding_complete,
            }
        )


class GoalSuggestionCreateView(APIView):
    authentication_classes = []  # Add auth if needed
    permission_classes = []

    def post(self, request):
        data = request.data
        name = data.get("name")
        description = data.get("description")

        if not name or not description:
            return Response(
                {"error": "Missing 'name' or 'description'"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        suggestion = GoalSuggestion.objects.create(name=name, description=description)
        return Response(
            {
                "status": "created",
                "id": suggestion.id,
                "name": suggestion.name,
                "description": suggestion.description,
            },
            status=status.HTTP_201_CREATED,
        )

class GoalSuggestionsDeleteView(APIView):

    authentication_classes = []  # Add auth if needed
    permission_classes = []

    def post(self, request):
        suggestion_id = request.data.get("id")

        if not suggestion_id:
            return Response({"error": "Missing 'id' in request body"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            suggestion = GoalSuggestion.objects.get(id=suggestion_id)
            suggestion.delete()
            return Response({"status": f"Goal suggestion {suggestion_id} deleted"}, status=status.HTTP_200_OK)
        except GoalSuggestion.DoesNotExist:
            return Response({"error": "Goal suggestion not found"}, status=status.HTTP_404_NOT_FOUND)


class OnboardingContextFileUploadView(APIView):
    authentication_classes = []
    permission_classes = []

    def post(self, request):
        data = request.data
        org_id = data.get("organization_id")
        s3_key = data.get("s3_key")
        file_name = data.get("file_name")

        if not org_id or not s3_key or not file_name:
            return Response(
                {"error": "Missing organization_id, s3_key or file_name"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        org = get_object_or_404(Organization, id=org_id)

        onboarding_data = org.onboarding_data or {
            "steps_data": {},
            "current_step": "goal",
        }

        steps_data = onboarding_data.get("steps_data", {})
        context_data = steps_data.get("context", {})

        files = context_data.get("files", [])
        files.append({"key": s3_key, "name": file_name})

        context_data["files"] = files
        steps_data["context"] = context_data
        onboarding_data["steps_data"] = steps_data
        org.onboarding_data = onboarding_data
        org.save(update_fields=["onboarding_data"])

        return Response({"status": "file added"}, status=200)


class OnboardingContextFilesView(APIView):
    authentication_classes = []
    permission_classes = []

    def get(self, request):
        org_id = request.GET.get("organization_id")
        if not org_id:
            return Response(
                {"error": "Missing organization_id"}, status=status.HTTP_400_BAD_REQUEST
            )

        org = get_object_or_404(Organization, id=org_id)
        onboarding_data = org.onboarding_data or {}
        steps_data = onboarding_data.get("steps_data", {})
        context_data = steps_data.get("context", {})
        files = context_data.get("files", [])

        return Response({"files": files}, status=200)

    def delete(self, request):
        org_id = request.data.get("organization_id")
        s3_key = request.data.get("s3_key")
        if not org_id or not s3_key:
            return Response(
                {"error": "Missing organization_id or s3_key"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        org = get_object_or_404(Organization, id=org_id)
        onboarding_data = org.onboarding_data or {}
        steps_data = onboarding_data.get("steps_data", {})
        context_data = steps_data.get("context", {})
        files = context_data.get("files", [])

        updated_files = [f for f in files if f.get("key") != s3_key]
        context_data["files"] = updated_files
        steps_data["context"] = context_data
        onboarding_data["steps_data"] = steps_data
        org.onboarding_data = onboarding_data
        org.save(update_fields=["onboarding_data"])

        return Response({"status": "file deleted"}, status=200)


class TeamInviteView(APIView):
    def post(self, request):
        data = request.data
        org_id = data.get("organization_id")
        emails = data.get("email")


        if not org_id or not emails or not isinstance(emails, list):
            return Response(
                {"error": "organization_id and a list of inviteesEmails are required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        org = get_object_or_404(Organization, id=org_id)
        onboarding_data = org.onboarding_data or {}
        steps_data = onboarding_data.get("steps_data", {})
        team_data = steps_data.get("team", {})
        existing_emails = set(team_data.get("emails", []))

        new_emails = set(emails)
        combined_emails = list(existing_emails.union(new_emails))

        team_data["emails"] = combined_emails
        steps_data["team"] = team_data
        onboarding_data["steps_data"] = steps_data
        org.onboarding_data = onboarding_data
        org.save(update_fields=["onboarding_data"])

        return Response({"status": "success"}, status=status.HTTP_201_CREATED)

    def delete(self, request):
        org_id = request.query_params.get("organization_id")
        email = request.query_params.get("email")
        if not org_id or not email:
            return Response(
                {"error": "organization_id and email are required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        org = get_object_or_404(Organization, id=org_id)
        onboarding_data = org.onboarding_data or {}
        steps_data = onboarding_data.get("steps_data", {})
        team_data = steps_data.get("team", {})
        emails = team_data.get("emails", [])

        if email in emails:
            emails.remove(email)
            team_data["emails"] = emails
            steps_data["team"] = team_data
            onboarding_data["steps_data"] = steps_data
            org.onboarding_data = onboarding_data
            org.save(update_fields=["onboarding_data"])

        return Response({"status": "success"}, status=status.HTTP_200_OK)


class CompleteSetupView(APIView):
    authentication_classes = []
    permission_classes = []

    def post(self, request):
        try:
            data = request.data
            organization_id = data.get("organization_id")
            if not organization_id:
                return JsonResponse(
                    {"error": "organization_id is required."}, status=400
                )

            # Fetch the organization instance
            try:
                org = get_object_or_404(Organization, id=organization_id)
            except Organization.DoesNotExist:
                return JsonResponse({"error": "Organization not found."}, status=404)

            onboarding_data = org.onboarding_data or {}
            review_data = onboarding_data.get("steps_data", {}).get("review", {})
            tools_data = review_data.get("tools", [])
            agents_data = review_data.get("agents", [])
            procedures_data = review_data.get("procedures", [])
            team_data = review_data.get("team", [])
            files_data = review_data.get("files", [])
            # Call utility functions
            created_tool_map = create_tools_agents(tools_data, organization_id)
            created_agents_map = create_tools_agents(agents_data, organization_id)
            tools_map = created_tool_map | created_agents_map
            # create_procedures_for_onboarding(procedures_data, tools_map, organization_id)
            create_procedures_for_onboarding_v2(tools_data, tools_map, organization_id)
            create_datastores(files_data, organization_id)
            mark_onboarding_complete(organization_id)

            return JsonResponse(
                {
                    "status": "Sucess",
                    "teams_data": team_data
                },
                status=201,
            )

        except json.JSONDecodeError:
            return JsonResponse({"error": "Invalid JSON."}, status=400)
        except Exception as e:
            return JsonResponse({"error": str(e)}, status=500)


class GetTemplateProcedureDetailsView(APIView):
    def get(self, request):
        procedure_id = request.GET.get("procedure_id")
        if not procedure_id:
            return Response({"error": "Missing procedure_id"}, status=400)

        try:
            details = get_template_procedure_details(procedure_id)
            return Response(details, status=200)
        except Exception as e:
            return Response({"error": str(e)}, status=500)



class GetToolTemplatesWithMinimalInfoView(APIView):
    def get(self, request):
        component_type = request.GET.get("componentType")
        logger.debug("Inside Missing ToolType")

        if not component_type:
            return Response({"error": "Missing componentType"}, status=400)

        return get_minimal_tool_template_info(component_type)


class UpdateProceduresDetails(APIView):
    def put(self, request, procedure_id, *args, **kwargs):
        try:
            org_id = request.data.get("organization_id")
            updated_fields = request.data.get("updated_fields")

            if not org_id:
                return Response({"error": "Missing 'organization_id'"}, status=status.HTTP_400_BAD_REQUEST)
            if not updated_fields or not isinstance(updated_fields, dict):
                return Response({"error": "Missing or invalid 'updated_fields'"}, status=status.HTTP_400_BAD_REQUEST)

            try:
                org = Organization.objects.get(id=org_id)
            except Organization.DoesNotExist:
                return Response({"error": "Organization not found"}, status=status.HTTP_404_NOT_FOUND)

            update_procedure_details(org, procedure_id, updated_fields)

            return Response({"message": "usedInProcedures updated successfully"}, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class RemoveTaskFromProcedureView(APIView):
    def delete(self, request, procedure_id: int, task_id: int):
        logger.debug("Inside logger function")
        org_id = request.query_params.get("organization_id")
        if not org_id:
            return Response({"error": "Missing organization_id"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            org = Organization.objects.get(id=org_id)
        except Organization.DoesNotExist:
            return Response({"error": "Organization not found"}, status=status.HTTP_404_NOT_FOUND)

        remove_task_from_procedure(org, int(procedure_id), int(task_id))
        return Response(status=status.HTTP_204_NO_CONTENT)


class AddTaskToProcedureView(APIView):
    def post(self, request, procedure_id: int):
        org_id = request.data.get("organization_id")
        if not org_id:
            return Response({"error": "Missing organization_id"}, status=400)

        try:
            org = Organization.objects.get(id=org_id)
        except Organization.DoesNotExist:
            return Response({"error": "Organization not found"}, status=404)

        try:
            logger.debug("1284")
            add_task_to_procedure(org, procedure_id, request.data)
            return Response({"message": "New Task created successfully"}, status=status.HTTP_200_OK)
        except ValueError as ve:
            return Response({"error": str(ve)}, status=403)
        except Exception as e:
            return Response({"error": str(e)}, status=500)

class UpdateUnconfiguredTaskToolsView(APIView):
    def patch(self, request, procedure_id, task_id):
        specialist = request.data.get("specialist")
        tool_ids = request.data.get("tools", [])
        org_id = request.data.get("organization_id")



        try:
            org = Organization.objects.get(id=org_id)
        except Organization.DoesNotExist:
            return Response({"error": "Organization not found"}, status=404)

        update_tools_for_task_details(
            org=org,
            procedure_id=procedure_id,
            task_id=task_id,
            tool_ids=tool_ids,
            specialist=specialist
        )

        return Response({"message": "Tools updated successfully."}, status=200)

class UpdateTaskDescriptionView(APIView):
    def patch(self, request, procedure_id, task_id, *args, **kwargs):
        org_id = request.data.get("organization_id")
        description = request.data.get("description")

        if not org_id or description is None:
            return Response({"error": "Missing required fields"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            org = Organization.objects.get(id=org_id)
        except Organization.DoesNotExist:
            return Response({"error": "Organization not found"}, status=status.HTTP_404_NOT_FOUND)

        updated = update_task_fields(org, int(procedure_id), int(task_id), {"description": description})

        if updated:
            return Response({"message": "Task description updated successfully"}, status=status.HTTP_200_OK)
        else:
            return Response({"error": "Task or procedure not found"}, status=status.HTTP_404_NOT_FOUND)


class UpdateTaskBasicDetailsView(APIView):
    def patch(self, request, procedure_id, task_id, *args, **kwargs):
        org_id = request.data.get("organization_id")
        updated_fields = request.data.get("updated_fields")

        if not org_id or not updated_fields:
            return Response({"error": "Missing 'organization_id' or 'updated_fields'"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            org = Organization.objects.get(id=org_id)
        except Organization.DoesNotExist:
            return Response({"error": "Organization not found"}, status=status.HTTP_404_NOT_FOUND)

        updated = update_task_fields(org, int(procedure_id), int(task_id), updated_fields)

        if updated:
            return Response({"message": "Task basic details updated successfully"}, status=status.HTTP_200_OK)
        else:
            return Response({"error": "Task or procedure not found"}, status=status.HTTP_404_NOT_FOUND)

