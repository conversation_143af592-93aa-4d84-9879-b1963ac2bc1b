from core.models import ToolTemplate
from core.models import Tool
from django.db import models
import json
from django.utils import timezone
from core.models import (TemplateTasksRelationship,
                         TasksRelationship,
                         TemplateTask,
                         TemplateTasksTemplateToolsRelationship,
                         TemplateProcedure,
                         Task,
                         Procedure,
                         TasksToolsRelationship,
                         Tool,
                         Organization,
                         ToolTemplate,
                         TemplateTasksPublicToolsRelationship
                         )
from django.db import connection
import logging
from organization.utils.kms_encryption import encrypt_secret, decrypt_secret
from django.shortcuts import get_object_or_404
from core.enums import ToolType
from collections import defaultdict
from typing import List, Dict, Any
from collections import defaultdict
from django.db import connection
from django.utils import timezone




logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


def decrypt_tool_credentials(tool_id: int) -> dict:
    """
    This function is used only for testing purpose
    """
    try:
        tool = Tool.objects.get(id=tool_id)
        spec_str = tool.spec
        if not spec_str:
            print(f"No spec found for Tool ID: {tool_id}")
            return {}

        spec = json.loads(spec_str)
        credentials = spec.get("credentials", {})
        encrypted_data = credentials.get("data")

        if not encrypted_data:
            print(f"No encrypted credentials found in Tool ID: {tool_id}")
            return {}

        decrypted_data = decrypt_secret(encrypted_data)
        logger.debug(f"Decrypted credentials for Tool ID {tool_id}: {decrypted_data}")
        return decrypted_data

    except Tool.DoesNotExist:
        print(f"Tool with ID {tool_id} does not exist.")
        return {}
    except Exception as e:
        print(f"Error processing Tool ID {tool_id}: {str(e)}")
        return {}


def create_tools_agents(tools_data, organization):
    tool_ids = [tool['id'] for tool in tools_data]
    created_tool_map = {}  # display_name -> tool.id

    try:
        templates = ToolTemplate.objects.filter(id__in=tool_ids)
        template_map = {template.id: template for template in templates}

        for tool_data in tools_data:
            tool_id = tool_data['id']
            template = template_map.get(tool_id)
            now = timezone.now()

            if not template:
                print(f"Template not found for Tool ID: {tool_id}")
                continue

            configuration = tool_data.get('configuration')
            if configuration:
                config_data = configuration.get("data")
                if  config_data and isinstance(config_data, dict) and config_data != {}:
                    logger.debug("encrypt data")
                    encrypted_data = encrypt_secret(configuration['data'])
                    template_spec = template.spec or {}
                    credentials = template_spec.get('credentials', {})
                    credentials['data'] = encrypted_data
                    template_spec['credentials'] = credentials

            tool = Tool(
                configured_by=organization,
                template=template,
                display_name=template.display_name,
                display_description=template.display_description,
                llm_type=template.llm_type,
                configurable_spec_fields=template.configurable_spec_fields or {},
                spec=json.dumps(template.spec or {}),
                img_url=template.img_url,
                type=template.type,
                disabled_in_organizations='{}',
                created_at=now,
                updated_at=now,
            )
            tool.save()

            created_tool_map[tool.display_name] = tool.id


    except Exception as e:
        print(f"Error in db: {str(e)}")

    return created_tool_map



def create_procedures_for_onboarding(procedures_data, created_tool_map, organization_id):
    try:
        template_procedure_ids = [tool['id'] for tool in procedures_data]

        template_to_task_map = {}  # {template_task_id: new_task_id}
        for template_proc_id in template_procedure_ids:
            template_proc = TemplateProcedure.objects.get(id=template_proc_id)

            # Step 1: Create Procedure
            new_proc = Procedure.objects.create(
                name=template_proc.name,
                llm_description=template_proc.llm_description,
                ui_description=template_proc.ui_description,
                long_term_memory=False,
                inputs=template_proc.inputs,
                organization_id=organization_id,
                created_at=timezone.now(),
                updated_at=timezone.now(),
                is_enabled=True,
                deleted=False

            )


            # Step 2: Create Tasks
            template_tasks = TemplateTask.objects.filter(template_procedure=template_proc)
            import ast  # to safely evaluate stringified lists if necessary
            for ttask in template_tasks:
                inputs_raw = ttask.inputs_names
                task = Task.objects.create(
                    procedure=new_proc,
                    prompt=ttask.prompt,
                    ui_order=ttask.ui_order,
                    created_at=timezone.now(),
                    updated_at=timezone.now(),
                    deleted=False,

                )
                template_to_task_map[ttask.id] = task.id

            rels = TemplateTasksRelationship.objects.values('a_id', 'b_id').filter(
                a_id__in=template_to_task_map.keys()
            )

            # Step 2: Map to new task IDs
            rel_rows = []
            for rel in rels:
                a_new = template_to_task_map.get(rel['a_id'])
                b_new = template_to_task_map.get(rel['b_id'])
                if a_new and b_new:
                    rel_rows.append((a_new, b_new))


            # Step 3: Insert directly into _TasksRelationship using raw SQL
            with connection.cursor() as cursor:
                cursor.executemany(
                    'INSERT INTO "_TasksRelationship" ("A", "B") VALUES (%s, %s) ON CONFLICT DO NOTHING',
                    rel_rows
                )


            # Step 4: Create Task-Tool Relationships
            ttool_rels = TemplateTasksTemplateToolsRelationship.objects.values('a_id', 'b_id').filter(
                a_id__in=template_to_task_map.keys()
            )

            # Step 1: Load all relevant TemplateTool entries once
            template_tool_ids = {rel['b_id'] for rel in ttool_rels}
            template_tools = ToolTemplate.objects.filter(id__in=template_tool_ids)
            template_tool_map = {tool.id: tool.display_name for tool in template_tools}

            # Step 2: Map relationships using template_to_task_map and created_tool_map
            rel_rows = []
            for rel in ttool_rels:
                task_id = template_to_task_map.get(rel['a_id'])

                template_tool_id = rel['b_id']
                tool_display_name = template_tool_map.get(template_tool_id)
                tool_id = created_tool_map.get(tool_display_name)  # final real tool ID

                if task_id and tool_id:
                    rel_rows.append((task_id, tool_id))


            # Step 3: Insert into _TasksToolsRelationship
            with connection.cursor() as cursor:
                cursor.executemany(
                    'INSERT INTO "_TasksToolsRelationship" ("A", "B") VALUES (%s, %s) ON CONFLICT DO NOTHING',
                    rel_rows
                )

            # Step 5: Handle public tools from _TemplateTasksPublicToolsRelationship
            public_tool_rels = TemplateTasksPublicToolsRelationship.objects.values('a_id', 'b_id').filter(
                a_id__in=template_to_task_map.keys()
            )

            # Step 1: Get all tool template IDs used in the relationship
            public_template_ids = {rel['b_id'] for rel in public_tool_rels}

            # Step 2: Find actual Tool entries (configured_by='public') based on template_id
            public_tools = Tool.objects.filter(id__in=public_template_ids, configured_by='public')
            template_id_to_tool_id = {t.id: t.id for t in public_tools}


            # Step 3: Build task-to-tool relationships for public tools
            public_rel_rows = []
            for rel in public_tool_rels:
                task_id = template_to_task_map.get(rel['a_id'])
                tool_id = template_id_to_tool_id.get(rel['b_id'])
                if task_id and tool_id:
                    public_rel_rows.append((task_id, tool_id))

            # Step 4: Insert these into _TasksToolsRelationship as well
            with connection.cursor() as cursor:
                cursor.executemany(
                    'INSERT INTO "_TasksToolsRelationship" ("A", "B") VALUES (%s, %s) ON CONFLICT DO NOTHING',
                    public_rel_rows
                )

    except Exception as e:
        print(f"Error in procedures: {str(e)}")

    print("✅ Procedures and tasks created from templates.")


def get_template_procedure_details(procedure_id: int) -> dict:
    template_proc = get_object_or_404(TemplateProcedure, id=procedure_id)

    # Step 1: Inputs
    raw_inputs = template_proc.inputs or []
    inputs = [
        {
            "name": i.get("Name", ""),
            "description": i.get("Description", "")
        }
        for i in raw_inputs if isinstance(i, dict)
    ]

    # Step 2: Tasks and IDs
    tasks = TemplateTask.objects.filter(template_procedure=template_proc).order_by("ui_order")
    task_ids = [t.id for t in tasks]

    # Step 3: Tool mappings (TemplateTask -> ToolTemplate)
    tool_rels = TemplateTasksTemplateToolsRelationship.objects.filter(a_id__in=task_ids).values("a_id", "b_id")
    tool_ids = set(rel["b_id"] for rel in tool_rels)
    tool_usage_counts = defaultdict(int)
    task_tool_map = defaultdict(list)

    for rel in tool_rels:
        task_id = rel["a_id"]
        tool_id = rel["b_id"]
        task_tool_map[task_id].append(tool_id)
        tool_usage_counts[tool_id] += 1

    # Step 4: Parent task relationships
    parent_rels = TemplateTasksRelationship.objects.filter(a_id__in=task_ids).values("a_id", "b_id")
    parent_map = defaultdict(list)
    for rel in parent_rels:
        parent_map[rel["a_id"]].append(rel["b_id"])

    # Step 5: ToolTemplate metadata
    tools = ToolTemplate.objects.filter(id__in=tool_ids)
    tool_lookup = {tool.id: tool for tool in tools}

    # Step 6: childrenUnconfiguredTasks
    children_tasks = []
    for task in tasks:
        template_tools = [
            {
                "id": str(tid),
                "type": tool_lookup[tid].type,
                "displayName": tool_lookup[tid].display_name
            }
            for tid in task_tool_map.get(task.id, [])
            if tid in tool_lookup
        ]

        children_tasks.append({
            "id": task.id,
            "tools": template_tools,
            "templateTools": template_tools,
            "inputsNames": task.inputs_names or [],
            "UIOrder": task.ui_order,
            "prompt": task.prompt,
            "description": task.description or "",
            "parentTasks": parent_map.get(task.id, [])
        })

    # Step 7: Selection summary
    toolTemplateSelectionThatNeedsAttention = [
        {
            "id": tool.id,
            "type": tool.type,
            "name": tool.display_name,
            "noOfConfiguredTools": tool_usage_counts[tool.id]
        }
        for tool in tools
    ]

    return {
        "unconfiguredProcedureDetails": {
            "id": template_proc.id,
            "name": template_proc.name,
            "llmDescription": template_proc.llm_description,
            "uiDescription": template_proc.ui_description,
            "inputs": inputs,
            "toolTemplateSelectionThatNeedsAttention": toolTemplateSelectionThatNeedsAttention,
            "checkedToolTemplateSelection": [],
            "childrenUnconfiguredTasks": children_tasks
        }
    }


def mark_onboarding_complete(organization_id):
    try:
        updated = Organization.objects.filter(id=organization_id).update(onboarding_complete=True)
        if updated:
            print(f"✅ Onboarding marked complete for organization {organization_id}.")
        else:
            print(f"⚠️ No organization found with id {organization_id}.")
    except Exception as e:
        print(f"❌ Failed to update onboarding_complete: {str(e)}")



def create_procedures_for_onboarding_v2(tools_data: list, created_tool_map: dict, organization_id: str):
    try:
        # Step 1: Flatten and extract unique procedure entries from "usedInProcedures"
        procedure_details_seen = set()
        condensed_procedures = []

        for tool in tools_data:
            used_procs = tool.get("usedInProcedures", [])
            for proc in used_procs:
                proc_detail = proc.get("unconfiguredProcedureDetails")
                proc_id = proc_detail.get("id") if proc_detail else None
                if proc_detail and proc_id not in procedure_details_seen:
                    procedure_details_seen.add(proc_id)
                    condensed_procedures.append(proc_detail)

        # Step 2: Begin procedure creation from condensed data
        for proc_detail in condensed_procedures:
            template_to_task_map = {}

            raw_inputs = proc_detail.get("inputs", [])
            #BA-1809
            capitalized_inputs = [
                {
                    "Name": inp.get("name", ""),
                    "Description": inp.get("description", "")
                }
                for inp in raw_inputs
            ]

            # Create Procedure
            new_proc = Procedure.objects.create(
                name=proc_detail["name"],
                llm_description=proc_detail.get("llmDescription"),
                ui_description=proc_detail.get("uiDescription"),
                long_term_memory=False,
                inputs=capitalized_inputs,
                organization_id=organization_id,
                created_at=timezone.now(),
                updated_at=timezone.now(),
                is_enabled=True,
                deleted=False
            )

            # Create Tasks
            for task in proc_detail.get("childrenUnconfiguredTasks", []):
                logger.debug(task.get("prompt"))
                task_obj = Task.objects.create(
                    procedure=new_proc,
                    prompt=task.get("prompt"),
                    ui_order=task.get("UIOrder", 0),
                    created_at=timezone.now(),
                    updated_at=timezone.now(),
                    deleted=False
                )
                template_to_task_map[task["id"]] = task_obj.id

            # Parent-child relationships
            rel_rows = []
            for task in proc_detail.get("childrenUnconfiguredTasks", []):
                for parent_id in task.get("parentTasks", []):
                    a_new = template_to_task_map.get(task["id"])
                    b_new = template_to_task_map.get(parent_id)
                    if a_new and b_new:
                        rel_rows.append((a_new, b_new))

            with connection.cursor() as cursor:
                cursor.executemany(
                    'INSERT INTO "_TasksRelationship" ("A", "B") VALUES (%s, %s) ON CONFLICT DO NOTHING',
                    rel_rows
                )

            # Task-tool relationships
            tool_rel_rows = []
            for task in proc_detail.get("childrenUnconfiguredTasks", []):
                task_id = template_to_task_map.get(task["id"])
                for tool in task.get("tools", []):
                    tool_id = created_tool_map.get(tool["displayName"])
                    if task_id and tool_id:
                        tool_rel_rows.append((task_id, tool_id))

            with connection.cursor() as cursor:
                cursor.executemany(
                    'INSERT INTO "_TasksToolsRelationship" ("A", "B") VALUES (%s, %s) ON CONFLICT DO NOTHING',
                    tool_rel_rows
                )

            # Handle public tools
            public_tool_rels = TemplateTasksPublicToolsRelationship.objects.filter(
                a_id__in=template_to_task_map.keys()
            ).values("a_id", "b_id")

            public_template_ids = {rel["b_id"] for rel in public_tool_rels}
            public_tools = Tool.objects.filter(id__in=public_template_ids, configured_by='public')
            template_id_to_tool_id = {t.id: t.id for t in public_tools}

            public_rel_rows = []
            for rel in public_tool_rels:
                task_id = template_to_task_map.get(rel["a_id"])
                tool_id = template_id_to_tool_id.get(rel["b_id"])
                if task_id and tool_id:
                    public_rel_rows.append((task_id, tool_id))

            with connection.cursor() as cursor:
                cursor.executemany(
                    'INSERT INTO "_TasksToolsRelationship" ("A", "B") VALUES (%s, %s) ON CONFLICT DO NOTHING',
                    public_rel_rows
                )

        print("✅ Procedures and tasks created successfully from V2 template flow")

    except Exception as e:
        logger.debug(f"❌ Error in V2 create_procedures_for_onboarding: {str(e)}")
