from typing import Dict, Any
from copy import deepcopy
import logging
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from typing import List
from organization.utils.onboarding_creation import get_minimal_tool_template_info

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


def update_procedure_details(org, procedure_id: int, updated_fields: Dict[str, Any]):
    onboarding_data = org.onboarding_data or {}
    steps_data = onboarding_data.get("steps_data", {})
    tools_data = steps_data.get("tools", {"required": [], "all": []})

    all_tools = tools_data.get("required", []) + tools_data.get("all", [])

    # Iterate over all tools and update matching procedures
    for tool in all_tools:
        updated_used_in_procs = []

        for proc_entry in tool.get("usedInProcedures", []):
            proc_detail = proc_entry.get("unconfiguredProcedureDetails")
            if proc_detail and proc_detail.get("id") == procedure_id:
                # Update only allowed fields
                for key in updated_fields:
                    proc_detail[key] = updated_fields[key]

            updated_used_in_procs.append({
                "unconfiguredProcedureDetails": proc_detail
            })

        tool["usedInProcedures"] = updated_used_in_procs

    # Rebuild required and all tool lists
    updated_required = [tool for tool in all_tools if tool["id"] in {t["id"] for t in tools_data.get("required", [])}]
    updated_all = [tool for tool in all_tools if tool["id"] in {t["id"] for t in tools_data.get("all", [])}]

    # Persist changes
    steps_data["tools"] = {
        "required": updated_required,
        "all": updated_all
    }
    onboarding_data["steps_data"] = steps_data
    org.onboarding_data = onboarding_data
    org.save(update_fields=["onboarding_data"])

def update_tools_for_task_details(org, procedure_id: int, task_id: int, tool_ids: List[str], specialist: str = None):
    onboarding_data = org.onboarding_data or {}
    steps_data = onboarding_data.get("steps_data", {})
    procedures = steps_data.get("procedures", {}).get("all", [])
    tools_data = steps_data.get("tools", {})
    required_tools = tools_data.get("required", [])
    all_tools = tools_data.get("all", [])

    # 🔹 Build tool lookup (required + all)
    tool_lookup = {str(t["id"]): t for t in required_tools + all_tools}

    # 🔹 Build specialist (coordinator) lookup
    coordinator_response = get_minimal_tool_template_info("coordinator")
    coordinator_items = coordinator_response.data.get("publicSpecialists", [])
    specialist_lookup = {str(s["id"]): s for s in coordinator_items}

    # 🔹 Build new_tools list
    new_tools = []

    for tid in tool_ids:
        t = tool_lookup.get(str(tid))
        if t:
            new_tools.append({
                "id": str(t["id"]),
                "type": t["type"],
                "displayName": t["name"],
            })

    # 🔹 Add specialist if valid
    if specialist:
        specialist_data = specialist_lookup.get(str(specialist))
        if specialist_data:
            new_tools.append({
                "id": specialist_data["id"],
                "type": "coordinator",
                "displayName": specialist_data["name"],
            })


    # Iterate through all required tools' usedInProcedures
    for tool in required_tools:
        used_procs = tool.get("usedInProcedures", [])

        for proc in used_procs:
            proc_data = proc.get("unconfiguredProcedureDetails")
            if proc_data and proc_data.get("id") == procedure_id:
                for task in proc_data.get("childrenUnconfiguredTasks", []):
                    if task.get("id") == task_id:
                        task["tools"] = new_tools
                        if specialist:
                            task["specialist"] = specialist
                        logger.debug(f"✅ Updated tool {tool['id']} -> task {task_id} in procedure {procedure_id}")

    # Save updated onboarding data
    onboarding_data["steps_data"] = steps_data
    org.onboarding_data = onboarding_data
    org.save(update_fields=["onboarding_data"])


def diff_enabled_vs_cached_proc_ids(tools_data, enabled_proc_ids):
    """
    Compare enabled_proc_ids with cached procedure IDs from tools_data["required"]
    and return newly added and removed procedure IDs.
    """
    cached_proc_ids = set()

    for tool in tools_data.get("required", []):
        for proc_info in tool.get("usedInProcedures", []):
            proc_detail = proc_info.get("unconfiguredProcedureDetails")
            if proc_detail and "id" in proc_detail:
                cached_proc_ids.add(proc_detail["id"])

    enabled_proc_ids_set = set(enabled_proc_ids)
    newly_added_proc_ids = enabled_proc_ids_set - cached_proc_ids
    removed_proc_ids = cached_proc_ids - enabled_proc_ids_set

    return newly_added_proc_ids, removed_proc_ids

def update_task_fields(org, procedure_id: int, task_id: int, fields_to_update: Dict[str, Any]):
    onboarding_data = org.onboarding_data or {}
    steps_data = onboarding_data.get("steps_data", {})
    tools_data = steps_data.get("tools", {})
    required_tools = tools_data.get("required", [])

    updated = False

    for tool in required_tools:
        for proc in tool.get("usedInProcedures", []):
            proc_data = proc.get("unconfiguredProcedureDetails")
            if proc_data and proc_data.get("id") == procedure_id:
                for task in proc_data.get("childrenUnconfiguredTasks", []):
                    if task.get("id") == task_id:
                        for key, value in fields_to_update.items():
                            task[key] = value
                        updated = True

    if updated:
        onboarding_data["steps_data"] = steps_data
        org.onboarding_data = onboarding_data
        org.save(update_fields=["onboarding_data"])

    return updated
