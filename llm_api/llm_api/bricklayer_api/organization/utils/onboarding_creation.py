from django.utils.timezone import now
from typing import Dict, Any
import logging
from django.db.models import Q
from rest_framework.response import Response
from core.enums import ToolType

from core.models import (Tool,
                         ToolTemplate,
                         TemplateProcedure,
                         TemplateTasksTemplateToolsRelationship,
                         TemplateTask,
                         TemplateTasksPublicToolsRelationship)


logger = logging.getLogger(__name__)


def add_task_to_procedure(org, procedure_id: int, task_data: Dict[str, Any]):

    logger.debug("Add task to procedure %s", procedure_id)

    onboarding_data = org.onboarding_data or {}
    steps_data = onboarding_data.get("steps_data", {})
    tools_data = steps_data.get("tools", {"required": [], "all": []})

    all_tools = tools_data.get("required", []) + tools_data.get("all", [])

    tool_ids_used = set(task_data.get("tools", []))
    authorized_tool_ids = set()

    logger.debug("tool_ids_used")
    logger.debug(f"tool_ids_used {tool_ids_used}")
    # ✅ Validate tool ownership
    for tool in all_tools:
        logger.debug(f"{tool['id']}, {tool.get('configured_by')}, {tool.get('configured_by')}")
        if tool["id"] in tool_ids_used:
            authorized_tool_ids.add(tool["id"])

    logger.debug(f"authorized_tool_ids {authorized_tool_ids}")
    if len(authorized_tool_ids) != len(tool_ids_used):
        raise ValueError("You have no access to one or more tools listed")

    # ✅ Generate task ID and prepare structure
    task_id = task_data.get("id") or int(now().timestamp())
    new_task = {
        "id": task_id,
        "prompt": task_data.get("prompt"),
        "description": task_data.get("description", ""),
        "inputsNames": task_data.get("inputs", []),
        "UIOrder": task_data.get("UIOrder"),  # can be None
        "tools": [
            tool for tool in task_data.get("tools_full", [])
            if tool["id"] in authorized_tool_ids
        ],
        "parentTasks": task_data.get("parentTasks", [])
    }

    # ✅ Find the target procedure (once only)
    procedure_found = False

    for tool in all_tools:
        for procedure in tool.get("usedInProcedures", []):
            if procedure.get("id") == procedure_id:
                procedure_found = True
                children = procedure.setdefault("childrenTasks", [])

                # ✅ Calculate UIOrder only once based on this procedure
                if new_task["UIOrder"] is None:
                    existing_orders = [t.get("UIOrder", 0) for t in children]
                    new_task["UIOrder"] = (max(existing_orders) + 1) if existing_orders else 1

                children.append(new_task)
                break  # No need to continue scanning once added

        if procedure_found:
            break

    if not procedure_found:
        raise ValueError(f"Procedure {procedure_id} not found in any tool")

    # ✅ Save back
    steps_data["tools"] = {
        "required": [tool for tool in all_tools if tool["id"] in [t["id"] for t in tools_data.get("required", [])]],
        "all": [tool for tool in all_tools if tool["id"] in [t["id"] for t in tools_data.get("all", [])]],
    }
    onboarding_data["steps_data"] = steps_data
    org.onboarding_data = onboarding_data
    org.save(update_fields=["onboarding_data"])

    logger.debug("✅ Task %s added to procedure %s", task_id, procedure_id)


def get_minimal_tool_template_info(component_type: str) -> Response:
    if component_type == ToolType.COORDINATOR:
        tools = ToolTemplate.objects.filter(type=ToolType.COORDINATOR)

        public_specialists = []
        configured_specialists = []

        for t in tools:
            tool_data = {
                "id": str(t.id),
                "name": t.display_name,
                "description": t.display_description,
                "children": [],
            }
            if t.configured_by == "public":
                public_specialists.append(tool_data)
            else:
                configured_specialists.append(tool_data)

        return Response({
            "publicSpecialists": public_specialists,
            "configuredSpecialists": configured_specialists,
        })

    # Non-coordinator tools
    template_tools = ToolTemplate.objects.filter(~Q(type=ToolType.COORDINATOR))

    public_plugins = [
        {
            "id": str(t.id),
            "name": t.display_name,
            "description": t.display_description,
            "type": t.type,
        }
        for t in template_tools
        if t.configured_by == "public" and t.type == ToolType.PLUGIN
    ]

    public_reporters = [
        {
            "id": str(t.id),
            "name": t.display_name,
            "description": t.display_description,
            "type": t.type,
        }
        for t in template_tools
        if t.configured_by == "public" and t.type == ToolType.REPORTER
    ]

    public_data_sources = [
        {
            "id": str(t.id),
            "name": t.display_name,
            "description": t.display_description,
            "type": t.type,
        }
        for t in Tool.objects.filter(type=ToolType.PUBLIC_DATASOURCE, configured_by="public")
    ]

    public_datastores = [
        {
            "id": str(t.id),
            "name": t.display_name,
            "description": t.display_description,
            "type": t.type,
        }
        for t in Tool.objects.filter(type=ToolType.DATASTORE, configured_by="public")
    ]

    return Response({
        "publicPlugins": public_plugins,
        "publicDataSources": public_data_sources,
        "publicDatastores": public_datastores,
        "publicReporters": public_reporters,
        "configuredDatastores": [],
        "configuredPlugins": [],
        "configuredServices": [],
        "configuredReporters": [],
    })


def collect_required_tools_and_used_by_map_from_db(enabled_proc_ids):
    required_tool_ids = set()
    used_by_map = {}

    for proc in TemplateProcedure.objects.filter(id__in=enabled_proc_ids):
        tasks = TemplateTask.objects.filter(template_procedure=proc)
        for task in tasks:
            template_ids = TemplateTasksTemplateToolsRelationship.objects.filter(
                a=task
            ).values_list("b_id", flat=True)
            for tid in template_ids:
                required_tool_ids.add(tid)
                used_by_map.setdefault(tid, set()).add(proc.name)

            public_ids = TemplateTasksPublicToolsRelationship.objects.filter(
                a=task
            ).values_list("b_id", flat=True)
            for pid in public_ids:
                required_tool_ids.add(pid)
                used_by_map.setdefault(pid, set()).add(proc.name)

    return required_tool_ids, used_by_map

