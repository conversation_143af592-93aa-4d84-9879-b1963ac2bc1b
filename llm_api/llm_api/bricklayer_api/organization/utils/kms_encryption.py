import os
import boto3
import base64
import json

KMS_REGION = os.getenv("REGION", "us-east-1")
KMS_KEY_ID = os.getenv("KMS_KEY")

if not KMS_KEY_ID:
    raise ValueError("Missing AWS_KMS_KEY_ID in environment variables")

# Initialize KMS client
kms = boto3.client("kms", region_name=KMS_REGION)

def encrypt_secret(secret_obj: dict) -> str:

    plaintext = json.dumps(secret_obj).encode("utf-8")
    response = kms.encrypt(KeyId=KMS_KEY_ID, Plaintext=plaintext)
    ciphertext_blob = response['CiphertextBlob']
    return base64.b64encode(ciphertext_blob).decode("utf-8")

def decrypt_secret(ciphertext_b64: str) -> dict:
    ciphertext_blob = base64.b64decode(ciphertext_b64)
    response = kms.decrypt(CiphertextBlob=ciphertext_blob)
    decrypted = response['Plaintext']
    return json.loads(decrypted.decode("utf-8"))