def remove_task_from_procedure(org, procedure_id: int, task_id: int):
    onboarding_data = org.onboarding_data or {}
    steps_data = onboarding_data.get("steps_data", {})
    tools_data = steps_data.get("tools", {"required": [], "all": []})

    all_tools = tools_data.get("required", []) + tools_data.get("all", [])

    for tool in all_tools:
        used_procs = tool.get("usedInProcedures", [])

        for proc_entry in used_procs:
            proc_detail = proc_entry.get("unconfiguredProcedureDetails")
            if proc_detail and proc_detail.get("id") == procedure_id:
                original_tasks = proc_detail.get("childrenUnconfiguredTasks", [])
                updated_tasks = [
                    task for task in original_tasks if task.get("id") != task_id
                ]
                if len(updated_tasks) < len(original_tasks):
                    proc_detail["childrenUnconfiguredTasks"] = updated_tasks
                    print(f"✅ Removed task {task_id} from procedure {procedure_id} in tool {tool['id']}")

    # ✅ Save updated tools structure back
    tool_map = {tool["id"]: tool for tool in all_tools}

    updated_required = [tool_map[t["id"]] for t in tools_data.get("required", []) if t["id"] in tool_map]
    updated_all = [tool_map[t["id"]] for t in tools_data.get("all", []) if t["id"] in tool_map]

    steps_data["tools"] = {
        "required": updated_required,
        "all": updated_all,
    }
    onboarding_data["steps_data"] = steps_data
    org.onboarding_data = onboarding_data
    org.save(update_fields=["onboarding_data"])
