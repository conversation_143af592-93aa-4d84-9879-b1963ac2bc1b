# Generated by Django 5.2 on 2025-04-14 07:34

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0003_organization_onboarding_complete_and_more'),
        ('organization', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='OrganizationGoal',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('raw_goals', models.JSONField(blank=True, default=list)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('organization', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='core.organization')),
                ('suggestions', models.ManyToManyField(blank=True, to='organization.goalsuggestion')),
            ],
        ),
    ]
