from core.models import Organization
from django.db import models


class GoalSuggestion(models.Model):
    name = models.CharField(max_length=100)
    description = models.TextField()

    def __str__(self):
        return self.name


class OrganizationGoal(models.Model):
    organization = models.OneToOneField(Organization, on_delete=models.CASCADE)
    raw_goals = models.JSONField(default=list, blank=True)  # List of free-form goals
    suggestions = models.ManyToManyField(GoalSuggestion, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Goals for {self.organization.business_name}"
