from django.urls import path
from organization.views import (
    AddTaskToProcedureView,
    ClearOnboardingView,
    CompleteSetupView,
    CompleteStepView,
    GetTemplateProcedureDetailsView,
    GetToolTemplatesWithMinimalInfoView,
    GoalSuggestionCreateView,
    GoalSuggestionsDeleteView,
    GoalSuggestionsView,
    OnboardingBlobDebugView,
    OnboardingContextFilesView,
    OnboardingContextFileUploadView,
    OnboardingSessionView,
    OnboardingStepView,
    RemoveTaskFromProcedureView,
    TeamInviteView,
    ToggleAgentView,
    ToggleProcedureView,
    ToggleToolView,
    ToolConfigureView,
    UpdateProceduresDetails,
    UpdateTaskBasicDetailsView,
    UpdateTaskDescriptionView,
    UpdateUnconfiguredTaskToolsView,
)

urlpatterns = [
    path(
        "session/",
        OnboardingSessionView.as_view(),
        name="onboarding-session",
    ),
    path(
        "step/<str:step>/",
        OnboardingStepView.as_view(),
        name="onboarding-step",
    ),
    # #
    # Get list of admin-configured goal suggestions
    path(
        "goal-suggestions/",
        GoalSuggestionsView.as_view(),
        name="onboarding-goal-suggestions",
    ),
    path(
        "goal-suggestions/add/",
        GoalSuggestionCreateView.as_view(),
        name="goal-suggestion-add",
    ),
    path(
        "goal-suggestions/clear/",
        GoalSuggestionsDeleteView.as_view(),
        name="goal-suggestion-add",
    ),
    path(
        "complete-step/",
        CompleteStepView.as_view(),
        name="onboarding-complete-step",
    ),
    path(
        "procedures/toggle",
        ToggleProcedureView.as_view(),
        name="toggle-procedure",
    ),
    path(
        "tools/configure/",
        ToolConfigureView.as_view(),
        name="onboarding-debug-blob",
    ),
    path(
        "tools/toggle/",
        ToggleToolView.as_view(),
        name="onboarding-debug-blob",
    ),
    path(
        "agents/toggle/",
        ToggleAgentView.as_view(),
        name="onboarding-debug-blob",
    ),
    path(
        "files/upload/",
        OnboardingContextFileUploadView.as_view(),
        name="onboarding-context-file-upload",
    ),
    path(
        "files/",
        OnboardingContextFilesView.as_view(),
        name="onboarding-context-files",
    ),
    path(
        "debug/blob",
        OnboardingBlobDebugView.as_view(),
        name="onboarding-debug-blob",
    ),
    path("clear-onboarding/", ClearOnboardingView.as_view(), name="delete-org"),
    path(
        "team/invite/",
        TeamInviteView.as_view(),
        name="onboarding-context-files",
    ),
    path(
        "complete-setup/",
        CompleteSetupView.as_view(),
        name="onboarding-context-files",
    ),
    path(
        "template-procedure/details/",
        GetTemplateProcedureDetailsView.as_view(),
        name="template-procedure-details",
    ),
    path(
        "minimalInformation/",
        GetToolTemplatesWithMinimalInfoView.as_view(),
        name="template-procedure-details",
    ),
    path(
        "procedures-details/<int:procedure_id>/",
        UpdateProceduresDetails.as_view(),
        name="procedure-details",
    ),
    path(
        "procedures/<int:procedure_id>/task/<int:task_id>",
        RemoveTaskFromProcedureView.as_view(),
    ),
    path(
        "procedures/<int:procedure_id>/task/",
        AddTaskToProcedureView.as_view(),
        name="AddTaskToProcedureView",
    ),
    path(
        "unconfiguredProcedures/<int:procedure_id>/task/<int:task_id>/tools",
        UpdateUnconfiguredTaskToolsView.as_view(),
    ),
    path(
        "unconfiguredProcedures/<int:procedure_id>/task/<int:task_id>/description/",
        UpdateTaskDescriptionView.as_view(),
        name="update-task-description",
    ),
    path(
        "unconfiguredProcedures/<int:procedure_id>/task/<int:task_id>/basicDetails/",
        UpdateTaskBasicDetailsView.as_view(),
        name="update-unconfigured-task-basic-details",
    ),
]
