import csv
import json
import logging
import os
import tempfile
from collections import defaultdict
from typing import Dict, List

import boto3
import requests
from langchain_core.messages import SystemMessage

from llm_api.llm.factory import (
    default_4_gpt_spec_data_json_enabled,
    get_model_from_spec,
)
from llm_api.specs.llm_spec import LLMSpec, LLMType

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

S3_BUCKET = os.environ.get("S3_BUCKET", "blai-dev-data-dev")
s3 = boto3.client("s3")

PUBLIC_BACKEND_URL = os.environ["PUBLIC_BACKEND_URL"].rstrip("/") + "/internal"
HEADERS = {"LLMApiKey": os.environ["LLM_API_KEY"]}


def generate_datastore_description_prompt(file_paths, llm, max_len=150, max_attempts=3):
    headers = None
    sample_data = []

    for fp in file_paths:
        with open(fp, newline="") as f:
            reader = csv.DictReader(f)
            if headers is None:
                headers = reader.fieldnames
            rows = []
            for i, row in enumerate(reader):
                if i >= 10:
                    break
                rows.append(row)
            sample_data.append({"file": os.path.basename(fp), "rows": rows})

    base_prompt = f"""You are given a datastore with several CSV files. All have the same columns.
Your task is to generate a short description (under 150 characters) that explains what kind of data can be found in this datastore.

Headers:
{json.dumps(headers, indent=2)}

Samples from the files:
{json.dumps(sample_data, indent=2)}

Return the result as a JSON object using the following format:
{{
  "name": "short meaningful name for the datastore",
  "description": "short description under 150 characters"
}}
Do not include any other text.
"""

    prompt = base_prompt
    last_description = ""
    shortest_result = {}

    for _ in range(max_attempts):
        response = llm.invoke([SystemMessage(content=prompt)])
        try:
            result_obj = json.loads(response.content.strip())
            description = result_obj.get("description", "").strip()
            if len(description) <= max_len:
                return result_obj
            shortest_result = result_obj
            last_description = description
        except Exception:
            last_description = response.content.strip()

        prompt = f"""{base_prompt}
The previous description was too long: "{last_description}"
It must be under 150 characters. Generate a new shorter one.
Remember: Output only a JSON object in this format:
{{
  "name": "short meaningful name for the datastore",
  "description": "shorter description here"
}}
"""

    return shortest_result


def download_s3_file(key: str) -> str:
    tmp_fd, tmp_path = tempfile.mkstemp(suffix=".csv")
    os.close(tmp_fd)
    s3.download_file(S3_BUCKET, key, tmp_path)
    return tmp_path


def get_csv_headers(file_path: str) -> List[str]:
    with open(file_path, newline="") as csvfile:
        reader = csv.reader(csvfile)
        headers = next(reader, [])
        return headers


def create_datastore(
    name: str, description: str, dtype: str, organization_id: str
) -> str:
    res = requests.post(
        f"{PUBLIC_BACKEND_URL}/api/datastores",
        headers=HEADERS,
        json={
            "name": name,
            "description": description,
            "type": dtype,
            "organizationId": organization_id,
            "userId": "llm-system",  # optional placeholder
        },
    )
    res.raise_for_status()
    return res.json()["dataStoreId"]


def upload_file(
    data_store_id: str,
    dtype: str,
    file_path: str,
    organization_id: str,
    original_filename: str,
):
    if dtype == "pdf":
        mime_type = "application/pdf"
    elif dtype == "csv":
        mime_type = "text/csv"
    else:
        mime_type = "application/octet-stream"

    with open(file_path, "rb") as f:
        res = requests.post(
            f"{PUBLIC_BACKEND_URL}/api/datastores/upload",
            headers=HEADERS,
            data={
                "dataStoreId": data_store_id,
                "type": dtype,
                "organizationId": organization_id,
                "userId": "llm-system",
            },
            files={"dataStore": (original_filename, f, mime_type)},
        )
        res.raise_for_status()


def create_datastores(
    files: List[Dict[str, str]], organization_id: str
) -> List[Dict[str, str]]:
    llm = get_model_from_spec(
        LLMSpec(
            type=LLMType.AzureChatOpenAI,
            data=default_4_gpt_spec_data_json_enabled,
        )
    )

    pdf_files = [f for f in files if f["name"].split(".")[-1].lower() == "pdf"]
    csv_files = [f for f in files if f["name"].split(".")[-1].lower() == "csv"]

    logger.debug(f"PDF files: {pdf_files}")
    logger.debug(f"CSV files: {csv_files}")

    # Handle PDFs
    if pdf_files:
        try:
            pdf_ds_id = create_datastore(
                "Organization Context",
                "Documents describing the organization",
                "pdf",
                organization_id,
            )
            for pdf in pdf_files:
                try:
                    local_path = download_s3_file(pdf["key"])
                    upload_file(
                        pdf_ds_id, "pdf", local_path, organization_id, pdf["name"]
                    )
                    os.remove(local_path)
                except Exception as e:
                    logger.warning(f"Failed to upload PDF {pdf['name']}: {e}")
        except Exception as e:
            logger.error(f"Failed to create PDF datastore: {e}")

    # Group CSVs by header
    header_groups = defaultdict(list)

    for f in csv_files:
        try:
            local_path = download_s3_file(f["key"])
            headers = get_csv_headers(local_path)
            header_key = "|".join([h.strip().lower() for h in headers])
            header_groups[header_key].append(
                {"name": f["name"], "key": f["key"], "local_path": local_path}
            )
        except Exception as e:
            logger.warning(f"Failed to process CSV {f['name']}: {e}")

    datastores = []

    for key, group in header_groups.items():
        logger.debug(f"Group with headers [{key}]: {[f['name'] for f in group]}")
        file_paths = [file["local_path"] for file in group]

        try:
            result = generate_datastore_description_prompt(file_paths, llm)
            logger.info(f"Generated for group [{key}]: {result}")

            ds_id = create_datastore(
                result["name"], result["description"], "csv", organization_id
            )

            for file in group:
                try:
                    upload_file(
                        ds_id, "csv", file["local_path"], organization_id, file["name"]
                    )
                except Exception as e:
                    logger.warning(f"Failed to upload {file['name']}: {e}")
                finally:
                    os.remove(file["local_path"])

            datastores.append(
                {
                    "name": result.get("name", ""),
                    "description": result.get("description", ""),
                    "files": [file["key"] for file in group],
                }
            )
        except Exception as e:
            logger.warning(f"Failed for group [{key}]: {e}")
            for file in group:
                try:
                    os.remove(file["local_path"])
                except Exception:
                    pass

    return datastores
