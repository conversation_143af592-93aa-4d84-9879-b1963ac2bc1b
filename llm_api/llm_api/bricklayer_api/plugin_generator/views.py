"""
Simplified API views for plugin generation that work directly with ToolTemplate.
"""

import logging
import json
import yaml
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.request import Request
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>
from django.utils import timezone

from core.models import ToolTemplate
from .services import PluginGenerationService

logger = logging.getLogger(__name__)


class PluginGenerationView(APIView):
    """
    API view for generating plugins from OpenAPI specs.
    Saves results to ToolTemplate model.
    """
    
    parser_classes = [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, J<PERSON><PERSON>ars<PERSON>]
    
    def post(self, request: Request) -> Response:
        """
        Generate plugin from OpenAPI spec and save to ToolTemplate.
        
        Expected payload:
        - uploaded_file: OpenAPI spec file (.json, .yaml, .yml)
        - intent: User's intent describing what endpoints they need
        
        Returns:
        - tool_template_id: ID of created ToolTemplate record
        - display_name: Name of the generated plugin
        - display_description: Description of the generated plugin
        """
        try:
            # Validate input
            uploaded_file = request.FILES.get('uploaded_file')
            intent = request.data.get('intent')
            
            if not uploaded_file:
                return Response(
                    {'error': 'uploaded_file is required'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            if not intent or not intent.strip():
                return Response(
                    {'error': 'intent is required and cannot be empty'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # TODO: Validate intent length
            # if len(intent.strip()) < 10:
            #     return Response(
            #         {'error': 'intent must be at least 10 characters long'}, 
            #         status=status.HTTP_400_BAD_REQUEST
            #     )
            
            # Validate file type
            file_name = uploaded_file.name.lower()
            allowed_extensions = ['.json', '.yaml', '.yml']
            if not any(file_name.endswith(ext) for ext in allowed_extensions):
                return Response(
                    {'error': f'File must be one of: {", ".join(allowed_extensions)}'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            try:
                # Parse uploaded file
                content = uploaded_file.read().decode('utf-8')
                
                if file_name.endswith('.json'):
                    openapi_spec = json.loads(content)
                else:  # yaml or yml
                    openapi_spec = yaml.safe_load(content)
                
                # Validate OpenAPI version
                openapi_version = openapi_spec.get('openapi', '')
                if not str(openapi_version).startswith('3.'):
                    return Response(
                        {'error': 'Only OpenAPI 3.x specifications are supported'}, 
                        status=status.HTTP_400_BAD_REQUEST
                    )
                
            except json.JSONDecodeError as e:
                return Response(
                    {'error': f'Invalid JSON format: {str(e)}'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            except yaml.YAMLError as e:
                return Response(
                    {'error': f'Invalid YAML format: {str(e)}'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            except UnicodeDecodeError:
                return Response(
                    {'error': 'File must be UTF-8 encoded'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            except Exception as e:
                return Response(
                    {'error': f'Error parsing file: {str(e)}'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            logger.info(f"Processing OpenAPI spec with intent: {intent[:30]}...")
            
            # Process OpenAPI spec and save to ToolTemplate
            tool_template_id = PluginGenerationService.process_openapi_to_tool_template(
                openapi_spec, 
                intent.strip()
            )
            
            # Get the created ToolTemplate for response
            tool_template = ToolTemplate.objects.get(id=tool_template_id)
            
            logger.info(f"Plugin generation completed successfully. ToolTemplate ID: {tool_template_id}")
            
            return Response({
                'tool_template_id': tool_template_id,
                'display_name': tool_template.display_name,
                'display_description': tool_template.display_description,
                'created_at': tool_template.created_at.isoformat(),
                'message': 'Plugin generated successfully'
            }, status=status.HTTP_201_CREATED)
            
        except Exception as e:
            error_msg = f"Plugin generation failed: {str(e)}"
            logger.error(error_msg)
            
            return Response(
                {'error': error_msg}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class PluginRetrieveView(APIView):
    """
    Retrieve a generated plugin from ToolTemplate.
    """
    
    def get(self, request: Request, tool_template_id: int) -> Response:
        """
        Retrieve plugin details by ToolTemplate ID.
        
        Args:
            tool_template_id: ID of the ToolTemplate record
            
        Returns:
            Plugin details from ToolTemplate
        """
        try:
            tool_template = ToolTemplate.objects.get(
                id=tool_template_id, 
                type='plugin'
            )
            
            # Parse the spec to get plugin details
            try:
                spec_data = json.loads(tool_template.spec)
            except json.JSONDecodeError:
                spec_data = {'error': 'Invalid spec format'}
            
            return Response({
                'tool_template_id': tool_template.id,
                'display_name': tool_template.display_name,
                'display_description': tool_template.display_description,
                'type': tool_template.type,
                'configured_by': tool_template.configured_by,
                'spec': spec_data,
                'created_at': tool_template.created_at.isoformat(),
                'updated_at': tool_template.updated_at.isoformat()
            })
            
        except ToolTemplate.DoesNotExist:
            return Response(
                {'error': 'Plugin not found'}, 
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {'error': f'Error retrieving plugin: {str(e)}'}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class PluginListView(APIView):
    """
    List all generated plugins from ToolTemplate.
    """
    
    def get(self, request: Request) -> Response:
        """
        List all plugins of type 'plugin' from ToolTemplate.
        
        Returns:
            List of plugin summaries
        """
        try:
            plugins = ToolTemplate.objects.filter(type='plugin').order_by('-created_at')
            
            plugin_list = []
            for plugin in plugins:
                plugin_list.append({
                    'tool_template_id': plugin.id,
                    'display_name': plugin.display_name,
                    'display_description': plugin.display_description,
                    'configured_by': plugin.configured_by,
                    'created_at': plugin.created_at.isoformat(),
                    'updated_at': plugin.updated_at.isoformat()
                })
            
            return Response({
                'plugins': plugin_list,
                'count': len(plugin_list)
            })
            
        except Exception as e:
            return Response(
                {'error': f'Error listing plugins: {str(e)}'}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
