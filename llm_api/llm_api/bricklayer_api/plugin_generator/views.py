"""
API views for the Plugin Generator app.
"""

import logging
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.request import Request
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>NParser
from django.shortcuts import get_object_or_404
from django.utils import timezone

from .models import PluginGenerationRequest
from .serializers import (
    PluginGenerationPublicSerializer,
    PluginGenerationRequestSerializer,
    PluginGenerationCreateSerializer,
    PluginGenerationListSerializer,
    ProcessingLogSerializer
)
from .services import PluginGenerationService

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

class PluginGenerationViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing plugin generation requests.

    Provides CRUD operations and additional actions for plugin generation workflow.
    Supports both JSON and multipart/form-data for file uploads.
    """

    queryset = PluginGenerationRequest.objects.all()
    permission_classes = []
    parser_classes = [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>]
    
    def get_serializer_class(self):
        """Return appropriate serializer based on action."""
        if self.action == 'create':
            return PluginGenerationCreateSerializer
        elif self.action == 'list':
            return PluginGenerationListSerializer
        elif self.action == 'retrieve':
            # Check if admin details are requested via query parameter
            if self.request.query_params.get('admin', '').lower() == 'true':
                return PluginGenerationRequestSerializer  # Full details for admin/debugging
            return PluginGenerationPublicSerializer  # Clean response for public API
        return PluginGenerationRequestSerializer  # Full details for admin/debugging
    
    def get_queryset(self):
        """Filter queryset based on query parameters."""
        queryset = PluginGenerationRequest.objects.all()

        # Filter by status
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        

        
        return queryset.order_by('-created_at')
    
    def create(self, request: Request) -> Response:
        """
        Create a new plugin generation request.

        Args:
            request: HTTP request object

        Returns:
            Response with created request data
        """
        # Use create serializer for input validation
        serializer = PluginGenerationCreateSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        # Save the request
        instance = serializer.save(
            user=request.user if request.user.is_authenticated else None
        )

        logger.info(
            f"Created plugin generation request {instance.id} "
            f"with file {instance.original_filename} ({instance.file_type})"
        )

        # Start async processing only if parsing was successful
        if instance.status == 'pending' and instance.openapi_spec:
            try:
                PluginGenerationService.start_processing(str(instance.id))
                logger.info(f"Started processing for request {instance.id}")
            except Exception as e:
                logger.error(f"Failed to start processing for request {instance.id}: {str(e)}")
                instance.mark_failed(f"Failed to start processing: {str(e)}")
        elif instance.status == 'failed':
            logger.warning(f"Skipping processing for request {instance.id} due to parsing failure: {instance.error_message}")

        # Return clean response using public serializer
        response_serializer = PluginGenerationPublicSerializer(instance)
        return Response(
            response_serializer.data,
            status=status.HTTP_201_CREATED
        )
    
    @action(detail=True, methods=['get'])
    def logs(self, request: Request, pk: str = None) -> Response:
        """
        Get processing logs for a specific request.
        
        Args:
            request: HTTP request object
            pk: Primary key of the request
            
        Returns:
            Response with processing logs
        """
        instance = get_object_or_404(PluginGenerationRequest, pk=pk)
        
        logs = instance.logs.all().order_by('timestamp')
        serializer = ProcessingLogSerializer(logs, many=True)
        
        return Response({
            'request_id': str(instance.id),
            'status': instance.status,
            'logs': serializer.data
        })
    
    @action(detail=False, methods=['get'])
    def health(self, request: Request) -> Response:
        """
        Health check endpoint for the plugin generation service.
        """
        health_data = {
            'status': 'healthy',
            'timestamp': timezone.now(),
            'version': '1.0.0',
        }
        
        return Response(health_data)
