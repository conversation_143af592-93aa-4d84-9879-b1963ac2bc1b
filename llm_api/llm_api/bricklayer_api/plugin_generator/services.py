"""
Core service logic for plugin generation processing.

This service uses the pantheon plugin_agent processor for generating
Bricklayer-compatible plugin specifications from OpenAPI specs.
"""

import logging
import uuid
import yaml
from typing import Dict, Any, Optional
from typing import Literal
from pydantic import SecretStr
from pydantic_settings import BaseSettings, SettingsConfigDict

from blai.pantheon.plugin_agent.processor import PluginAgentProcessor, PluginAgentProcessorSettings
from blai.pantheon.plugin_agent.models import PluginGenerationRequest as PantheonPluginRequest

from .models import PluginGenerationRequest, ProcessingLog

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

# Step 1: Load Azure settings from env
class AzureOpenAIEnvSettings(BaseSettings):
    kind: Literal["AzureOpenAISettings"] = "AzureOpenAISettings"
    model_config = SettingsConfigDict(env_prefix="AZURE_OPENAI_")

    DEPLOYMENT: str
    API_BASE: str
    API_KEY: SecretStr
    API_VERSION: str = "2024-08-01-preview"


class PluginGenerationService:
    
    @classmethod
    def start_processing(cls, request_id: str) -> None:
        """
        Starts synchronous processing of plugin generation.

        Args:
            request_id: UUID of the plugin generation request
        """
        cls.process_request(request_id)
    
    @classmethod
    def process_request(cls, request_id: str) -> None:
        """
        Main processing pipeline for plugin generation.
        
        Args:
            request_id: UUID of the plugin generation request
        """
        request_obj = None
        try:
            # Get the request object
            request_obj = PluginGenerationRequest.objects.get(id=request_id)
            
            # Mark as processing
            request_obj.mark_processing_started()
            cls._log_step(request_obj, 'processing_started', 'Started plugin generation')
            
            # Step 1: Generate plugin spec via LLM
            plugin_spec = cls._generate_plugin_spec(request_obj)
            
            # Step 2: Save generated spec
            request_obj.generated_plugin_spec = plugin_spec
            request_obj.save(update_fields=['generated_plugin_spec', 'updated_at'])
            
            cls._log_step(
                request_obj, 
                'spec_generated', 
                'Plugin spec generated successfully',
                data={'spec_size': len(str(plugin_spec))}
            )
            
            # Step 3: Test plugin via Plugin Service
            test_results = cls._test_plugin(request_obj, plugin_spec)
            
            # Step 4: Mark as completed
            request_obj.mark_completed(plugin_spec=plugin_spec, test_results=test_results)
            
            cls._log_step(
                request_obj, 
                'completed', 
                'Plugin generation completed successfully',
                data={'test_results_summary': cls._summarize_test_results(test_results)}
            )
            
            logger.info(f"Successfully processed request {request_id}")
            
        except Exception as e:
            error_msg = f"Processing failed: {str(e)}"
            logger.exception(f"Error processing request {request_id}: {error_msg}")
            
            if request_obj:
                request_obj.mark_failed(error_msg)
                cls._log_step(request_obj, 'failed', error_msg)
    
    @classmethod
    def _generate_plugin_spec(cls, request_obj: PluginGenerationRequest) -> Dict[str, Any]:
        """
        Generate plugin spec using LLM processing.

        Args:
            request_obj: Plugin generation request object

        Returns:
            Generated plugin specification in Bricklayer format
        """
        cls._log_step(request_obj, 'llm_call_start', 'Starting LLM processing for plugin spec generation')

        try:
            # Use pantheon plugin_agent
            llm_response = cls._generate_plugin_spec_with_pantheon(
                request_obj.openapi_spec,
                request_obj.intent
            )
            print(llm_response, 'llm_response??????????????????>>>>>>>>>>>>.')
            # Parse LLM response into Bricklayer format
            plugin_spec = cls._parse_llm_response_to_bricklayer_format(
                llm_response,
                request_obj.openapi_spec
            )

            cls._log_step(
                request_obj,
                'llm_call_success',
                'Plugin spec generated successfully',
                data={
                    'endpoints_count': len(plugin_spec.get('spec', {}).get('endpoints', [])),
                    'llm_response_length': len(str(llm_response))
                }
            )

            return plugin_spec

        except Exception as e:
            cls._log_step(
                request_obj,
                'llm_call_failed',
                f'LLM processing failed: {str(e)}',
                data={'error': str(e)}
            )
            raise
    
    @classmethod
    def _test_plugin(cls, request_obj: PluginGenerationRequest, plugin_spec: Dict[str, Any]) -> Dict[str, Any]:
        """
        Test plugin using Plugin Service.
        
        Args:
            request_obj: Plugin generation request object
            plugin_spec: Generated plugin specification
            
        Returns:
            Test results
        """
        cls._log_step(request_obj, 'testing_start', 'Starting plugin testing')
        
        # Update status to testing
        request_obj.status = 'testing'
        request_obj.save(update_fields=['status', 'updated_at'])
        
        # For now, return mock test results
        # This will be replaced with actual Plugin Service calls
        test_results = {
            "status": "passed",
            "total_tests": 3,
            "passed_tests": 3,
            "failed_tests": 0,
            "test_details": [
                {"test": "endpoint_validation", "status": "passed"},
                {"test": "authentication_check", "status": "passed"},
                {"test": "response_format", "status": "passed"}
            ]
        }
        
        cls._log_step(
            request_obj, 
            'testing_complete', 
            'Plugin testing completed',
            data={'test_summary': cls._summarize_test_results(test_results)}
        )
        
        return test_results


    @classmethod
    def _generate_plugin_spec_with_pantheon(cls, openapi_spec: Dict[str, Any],
                                           intent: str) -> Dict[str, Any]:
        """
        Generate plugin spec using local pantheon processor.

        Args:
            openapi_spec: The OpenAPI specification dictionary
            intent: User's intent describing what endpoints they need

        Returns:
            LLM-compatible response format
        """
        try:
            # Convert OpenAPI spec(python dict format) to YAML format (as plugin processor expects YAML)
            openapi_yaml = yaml.dump(openapi_spec, default_flow_style=False)

            # Load settings from env vars
            env_settings = AzureOpenAIEnvSettings()

            # Build AI settings
            ai_settings_dict = {
                "kind": env_settings.kind,
                "DEPLOYMENT": env_settings.DEPLOYMENT,
                "ENDPOINT": env_settings.API_BASE,
                "API_KEY": env_settings.API_KEY.get_secret_value(),
                "API_VERSION": env_settings.API_VERSION
            }
            processor = PluginAgentProcessor(settings=PluginAgentProcessorSettings(ai=ai_settings_dict))

            request = PantheonPluginRequest(
                openapi_yaml_content=openapi_yaml,
                user_intent=intent,
                request_id=str(uuid.uuid4())
            )

            logger.info(f"Processing with pantheon plugin_agent started...")
            response = processor(request)
            logger.info(f"Pantheon processing completed successfully...")

            # Convert response to expected format (matching original LLM API response)
            plugin_spec = response.plugin.model_dump() if response.plugin else {}

            return {
                "answer": plugin_spec,
                "cost": 0.0,
                "sources": [],
                "plan": ["Used pantheon plugin_agent to generate plugin spec"],
                "evidence": []
            }

        except ImportError as e:
            logger.error(f"Failed to import pantheon plugin_agent: {e}")
            raise Exception(f"Pantheon plugin_agent not available: {e}")
        except Exception as e:
            logger.error(f"Pantheon processing failed: {e}")
            raise Exception(f"Plugin generation failed: {e}")

    @classmethod
    def _parse_llm_response_to_bricklayer_format(cls, llm_response: Dict[str, Any], openapi_spec: Dict[str, Any]) -> Dict[str, Any]:
        """
        Parse LLM response and ensure it matches Bricklayer format.

        Args:
            llm_response: Response from LLM API
            openapi_spec: Original OpenAPI spec

        Returns:
            Bricklayer-formatted plugin specification
        """
        import json

        # Extract the actual response content - your LLM API uses 'answer' field
        llm_content = llm_response.get('answer', llm_response.get('response', llm_response.get('message', '')))

        logger.info(f"LLM Response Keys: {list(llm_response.keys())}")
        logger.info(f"LLM Content Type: {type(llm_content)}")
        logger.info(f"LLM Content Preview: {str(llm_content)[:500]}...")

        try:
            # Try to parse JSON from LLM response
            if isinstance(llm_content, str):
                # Clean up the response (remove markdown code blocks if present)
                llm_content = llm_content.strip()
                if llm_content.startswith('```json'):
                    llm_content = llm_content[7:]
                if llm_content.endswith('```'):
                    llm_content = llm_content[:-3]
                llm_content = llm_content.strip()

                plugin_spec = json.loads(llm_content)
            else:
                plugin_spec = llm_content

            # Validate and ensure required structure
            if not isinstance(plugin_spec, dict):
                raise ValueError("LLM response is not a valid JSON object")

            # Ensure required fields exist
            if 'name' not in plugin_spec:
                plugin_spec['name'] = "Generated Plugin"

            if 'description' not in plugin_spec:
                plugin_spec['description'] = "Generated plugin from OpenAPI specification"

            if 'spec' not in plugin_spec:
                plugin_spec['spec'] = {}

            # Ensure spec has required fields
            spec = plugin_spec['spec']
            if 'endpoints' not in spec:
                spec['endpoints'] = []

            if 'base_url' not in spec:
                # Extract base URL from OpenAPI spec
                servers = openapi_spec.get('servers', [])
                spec['base_url'] = servers[0]['url'] if servers else 'https://api.example.com'

            return plugin_spec

        except (json.JSONDecodeError, ValueError) as e:
            logger.error(f"Failed to parse LLM response: {str(e)}")
            logger.error(f"LLM response content: {llm_content}")

            # Fallback: create a basic plugin spec
            return {
                "name": "Generated Plugin",
                "description": "Generated plugin from OpenAPI specification (LLM parsing failed)",
                "spec": {
                    "base_url": openapi_spec.get('servers', [{}])[0].get('url', 'https://api.example.com'),
                    "endpoints": [],
                    "authentication": cls._extract_auth_info(openapi_spec)
                }
            }

    @classmethod
    def _extract_relevant_endpoints(cls, openapi_spec: Dict[str, Any], intent: str) -> list:
        """Extract relevant endpoints based on intent"""
        paths = openapi_spec.get('paths', {})
        endpoints = []
        
        # Simple keyword matching for now
        # This will be replaced with LLM-based filtering
        intent_lower = intent.lower()
        keywords = ['ip', 'host', 'domain', 'address'] if 'ip' in intent_lower or 'host' in intent_lower else []
        
        for path, methods in paths.items():
            if any(keyword in path.lower() for keyword in keywords) or not keywords:
                for method, details in methods.items():
                    if method.upper() in ['GET', 'POST', 'PUT', 'DELETE', 'PATCH']:
                        endpoints.append({
                            "path": path,
                            "method": method.upper(),
                            "summary": details.get('summary', ''),
                            "description": details.get('description', ''),
                            "parameters": details.get('parameters', [])
                        })
        
        return endpoints
    
    @classmethod
    def _extract_auth_info(cls, openapi_spec: Dict[str, Any]) -> Dict[str, Any]:
        """Extract authentication information"""
        security_schemes = openapi_spec.get('components', {}).get('securitySchemes', {})
        security = openapi_spec.get('security', [])
        
        return {
            "schemes": security_schemes,
            "requirements": security
        }
    
    @classmethod
    def _extract_base_url(cls, openapi_spec: Dict[str, Any]) -> str:
        """Extract base URL from servers"""
        servers = openapi_spec.get('servers', [])
        if servers and isinstance(servers[0], dict):
            return servers[0].get('url', '')
        return ''
    
    @classmethod
    def _log_step(cls, request_obj: PluginGenerationRequest, step: str, message: str, data: Optional[Dict] = None) -> None:
        """
        Log processing step to database.
        
        Args:
            request_obj: Plugin generation request object
            step: Step identifier
            message: Log message
            data: Optional additional data
        """
        try:
            ProcessingLog.objects.create(
                request=request_obj,
                step=step,
                message=message,
                data=data
            )
            logger.debug(f"Logged step '{step}' for request {request_obj.id}: {message}")
        except Exception as e:
            logger.error(f"Failed to log step '{step}' for request {request_obj.id}: {str(e)}")
    
    @classmethod
    def _summarize_test_results(cls, test_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a summary of test results.
        
        Args:
            test_results: Full test results
            
        Returns:
            Summarized test results
        """
        if not test_results:
            return {'status': 'no_results'}
        
        summary = {
            'status': test_results.get('status', 'unknown'),
            'total_tests': test_results.get('total_tests', 0),
            'passed_tests': test_results.get('passed_tests', 0),
            'failed_tests': test_results.get('failed_tests', 0),
        }
        
        if 'errors' in test_results:
            summary['error_count'] = len(test_results['errors'])
        
        return summary
