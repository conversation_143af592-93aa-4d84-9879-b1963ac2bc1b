"""
Simplified plugin generation service that saves directly to ToolTemplate.
"""

import logging
import json
import yaml
import uuid
from typing import Dict, Any
from pydantic import SecretStr
from pydantic_settings import BaseSettings, SettingsConfigDict
from typing_extensions import Literal

from core.models import ToolTemplate
from blai.pantheon.plugin_agent.processor import PluginAgentProcessor, PluginAgentProcessorSettings
from blai.pantheon.plugin_agent.models import PluginGenerationRequest as PantheonPluginRequest

logger = logging.getLogger(__name__)

# Azure OpenAI settings for pantheon processor
class AzureOpenAIEnvSettings(BaseSettings):
    kind: Literal["AzureOpenAISettings"] = "AzureOpenAISettings"
    model_config = SettingsConfigDict(env_prefix="AZURE_OPENAI_")

    DEPLOYMENT: str
    API_BASE: str
    API_KEY: SecretStr
    API_VERSION: str = "2024-08-01-preview"


class PluginGenerationService:
    
    @classmethod
    def process_openapi_to_tool_template(cls, openapi_spec: Dict[str, Any], intent: str) -> int:
        """
        Process OpenAPI spec and save directly to ToolTemplate.

        Args:
            openapi_spec: The OpenAPI specification dictionary
            intent: User's intent describing what endpoints they need

        Returns:
            ToolTemplate ID of the created record
        """
        try:
            logger.info("Starting plugin generation process...")
            
            # Generate plugin spec using pantheon
            llm_response = cls._generate_plugin_spec_with_pantheon(openapi_spec, intent)
            logger.info("Plugin spec generated successfully")
            
            # Save directly to ToolTemplate
            tool_template_id = cls._save_to_tool_template(llm_response)
            logger.info(f"ToolTemplate created with ID: {tool_template_id}")
            
            return tool_template_id
            
        except Exception as e:
            error_msg = f"Plugin generation failed: {str(e)}"
            logger.error(error_msg)
            raise Exception(error_msg)
    
    @classmethod
    def _generate_plugin_spec_with_pantheon(cls, openapi_spec: Dict[str, Any], intent: str) -> Dict[str, Any]:
        """
        Generate plugin spec using pantheon processor.

        Args:
            openapi_spec: The OpenAPI specification dictionary
            intent: User's intent describing what endpoints they need

        Returns:
            LLM response containing plugin specification
        """
        try:
            logger.info("Processing with pantheon plugin_agent started...")
            
            # Convert OpenAPI spec(python dict format) to YAML format (as plugin processor expects YAML)
            openapi_yaml = yaml.dump(openapi_spec, default_flow_style=False)
            
            # Create pantheon request with correct field names
            pantheon_request = PantheonPluginRequest(
                openapi_yaml_content=openapi_yaml,
                user_intent=intent,
                request_id=str(uuid.uuid4())
            )
            # Initialize processor with Azure OpenAI settings
            azure_settings = AzureOpenAIEnvSettings()

            # Try different ways to pass settings to pantheon processor
            try:
                # Option 1: Pass as llm_settings
                processor_settings = PluginAgentProcessorSettings(
                    llm_settings=azure_settings
                )
            except Exception:
                try:
                    # Option 2: Pass as model_dump
                    processor_settings = PluginAgentProcessorSettings(
                        llm_settings=azure_settings.model_dump()
                    )
                except Exception:
                    # Option 3: Pass without settings (use defaults)
                    processor_settings = PluginAgentProcessorSettings()

            logger.info("Azure settings loaded: %s", azure_settings)
            processor = PluginAgentProcessor(settings=processor_settings)
            
            # Process the request
            response = processor.process(pantheon_request)
            logger.info("Pantheon processing completed successfully...")
            
            # Convert response to expected format (matching original LLM API response)
            plugin_spec = response.plugin.model_dump() if response.plugin else {}

            return {
                "answer": plugin_spec,
                "cost": 0.0,
                "sources": [],
                "plan": ["Used pantheon plugin_agent to generate plugin spec"],
                "evidence": []
            }

        except ImportError as e:
            logger.error(f"Failed to import pantheon plugin_agent: {e}")
            raise Exception(f"Plugin generation failed: {e}")
        except Exception as e:
            logger.error(f"Pantheon processing failed: {e}")
            raise Exception(f"Plugin generation failed: {e}")
    
    @classmethod
    def _save_to_tool_template(cls, llm_response: Dict[str, Any]) -> int:
        """
        Save LLM response directly to ToolTemplate.

        Args:
            llm_response: Response from LLM containing plugin spec

        Returns:
            ToolTemplate ID of created record
        """
        try:
            # Extract plugin spec from LLM response
            plugin_spec = llm_response.get('answer', {})
            
            # Get display info
            display_name = plugin_spec.get('name', 'Generated Plugin')
            display_description = plugin_spec.get('description', 'Generated plugin from OpenAPI specification')
            
            # Create ToolTemplate record
            tool_template = ToolTemplate.objects.create(
                spec=json.dumps(llm_response), 
                type='plugin',  
                llm_type=None, 
                display_name=display_name,
                display_description=display_description,
                configured_by='public',  # As requested
                configurable_spec_fields=json.dumps([])  # Empty for now
            )
            
            logger.info(f"ToolTemplate created successfully with ID: {tool_template.id}")
            return tool_template.id
            
        except Exception as e:
            logger.error(f"Failed to save to ToolTemplate: {str(e)}")
            raise Exception(f"Failed to save plugin to ToolTemplate: {str(e)}")
