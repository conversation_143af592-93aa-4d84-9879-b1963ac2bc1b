import uuid
from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from django.conf import settings


def plugin_spec_upload_path(instance, filename):
    """
    Generate upload path for plugin spec files.
    Configurable via PLUGIN_GENERATOR_UPLOAD_PATH setting.
    """
    upload_dir = getattr(settings, 'PLUGIN_GENERATOR_UPLOAD_PATH', 'plugin_generator/specs')
    return f"{upload_dir}/{filename}"


class PluginGenerationRequest(models.Model):
    """
    Model to store plugin generation requests and their processing status.
    """
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        help_text="User who created this request"
    )

    # File upload fields
    uploaded_file = models.FileField(
        upload_to=plugin_spec_upload_path,
        null=True,
        blank=True,
        help_text="Uploaded OpenAPI/Swagger specification file (.json, .yaml, .yml)"
    )
    original_filename = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        help_text="Original filename of the uploaded file"
    )
    file_type = models.CharField(
        max_length=10,
        choices=[
            ('json', 'JSON'),
            ('yaml', 'YAML'),
            ('yml', 'YML'),
        ],
        null=True,
        blank=True,
        help_text="Type of the uploaded file"
    )

    # Parsed content (populated after file processing)
    openapi_spec = models.JSONField(
        null=True,
        blank=True,
        help_text="Parsed OpenAPI/Swagger specification (converted from uploaded file)"
    )
    intent = models.TextField(
        help_text="User's intent describing what endpoints they need"
    )
    status = models.CharField(
        max_length=20, 
        choices=STATUS_CHOICES, 
        default='pending',
        help_text="Current processing status"
    )

    # Processing results
    generated_plugin_spec = models.JSONField(
        null=True, 
        blank=True,
        help_text="Generated Bricklayer plugin specification"
    )
    test_results = models.JSONField(
        null=True,
        blank=True,
        help_text="Results from plugin testing"
    )
    created_tool_template_id = models.IntegerField(
        null=True,
        blank=True,
        help_text="ID of the created ToolTemplate entry"
    )
    error_message = models.TextField(
        null=True,
        blank=True,
        help_text="Error message if processing failed"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    processing_started_at = models.DateTimeField(
        null=True, 
        blank=True,
        help_text="When processing started"
    )
    processing_completed_at = models.DateTimeField(
        null=True, 
        blank=True,
        help_text="When processing completed (success or failure)"
    )
    
    class Meta:
        db_table = 'plugin_generator_plugingenerationrequest'  # Keep old table name
        ordering = ['-created_at']
        verbose_name = 'Plugin Generation Request'
        verbose_name_plural = 'Plugin Generation Requests'
    
    def __str__(self):
        filename_info = f" ({self.original_filename})" if self.original_filename else ""
        return f"Plugin Request {str(self.id)[:8]}{filename_info} - {self.status} ({self.created_at.strftime('%Y-%m-%d %H:%M')})"
    
    @property
    def processing_duration(self):
        """Calculate processing duration if available"""
        if self.processing_started_at and self.processing_completed_at:
            return self.processing_completed_at - self.processing_started_at
        return None

    @property
    def created_tool_template(self):
        """Get the created ToolTemplate instance if available"""
        if self.created_tool_template_id:
            from core.models import ToolTemplate
            try:
                return ToolTemplate.objects.get(id=self.created_tool_template_id)
            except ToolTemplate.DoesNotExist:
                return None
        return None
    
    def mark_processing_started(self):
        """Mark request as processing and set start time"""
        self.status = 'processing'
        self.processing_started_at = timezone.now()
        self.save(update_fields=['status', 'processing_started_at', 'updated_at'])
    
    def mark_completed(self, plugin_spec=None, test_results=None, tool_template_id=None):
        """Mark request as completed with results"""
        self.status = 'completed'
        self.processing_completed_at = timezone.now()
        if plugin_spec:
            self.generated_plugin_spec = plugin_spec
        if test_results:
            self.test_results = test_results
        if tool_template_id:
            self.created_tool_template_id = tool_template_id
        self.save(update_fields=[
            'status', 'processing_completed_at', 'generated_plugin_spec',
            'test_results', 'created_tool_template_id', 'updated_at'
        ])
    
    def mark_failed(self, error_message):
        """Mark request as failed with error message"""
        self.status = 'failed'
        self.processing_completed_at = timezone.now()
        self.error_message = error_message
        self.save(update_fields=[
            'status', 'processing_completed_at', 'error_message', 'updated_at'
        ])


class ProcessingLog(models.Model):
    """
    Model to store detailed processing logs for each request.
    """
    request = models.ForeignKey(
        PluginGenerationRequest, 
        on_delete=models.CASCADE, 
        related_name='logs',
        help_text="Associated plugin generation request"
    )
    step = models.CharField(
        max_length=100,
        help_text="Processing step identifier"
    )
    message = models.TextField(
        help_text="Log message"
    )
    data = models.JSONField(
        null=True, 
        blank=True,
        help_text="Additional structured data for this log entry"
    )
    timestamp = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        db_table = 'plugin_generator_processinglog'  # Keep old table name
        ordering = ['timestamp']
        verbose_name = 'Processing Log'
        verbose_name_plural = 'Processing Logs'
    
    def __str__(self):
        return f"{self.step} ({self.timestamp.strftime('%H:%M:%S')})"
