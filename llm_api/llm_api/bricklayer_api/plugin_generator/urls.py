"""
URL configuration for the Plugin Generator app.
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import PluginGenerationViewSet

# Create router and register viewsets
router = DefaultRouter()
router.register(r'generate-plugin', PluginGenerationViewSet, basename='generate-plugin')

# App name for namespacing
app_name = 'plugin_generator'

# URL patterns
urlpatterns = [
    # API routes
    path('', include(router.urls)),
]
