#!/usr/bin/env python
"""Django's command-line utility for administrative tasks."""
import json
import logging
import os
import sys
from pathlib import Path

from dotenv import load_dotenv

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

load_dotenv()


def load_env_secrets():
    secrets_path = "/mnt/secrets-store/secrets"
    if Path(secrets_path).exists():
        with Path(secrets_path).open("r") as f:
            env_vars_json = json.load(f)
            for k, v in env_vars_json.items():
                logger.debug(f"Setting {k} from secrets store")
                os.environ[k] = v


def main():
    """Run administrative tasks."""
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "bricklayer_api.settings")
    try:
        from django.core.management import execute_from_command_line
    except ImportError as exc:
        raise ImportError(
            "Couldn't import Django. Are you sure it's installed and "
            "available on your PYTHONPATH environment variable? Did you "
            "forget to activate a virtual environment?"
        ) from exc
    execute_from_command_line(sys.argv)


if __name__ == "__main__":
    load_env_secrets()
    main()
