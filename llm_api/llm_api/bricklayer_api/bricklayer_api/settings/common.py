"""
Django settings for bricklayer_api project.

Generated by 'django-admin startproject' using Django 5.2.

For more information on this file, see
https://docs.djangoproject.com/en/5.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.2/ref/settings/
"""

import os
from pathlib import Path

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.2/howto/deployment/checklist/

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ["*"]

APPEND_SLASH = True  # default
# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "rest_framework",
    "django_celery_results",
    "core",
    "bl_auth",
    "organization",
    "procedures",
    "procedure_dashboard",
    "plugin_generator",
]

MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
]

ROOT_URLCONF = "bricklayer_api.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "bricklayer_api.wsgi.application"


# Database
# https://docs.djangoproject.com/en/5.2/ref/settings/#databases

BLAI_ENV = os.environ.get("BLAI_ENV", "local")

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": os.environ.get("POSTGRES_DB", "prisma"),
        "USER": os.environ.get("POSTGRES_USER", "postgres"),
        "PASSWORD": os.environ.get("POSTGRES_PASS", "example"),
        # "HOST": os.environ.get("POSTGRES_HOST", "localhost"),
        "HOST": os.environ.get("POSTGRES_HOST", "db"),
        "PORT": os.environ.get("POSTGRES_PORT", 5432),
    }
}


# Password validation
# https://docs.djangoproject.com/en/5.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

# Logging

LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
        },
        "ignore": {
            "class": "logging.NullHandler",
        },
    },
    "root": {
        "handlers": ["console"],
        "level": "DEBUG",
    },
    "loggers": {
        "django": {
            "handlers": ["console"],
            "level": "DEBUG",
            "propagate": False,
        },
        "watchfiles": {
            "handlers": ["ignore"],
            "level": "WARNING",
            "propagate": False,
        },
        "watchdog": {
            "handlers": ["ignore"],
            "level": "WARNING",
            "propagate": False,
        },
        "django.db.backends": {
            "handlers": ["ignore"],
            "level": "WARNING",
            "propagate": False,
        },
        "boto3": {
            "handlers": ["ignore"],
            "level": "WARNING",
            "propagate": False,
        },
        "botocore": {
            "handlers": ["ignore"],
            "level": "WARNING",
            "propagate": False,
        },
        "urllib3": {
            "handlers": ["ignore"],
            "level": "WARNING",
            "propagate": False,
        },
    },
}

import django.utils.autoreload


def quiet_file_change_logger(path, *args, **kwargs):
    pass  # ignore all autoreload file change logs


django.utils.autoreload.logger.info = quiet_file_change_logger
django.utils.autoreload.logger.debug = quiet_file_change_logger


# Internationalization
# https://docs.djangoproject.com/en/5.2/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.2/howto/static-files/

STATIC_URL = "static/"

# Default primary key field type
# https://docs.djangoproject.com/en/5.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# Celery

# NOTE: In out current setup, database /0 in Redis is used
# by both procedure workers and BullMQ (from the Node.js backend)
# We'll use /1 for broker messages and /2 as the result backend

# Broker (where tasks are sent)
CELERY_BROKER_URL = f"redis://{os.environ['REDIS_HOST']}:6379/1"

# We'll use django-celery-results to store task results in PostgreSQL
CELERY_RESULT_BACKEND = "django-db"

CELERY_ACCEPT_CONTENT = ["json"]
CELERY_TASK_SERIALIZER = "json"
CELERY_RESULT_SERIALIZER = "json"

# Bricklayer settings
PUBLIC_BACKEND_URL = os.environ["PUBLIC_BACKEND_URL"]
PUBLIC_APP_URL = os.environ["PUBLIC_APP_URL"]

# AWS general settings
AWS_REGION = os.environ["AWS_REGION"]

# AWS Cognito
COGNITO_CLIENT_ID = os.environ["COGNITO_CLIENT_ID"]
COGNITO_USER_POOL_ID = os.environ["COGNITO_USER_POOL_ID"]

# AWS SES
INVITATION_EMAIL = os.environ["INVITATION_EMAIL"]
## Identity ARN for sending emails
BRICKLAYER_AWS_SES_IDENTITY_ARN = os.environ["BRICKLAYER_AWS_SES_IDENTITY_ARN"]
## IAM Role for SES operations
BRICKLAYER_AWS_SHARED_SES_ROLE = os.environ["BRICKLAYER_AWS_SHARED_SES_ROLE"]

# LangSmith Configuration for OpenAPI Generator
LANGSMITH_TRACING = os.environ.get("LANGSMITH_TRACING", "false").lower() == "true"
LANGSMITH_PROJECT = os.environ.get("LANGSMITH_PROJECT", "openapi-generator")
LANGSMITH_ENDPOINT = os.environ.get("LANGSMITH_ENDPOINT", "https://api.smith.langchain.com")
LANGSMITH_API_KEY = os.environ.get("LANGSMITH_API_KEY", '***************************************************')

AZURE_OPENAI_API_TYPE="azure"
AZURE_OPENAI_API_KEY="7YiuKrRRMliVJSlUO31fTVepvkbCcZ4wuFGcJybgHkbFuQbWgu35JQQJ99BBACHYHv6XJ3w3AAABACOGThNI"
AZURE_OPEN_API_VERSION="2024-02-01"
AZURE_OPENAI_DEPLOYMENT_NAME="gpt-4o-mini"
AZURE_OPENAI_ENDPOINT="https://local-bricklayerai.openai.azure.com/"
