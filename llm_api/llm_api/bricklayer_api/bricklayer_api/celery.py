import json
import os
from pathlib import Path

from celery import Celery

# Load secrets into env
secrets_path = "/mnt/secrets-store/secrets"
if Path(secrets_path).exists():
    with open(secrets_path) as f:
        env_vars_json = json.load(f)
        for k, v in env_vars_json.items():
            os.environ[k] = v

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "bricklayer_api.settings")

app = Celery("bricklayer_api")
app.config_from_object("django.conf:settings", namespace="CELERY")
app.autodiscover_tasks()
