"""
URL configuration for bricklayer_api project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from bricklayer_api.views import healthcheck
from django.contrib import admin
from django.urls import include, path

urlpatterns = [
    path("admin/", admin.site.urls),
    path("healthcheck", healthcheck, name="healthcheck"),
    path("auth/", include("bl_auth.urls")),
    path("onboarding/", include("organization.urls")),
    path("procedures/", include("procedures.urls")),
    path("procedure_dashboard/", include("procedure_dashboard.urls")),
    path("plugins/", include("plugin_generator.urls")),

]
