"""
ASGI config for bricklayer_api project.

It exposes the ASGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/5.2/howto/deployment/asgi/
"""

import os

import os, json
from pathlib import Path

# Load secrets into env
secrets_path = "/mnt/secrets-store/secrets"
if Path(secrets_path).exists():
    with open(secrets_path) as f:
        env_vars_json = json.load(f)
        for k, v in env_vars_json.items():
            os.environ[k] = v

from django.core.asgi import get_asgi_application

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "bricklayer_api.settings")

application = get_asgi_application()
