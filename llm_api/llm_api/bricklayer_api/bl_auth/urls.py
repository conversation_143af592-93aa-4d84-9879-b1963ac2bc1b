from bl_auth.api.views import (
    CognitoAuthAPIView,
    MfaRecoveryViewSet,
    MfaViewSet,
    OrganizationMfaViewSet,
    OrganizationUserMfaViewSet,
    UserMfaViewSet,
)
from django.urls import path
from rest_framework.routers import DefaultRouter

mfa_router = DefaultRouter()
mfa_router.register(r"mfa", MfaViewSet, basename="mfa")
mfa_router.register(
    r"mfa/organization/settings",
    OrganizationUserMfaViewSet,
    basename="organization-user-mfa",
)
mfa_router.register(
    r"mfa/organization", OrganizationMfaViewSet, basename="organization-mfa"
)
mfa_router.register(r"mfa/user", UserMfaViewSet, basename="user-mfa")
mfa_router.register(r"mfa/recovery", MfaRecoveryViewSet, basename="mfa-recovery")

urlpatterns = [
    path("sign-in/", CognitoAuthAPIView.as_view(), name="sign-in"),
] + mfa_router.urls
