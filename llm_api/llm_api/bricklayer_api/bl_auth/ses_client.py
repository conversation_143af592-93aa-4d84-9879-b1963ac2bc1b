import logging

import boto3
from botocore.exceptions import Client<PERSON>rror
from django.conf import settings

logger = logging.getLogger(__name__)


def get_ses_client_with_role():
    """
    Create an SES client by assuming the BricklayerSESRole IAM role.

    Returns:
        boto3.client: SES client with assumed role credentials
    """
    try:
        # Create STS client to assume role
        sts_client = boto3.client("sts", region_name=settings.AWS_REGION)

        # Assume the SES role
        assumed_role = sts_client.assume_role(
            RoleArn=settings.BRICKLAYER_AWS_SHARED_SES_ROLE,
            RoleSessionName="BricklayerSESSession",
        )

        # Extract temporary credentials
        credentials = assumed_role["Credentials"]

        # Create SES client with assumed role credentials
        ses_client = boto3.client(
            "ses",
            region_name=settings.AWS_REGION,
            aws_access_key_id=credentials["AccessKeyId"],
            aws_secret_access_key=credentials["SecretAccessKey"],
            aws_session_token=credentials["SessionToken"],
        )

        logger.info(
            f"Successfully assumed SES role: {settings.BRICKLAYER_AWS_SHARED_SES_ROLE}"
        )
        return ses_client

    except ClientError as e:
        logger.error(
            f"Failed to assume SES role {settings.BRICKLAYER_AWS_SHARED_SES_ROLE}: {str(e)}"
        )
        raise
    except Exception as e:
        logger.error(f"Unexpected error when creating SES client: {str(e)}")
        raise


def send_ses_email(source, destination, subject, body_text, body_html=None):
    """
    Send email using SES with assumed role credentials.

    Args:
        source (str): From email address
        destination (str): To email address
        subject (str): Email subject
        body_text (str): Plain text body
        body_html (str, optional): HTML body

    Returns:
        dict: SES send_email response
    """
    try:
        ses_client = get_ses_client_with_role()

        # Prepare email body
        body = {"Text": {"Data": body_text, "Charset": "UTF-8"}}
        if body_html:
            body["Html"] = {"Data": body_html, "Charset": "UTF-8"}

        # Send email
        response = ses_client.send_email(
            Source=source,
            Destination={"ToAddresses": [destination]},
            Message={"Subject": {"Data": subject, "Charset": "UTF-8"}, "Body": body},
            SourceArn=settings.BRICKLAYER_AWS_SES_IDENTITY_ARN,
        )

        logger.info(
            f"Email sent successfully to {destination}. MessageId: {response['MessageId']}"
        )
        return response
    except ClientError as e:
        error_code = e.response["Error"]["Code"]
        error_message = e.response["Error"]["Message"]
        logger.error(f"SES error {error_code}: {error_message}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error sending email: {str(e)}")
        raise
