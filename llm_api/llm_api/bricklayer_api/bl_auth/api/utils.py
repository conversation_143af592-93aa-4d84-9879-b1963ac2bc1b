from typing import TYPE_CHECKING

import jwt
from core.models import Organization, User
from rest_framework.exceptions import AuthenticationFailed

if TYPE_CHECKING:
    from rest_framework.request import Request


def get_organization_from_jwt(request: "Request") -> Organization:
    """
    Extract organization from JW<PERSON> token in Authorization header.

    Args:
        request: DRF Request object containing Authorization header

    Returns:
        Organization instance from the JWT custom:OrganizationId claim

    Raises:
        AuthenticationFailed: If token is missing, invalid, or organization not found
    """
    auth_header = request.headers.get("Authorization")
    if not auth_header:
        raise AuthenticationFailed("Authorization header is required")

    try:
        # Extract token from "Bearer <token>" format
        token = (
            auth_header.split(" ")[1]
            if auth_header.startswith("Bearer ")
            else auth_header
        )
        decoded_jwt = jwt.decode(token, options={"verify_signature": False})
        organization_id = decoded_jwt.get("custom:OrganizationId")

        if not organization_id:
            raise AuthenticationFailed("Organization ID not found in JWT")

        return Organization.objects.get(id=organization_id)
    except (IndexError, jwt.DecodeError, Organization.DoesNotExist) as e:
        raise AuthenticationFailed(f"Invalid token or organization: {str(e)}")


def get_user_from_jwt(request: "Request") -> User:
    """
    Extract user from JWT token in Authorization header.

    Args:
        request: DRF Request object containing Authorization header

    Returns:
        User instance from the JWT custom:UserId and custom:OrganizationId claims

    Raises:
        AuthenticationFailed: If token is missing, invalid, or user not found
    """
    auth_header = request.headers.get("Authorization")
    if not auth_header:
        raise AuthenticationFailed("Authorization header is required")

    try:
        # Extract token from "Bearer <token>" format
        token = (
            auth_header.split(" ")[1]
            if auth_header.startswith("Bearer ")
            else auth_header
        )
        decoded_jwt = jwt.decode(token, options={"verify_signature": False})
        user_id = decoded_jwt.get("custom:UserId")
        organization_id = decoded_jwt.get("custom:OrganizationId")

        if not user_id:
            raise AuthenticationFailed("User ID not found in JWT")

        if not organization_id:
            raise AuthenticationFailed("Organization ID not found in JWT")

        return User.objects.get(pk=user_id, organization_id=organization_id)
    except (IndexError, jwt.DecodeError, User.DoesNotExist) as e:
        raise AuthenticationFailed(f"Invalid token or user: {str(e)}")
