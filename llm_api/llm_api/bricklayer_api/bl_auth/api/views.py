from bl_auth.api.serializers import (
    CognitoMfaAuthSerializer,
    CognitoMfaRecoverySerializer,
    MfaChallengeResponseSerializer,
    MfaRecoveryApprovalSerializer,
    MfaRecoveryCompletionSerializer,
    MfaRecoveryRequestListSerializer,
    MfaRecoveryRequestSerializer,
    MfaVerificationSerializer,
    OrganizationMfaSerializer,
    UserMfaSerializer,
)
from bl_auth.api.utils import get_organization_from_jwt, get_user_from_jwt
from core.models import MfaRecoveryRequest, User
from django.core.exceptions import ValidationError
from django.db import transaction
from django.db.models import <PERSON><PERSON>anField, ExpressionWrapper, Q
from django.db.models.functions import Now
from django.shortcuts import get_object_or_404
from django.utils import timezone
from rest_framework import generics, permissions, status, viewsets
from rest_framework.decorators import action
from rest_framework.response import Response


class CognitoAuthAPIView(generics.CreateAPIView):
    serializer_class = CognitoMfaAuthSerializer


class MfaViewSet(viewsets.GenericViewSet):
    @action(detail=False, methods=["post"], serializer_class=MfaVerificationSerializer)
    def verification(self, request):
        serializer_data = {**request.data}
        match serializer_data["credentials"]:
            case {"Session": _}:
                serializer_data["challenge"] = "MFA_SETUP"

        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    @action(
        detail=False,
        methods=["post"],
        serializer_class=MfaChallengeResponseSerializer,
        url_path="challenge-response",
    )
    def challenge_response(self, request):
        serializer = self.get_serializer(
            data={**request.data, "challenge": "SOFTWARE_TOKEN_MFA"}
        )
        serializer.is_valid(raise_exception=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    @action(
        detail=False,
        methods=("post",),
        serializer_class=CognitoMfaRecoverySerializer,
        url_path="dev-recovery",
    )
    def recovery(self, request):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


class OrganizationMfaViewSet(viewsets.GenericViewSet):
    @action(
        detail=False,
        methods=["patch"],
        url_path="settings",
        serializer_class=OrganizationMfaSerializer,
    )
    def update_settings(self, request):
        organization = get_organization_from_jwt(request)
        serializer = self.get_serializer(organization, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response(serializer.data, status=status.HTTP_200_OK)


class UserMfaViewSet(viewsets.GenericViewSet):
    @action(
        detail=False,
        methods=["patch"],
        url_path="settings",
        serializer_class=UserMfaSerializer,
    )
    def update_settings(self, request):
        user = get_user_from_jwt(request)

        serializer = self.get_serializer(user, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)

        try:
            serializer.save()
        except ValidationError as e:
            return Response(
                {"detail": str(e.message) if hasattr(e, "message") else str(e)},
                status=status.HTTP_400_BAD_REQUEST,
            )

        return Response(serializer.data, status=status.HTTP_200_OK)


class OrganizationUserMfaViewSet(viewsets.GenericViewSet):
    serializer_class = UserMfaSerializer

    def partial_update(self, request, pk=None):
        organization = get_organization_from_jwt(request)

        user = get_object_or_404(User, pk=pk, organization=organization)

        serializer = self.get_serializer(user, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)

        try:
            serializer.save()
        except ValidationError as e:
            return Response(
                {"detail": str(e.message) if hasattr(e, "message") else str(e)},
                status=status.HTTP_400_BAD_REQUEST,
            )

        return Response(serializer.data, status=status.HTTP_200_OK)


class MfaRecoveryViewSet(viewsets.GenericViewSet):
    def get_queryset(self):
        action = getattr(self, "action")

        match action:
            case "approve" | "deny" | "list" | "retrieve":
                # Admin endpoints - return all requests for the organization
                organization = get_organization_from_jwt(self.request)
                return MfaRecoveryRequest.objects.filter(organization=organization)
            case _:
                return MfaRecoveryRequest.objects.all()

    def get_serializer_class(self):
        match self.action:
            case "create" | "retrieve":
                return MfaRecoveryRequestSerializer
            case "list":
                return MfaRecoveryRequestListSerializer

        return super().get_serializer_class()

    @transaction.atomic
    def create(self, request):
        """Create a new MFA recovery request with email/password authentication"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        # Get validated user from serializer (after Cognito authentication)
        validated_data = serializer.validated_data
        user = validated_data["user"]

        # Check if user already has a pending request
        existing_request = MfaRecoveryRequest.objects.filter(
            user=user, status=MfaRecoveryRequest.Status.PENDING
        ).first()

        if existing_request:
            return Response(
                {"detail": "You already have a pending recovery request"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Set user to recovery pending state
        user.mfa_recovery_state = User.MfaRecoveryState.PENDING
        user.save()

        # Create recovery request
        recovery_request = serializer.save()

        return Response(
            MfaRecoveryRequestSerializer(recovery_request).data,
            status=status.HTTP_201_CREATED,
        )

    def list(self, request):
        """
        List all recovery requests for organization admins.
        TODO: Implement admin validation here, for now we rely on Node.js for this.
        """
        queryset = self.get_queryset().annotate(
            is_expired=ExpressionWrapper(
                Q(expires_at__lt=Now()), output_field=BooleanField()
            )
        )
        serializer = MfaRecoveryRequestListSerializer(queryset, many=True)
        return Response(serializer.data)

    def retrieve(self, request, pk=None):
        """
        Get specific recovery request details.
        TODO: Implement admin validation here, for now we rely on Node.js for this.
        """
        user = get_user_from_jwt(request)
        recovery_request = get_object_or_404(self.get_queryset(), pk=pk)

        serializer = MfaRecoveryRequestSerializer(recovery_request)
        return Response(serializer.data)

    @action(
        detail=True, methods=["patch"], serializer_class=MfaRecoveryApprovalSerializer
    )
    @transaction.atomic
    def approve(self, request, pk=None):
        """
        Approve a recovery request (admin only).
        TODO: Implement admin validation here, for now we rely on Node.js for this.
        """
        admin_user = get_user_from_jwt(request)

        recovery_request = get_object_or_404(self.get_queryset(), pk=pk)

        if recovery_request.status != MfaRecoveryRequest.Status.PENDING:
            return Response(
                {"detail": "Request cannot be approved in current status"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        serializer = self.get_serializer(
            recovery_request,
            data=request.data,
            partial=True,
            context={"approval_decision": "approve", "admin_user": admin_user},
        )
        serializer.is_valid(raise_exception=True)
        recovery_request = serializer.save()

        return Response(
            MfaRecoveryRequestSerializer(recovery_request).data,
            status=status.HTTP_200_OK,
        )

    @action(
        detail=True, methods=["patch"], serializer_class=MfaRecoveryApprovalSerializer
    )
    @transaction.atomic
    def deny(self, request, pk=None):
        """
        Deny a recovery request (admin only).
        TODO: Implement admin validation here, for now we rely on Node.js for this.
        """
        admin_user = get_user_from_jwt(request)

        recovery_request = get_object_or_404(self.get_queryset(), pk=pk)

        if recovery_request.status != MfaRecoveryRequest.Status.PENDING:
            return Response(
                {"detail": "Request cannot be denied in current status"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        serializer = self.get_serializer(
            recovery_request,
            data=request.data,
            partial=True,
            context={"approval_decision": "deny", "admin_user": admin_user},
        )
        serializer.is_valid(raise_exception=True)
        recovery_request = serializer.save()

        # Reset user recovery state
        recovery_request.user.mfa_recovery_state = User.MfaRecoveryState.NORMAL
        recovery_request.user.save()

        return Response(
            MfaRecoveryRequestSerializer(recovery_request).data,
            status=status.HTTP_200_OK,
        )

    @action(
        detail=False,
        methods=["post"],
        serializer_class=MfaRecoveryCompletionSerializer,
        url_path="complete",
    )
    @transaction.atomic
    def complete_recovery(self, request):
        """Complete MFA recovery using recovery token"""
        recovery_request = get_object_or_404(
            self.get_queryset(),
            recovery_token=request.data["recoveryToken"],
            status=MfaRecoveryRequest.Status.APPROVED,
        )
        serializer = self.get_serializer(
            recovery_request,
            data={
                "completedAt": timezone.now(),
                "status": MfaRecoveryRequest.Status.COMPLETED,
            },
            partial=True,
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response(serializer.data, status=status.HTTP_200_OK)
