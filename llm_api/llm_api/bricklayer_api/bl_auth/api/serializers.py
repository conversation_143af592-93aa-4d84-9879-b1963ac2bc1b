import logging
from urllib.parse import quote as url_quote

import jwt
import pyqrcode
from bl_auth.cognito import get_cognito_idp_client
from bl_auth.email_utils import send_mfa_recovery_email
from core.models import MfaRecoveryRequest, Organization, User
from django.conf import settings
from django.utils import timezone
from rest_framework import serializers
from rest_framework.exceptions import ValidationError

logger = logging.getLogger(__name__)


class MfaConfigurationSerializer(serializers.Serializer):
    cognitoId = serializers.UUIDField()
    email = serializers.EmailField(write_only=True)
    qrCodeUrl = serializers.CharField(default=None, read_only=True)
    credentials = serializers.DictField()
    setUp = serializers.BooleanField(default=False)

    def validate(self, attrs):
        client = get_cognito_idp_client()

        if not attrs["setUp"]:
            association_response = client.associate_software_token(
                **attrs["credentials"]
            )
            otp_url = f"""otpauth://totp/{url_quote(f'Bricklayer AI: {attrs["email"]}')}?secret={association_response['SecretCode']}"""
            qr_code = pyqrcode.create(otp_url)

            attrs["qrCodeUrl"] = (
                f"data:image/png;base64,{qr_code.png_as_base64_str(scale=5)}"
            )

        return attrs

    def create(self, validated_data):
        return validated_data


class CognitoAuthSerializer(serializers.Serializer):
    accessToken = serializers.CharField(
        read_only=True, source="AuthenticationResult.AccessToken"
    )
    accessTokenExpires = serializers.IntegerField(
        read_only=True, source="AuthenticationResult.ExpiresIn"
    )
    idToken = serializers.CharField(
        read_only=True, source="AuthenticationResult.IdToken"
    )
    onboardingComplete = serializers.BooleanField(default=False, read_only=True)
    organizationId = serializers.CharField(
        read_only=True, source="decodedJwt.custom:OrganizationId"
    )
    refreshToken = serializers.CharField(
        read_only=True, source="AuthenticationResult.RefreshToken"
    )
    userId = serializers.CharField(read_only=True, source="decodedJwt.custom:UserId")
    userPrivileges = serializers.ListField(
        child=serializers.CharField(),
        default=["MemberAdmins"],
        read_only=True,
        source="decodedJwt.cognito:groups",
    )

    def to_representation(self, instance: dict):
        decoded_jwt = jwt.decode(
            instance["AuthenticationResult"]["IdToken"],
            options={"verify_signature": False},
        )
        organization: Organization = Organization.objects.get(
            pk=decoded_jwt["custom:OrganizationId"]
        )

        return super().to_representation(
            {
                **instance,
                "decodedJwt": decoded_jwt,
                "onboardingComplete": organization.onboarding_complete,
            }
        )


class CognitoMfaAuthSerializer(serializers.Serializer):
    authSession = CognitoAuthSerializer(default=None, read_only=True)
    email = serializers.EmailField(write_only=True)
    mfaConfiguration = MfaConfigurationSerializer(default=None, read_only=True)
    password = serializers.CharField(write_only=True)

    def _perform_cognito_login(self, email, password, client=get_cognito_idp_client()):
        return client.initiate_auth(
            ClientId=settings.COGNITO_CLIENT_ID,
            AuthFlow="USER_PASSWORD_AUTH",
            AuthParameters={
                "USERNAME": email,
                "PASSWORD": password,
            },
        )

    def validate(self, attrs):
        client = get_cognito_idp_client()
        try:
            login_response = self._perform_cognito_login(
                attrs["email"], attrs["password"], client
            )
            user = User.objects.get(email=attrs["email"])

            if user.mfa_required:
                # This user is required to use MFA, regardless
                # of its configuration state in AWS Cognito
                challenge_name = login_response.get("ChallengeName")
                mfa_set_up = challenge_name == "SOFTWARE_TOKEN_MFA"
                credentials = {}

                if challenge_name in ("MFA_SETUP", "SOFTWARE_TOKEN_MFA"):
                    # This means that either:
                    # - The AWS Cognito pool has MFA set to mandatory (here you might get MFA_SETUP)
                    # - The AWS Cognito pool has MFA set to optional and the user's MFA is configured
                    #
                    # In both cases, we will expect and use a session token as challenge credentials
                    credentials = {"Session": login_response["Session"]}

                if challenge_name is None:
                    # This means that MFA for this user isn't configured and activated
                    # on the AWS Cognito side, even though we require him to use MFA.
                    # It also means that MFA is optional for the pool, so we need
                    # to set up the user's MFA manually, intentionally.
                    credentials = {
                        "AccessToken": login_response["AuthenticationResult"][
                            "AccessToken"
                        ]
                    }

                mfa_configuration = MfaConfigurationSerializer(
                    data={
                        "cognitoId": user.cognito_id,
                        "email": attrs["email"],
                        "credentials": credentials,
                        "setUp": mfa_set_up,
                    },
                )
                mfa_configuration.is_valid(raise_exception=True)

                return {
                    **attrs,
                    "mfaConfiguration": mfa_configuration.data,
                }

            return super().validate(
                {
                    **attrs,
                    "authSession": login_response,
                }
            )
        except client.exceptions.NotAuthorizedException:
            raise ValidationError("Invalid email or password.")
        except Exception as e:
            raise ValidationError(str(e))

    def create(self, validated_data):
        return validated_data


class MfaChallengeResponseSerializer(serializers.Serializer):
    authSession = CognitoAuthSerializer(default=None, read_only=True)
    challenge = serializers.ChoiceField(
        choices=("MFA_SETUP", "SOFTWARE_TOKEN_MFA"), write_only=True
    )
    cognitoId = serializers.UUIDField(write_only=True)
    credentials = serializers.DictField(write_only=True)
    userCode = serializers.CharField(write_only=True)

    def _respond_to_auth_challenge(
        self,
        credentials,
        challenge_name,
        cognito_id,
        totp_code,
        client=get_cognito_idp_client(),
    ):
        return client.respond_to_auth_challenge(
            ClientId=settings.COGNITO_CLIENT_ID,
            ChallengeName=challenge_name,
            ChallengeResponses={
                "SOFTWARE_TOKEN_MFA_CODE": totp_code,
                "USERNAME": str(cognito_id),
            },
            **credentials,
        )

    def validate(self, attrs):
        client = get_cognito_idp_client()
        try:
            challenge_response = self._respond_to_auth_challenge(
                attrs["credentials"],
                attrs["challenge"],
                attrs["cognitoId"],
                attrs["userCode"],
                client,
            )

            return {
                **attrs,
                "authSession": challenge_response,
            }
        except client.exceptions.NotAuthorizedException as e:
            raise ValidationError(f"{e.__class__.__name__}: {e}")
        except Exception as e:
            raise ValidationError(f"{e.__class__.__name__}: {e}")

    def create(self, validated_data):
        return validated_data


class MfaVerificationSerializer(serializers.Serializer):
    challenge = serializers.CharField(
        required=False,
        write_only=True,
    )
    challengeResponse = MfaChallengeResponseSerializer(read_only=True, required=False)
    cognitoId = serializers.UUIDField(write_only=True)
    credentials = serializers.DictField(write_only=True)
    deviceName = serializers.CharField(default="", required=False, write_only=True)
    userCode = serializers.CharField(write_only=True)

    def _verify_software_token(
        self, credentials, totp_code, device_name="", client=get_cognito_idp_client()
    ):
        return client.verify_software_token(
            FriendlyDeviceName=device_name,
            UserCode=totp_code,
            **credentials,
        )

    def validate(self, attrs):
        client = get_cognito_idp_client()
        try:
            verification_response = self._verify_software_token(
                attrs["credentials"], attrs["userCode"], attrs["deviceName"], client
            )
            if verification_response["Status"] == "SUCCESS":
                user = User.objects.get(cognito_id=attrs["cognitoId"])
                user.enable_totp_mfa()

                if "challenge" in attrs:
                    challenge_data = {
                        "challenge": attrs["challenge"],
                        "cognitoId": attrs["cognitoId"],
                        "credentials": attrs["credentials"],
                        "userCode": attrs["userCode"],
                    }

                    challenge_response = MfaChallengeResponseSerializer(
                        data=challenge_data
                    )
                    challenge_response.is_valid(raise_exception=True)
                    attrs["challengeResponse"] = challenge_response.data

            return super().validate(attrs)
        except client.exceptions.NotAuthorizedException as e:
            raise ValidationError(f"{e.__class__.__name__}: {e}")
        except Exception as e:
            raise ValidationError(f"{e.__class__.__name__}: {e}")

    def create(self, validated_data):
        return validated_data


class CognitoMfaRecoverySerializer(serializers.Serializer):
    email = serializers.EmailField(write_only=True)
    mfaConfiguration = MfaConfigurationSerializer(default=None, read_only=True)
    password = serializers.CharField(write_only=True)

    def _perform_cognito_login(self, email, password, client=get_cognito_idp_client()):
        return client.initiate_auth(
            ClientId=settings.COGNITO_CLIENT_ID,
            AuthFlow="USER_PASSWORD_AUTH",
            AuthParameters={
                "USERNAME": email,
                "PASSWORD": password,
            },
        )

    def validate(self, attrs):
        attrs = super().validate(attrs)
        client = get_cognito_idp_client()
        try:
            mfa_login_response = self._perform_cognito_login(
                attrs["email"], attrs["password"], client
            )

            cognito_id = mfa_login_response["ChallengeParameters"]["USER_ID_FOR_SRP"]
            user = User.objects.get(cognito_id=cognito_id)
            user.disable_totp_mfa()

            regular_login_response = self._perform_cognito_login(
                attrs["email"], attrs["password"], client
            )

            user.enable_totp_mfa()

            if "AuthenticationResult" in regular_login_response:
                access_token = regular_login_response["AuthenticationResult"][
                    "AccessToken"
                ]
                mfa_configuration = MfaConfigurationSerializer(
                    data={
                        "cognitoId": cognito_id,
                        "email": attrs["email"],
                        "credentials": {"AccessToken": access_token},
                        "setUp": False,
                    },
                )
                mfa_configuration.is_valid(raise_exception=True)

                return {**attrs, "mfaConfiguration": mfa_configuration.data}
            return attrs
        except client.exceptions.NotAuthorizedException:
            raise ValidationError("Invalid email or password.")
        except Exception as e:
            raise ValidationError(f"{e.__class__.__name__}: {e}")

    def create(self, validated_data):
        return validated_data


class OrganizationMfaSerializer(serializers.ModelSerializer):
    class Meta:
        model = Organization
        fields = ["mfa_status"]


class UserMfaSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ["mfa_status"]

    def update(self, instance, validated_data):
        current_mfa_status = instance.mfa_status
        new_mfa_status = validated_data.get("mfa_status", current_mfa_status)

        # Handle MFA status changes
        if current_mfa_status != new_mfa_status:
            match new_mfa_status:
                case User.MfaStatus.ON:
                    instance.enable_totp_mfa()
                case User.MfaStatus.OFF:
                    instance.disable_totp_mfa()

        return instance


class MfaRecoveryRequestSerializer(serializers.ModelSerializer):
    email = serializers.EmailField(write_only=True)
    organizationName = serializers.CharField(
        source="organization.business_name", read_only=True
    )
    password = serializers.CharField(write_only=True)
    reviewedByName = serializers.CharField(
        source="reviewed_by.first_name", read_only=True
    )
    userEmail = serializers.EmailField(source="user.email", read_only=True)
    userName = serializers.CharField(source="user.first_name", read_only=True)

    class Meta:
        model = MfaRecoveryRequest
        fields = [
            "adminNotes",
            "completedAt",
            "createdAt",
            "email",
            "expiresAt",
            "id",
            "organizationName",
            "password",
            "requestReason",
            "reviewedAt",
            "reviewedByName",
            "status",
            "userEmail",
            "userName",
        ]
        read_only_fields = [
            "completedAt",
            "createdAt",
            "expiresAt",
            "id",
            "organizationName",
            "reviewedAt",
            "reviewedByName",
            "status",
            "userEmail",
            "userName",
        ]
        extra_kwargs = {
            "adminNotes": {"source": "admin_notes"},
            "completedAt": {"source": "completed_at"},
            "createdAt": {"source": "created_at"},
            "expiresAt": {"source": "expires_at"},
            "requestReason": {"source": "request_reason"},
            "reviewedAt": {"source": "reviewed_at"},
        }

    def validate(self, attrs):
        email = attrs.pop("email")
        password = attrs.pop("password")

        if not email or not password:
            raise ValidationError("Email and password are required")

        client = get_cognito_idp_client()

        # Authenticate with Cognito
        try:
            user = User.objects.get(email=email)
            client.initiate_auth(
                ClientId=settings.COGNITO_CLIENT_ID,
                AuthFlow="USER_PASSWORD_AUTH",
                AuthParameters={
                    "USERNAME": email,
                    "PASSWORD": password,
                },
            )

            return {
                **attrs,
                "user": user,
                "organization": user.organization,
            }
        except (
            client.exceptions.NotAuthorizedException,
            client.exceptions.UserNotFoundException,
            User.DoesNotExist,
        ):
            raise ValidationError("Invalid email or password")
        except Exception:
            logger.exception("MFA Recovery Request: Cognito authentication error")
            raise ValidationError("Authentication failed")


class MfaRecoveryRequestListSerializer(serializers.ModelSerializer):
    userEmail = serializers.EmailField(source="user.email", read_only=True)
    userName = serializers.SerializerMethodField()
    organizationName = serializers.CharField(
        source="organization.business_name", read_only=True
    )
    reviewedByName = serializers.SerializerMethodField()
    isExpired = serializers.BooleanField(read_only=True)

    class Meta:
        model = MfaRecoveryRequest
        fields = [
            "adminNotes",
            "completedAt",
            "createdAt",
            "expiresAt",
            "id",
            "isExpired",
            "organizationName",
            "requestReason",
            "reviewedAt",
            "reviewedByName",
            "status",
            "userEmail",
            "userName",
        ]
        extra_kwargs = {
            "adminNotes": {"source": "admin_notes"},
            "completedAt": {"source": "completed_at"},
            "createdAt": {"source": "created_at"},
            "expiresAt": {"source": "expires_at"},
            "isExpired": {"source": "is_expired"},
            "requestReason": {"source": "request_reason"},
            "reviewedAt": {"source": "reviewed_at"},
        }

    def get_userName(self, obj):
        return f"{obj.user.first_name} {obj.user.last_name}".strip()

    def get_reviewedByName(self, obj):
        if obj.reviewed_by:
            return f"{obj.reviewed_by.first_name} {obj.reviewed_by.last_name}".strip()
        return None


class MfaRecoveryApprovalSerializer(serializers.ModelSerializer):
    class Meta:
        model = MfaRecoveryRequest
        extra_kwargs = {"adminNotes": {"source": "admin_notes"}}
        fields = ("adminNotes",)

    def update(self, instance, validated_data):
        approval_decision = self.context.get("approval_decision")
        admin_user = self.context.get("admin_user")

        match approval_decision:
            case "approve":
                instance.status = MfaRecoveryRequest.Status.APPROVED
                instance.reviewed_by = admin_user
                instance.reviewed_at = timezone.now()
                instance.generate_recovery_token()

                # Send recovery email with token
                send_mfa_recovery_email(instance)

            case "deny":
                instance.status = MfaRecoveryRequest.Status.DENIED
                instance.reviewed_by = admin_user
                instance.reviewed_at = timezone.now()

        instance.admin_notes = validated_data.get("adminNotes", instance.admin_notes)
        instance.save()

        return instance


class MfaRecoveryCompletionSerializer(serializers.ModelSerializer):
    class Meta:
        extra_kwargs = {"completedAt": {"source": "completed_at"}}
        fields = ("completedAt", "status")
        model = MfaRecoveryRequest

    def validate(self, attrs):
        if (
            self.instance.token_expires_at
            and timezone.now() > self.instance.token_expires_at
        ):
            self.instance.status = MfaRecoveryRequest.Status.EXPIRED
            self.instance.save()
            raise ValidationError("Recovery token has expired")

        return super().validate(attrs)

    def update(self, instance, validated_data):
        user = instance.user
        user.reset_totp_mfa()
        user.mfa_recovery_state = User.MfaRecoveryState.NORMAL
        user.save()

        return super().update(instance, validated_data)
