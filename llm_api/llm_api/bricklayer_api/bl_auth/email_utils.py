import logging

from bl_auth.ses_client import send_ses_email
from django.conf import settings

logger = logging.getLogger(__name__)


def send_mfa_recovery_email(recovery_request):
    """
    Send MFA recovery email with recovery token to the user.

    Args:
        recovery_request: MfaRecoveryRequest instance with approved status
    """
    try:
        user = recovery_request.user
        organization = recovery_request.organization
        recovery_token = recovery_request.recovery_token

        # Email context
        context = {
            "user_name": f"{user.first_name} {user.last_name}".strip(),
            "organization_name": organization.business_name,
            "recovery_token": recovery_token,
            "token_expires_at": recovery_request.token_expires_at,
            "admin_name": (
                f"{recovery_request.reviewed_by.first_name} {recovery_request.reviewed_by.last_name}".strip()
                if recovery_request.reviewed_by
                else "Administrator"
            ),
            "support_email": "<EMAIL>",
        }

        # Email subject
        subject = f"MFA Recovery Approved - {organization.business_name}"

        # Email body (HTML version)
        html_message = f"""
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <h2 style="color: #2c3e50;">MFA Recovery Request Approved</h2>

                <p>Hello {context['user_name']},</p>

                <p>Your Multi-Factor Authentication (MFA) recovery request for <strong>{context['organization_name']}</strong> has been approved by {context['admin_name']}.</p>

                <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
                    <h3 style="margin-top: 0; color: #495057;">Recovery Link</h3>
                    <p style="margin-bottom: 15px;">Click the link below to complete your MFA recovery:</p>
                    <p style="text-align: center;">
                        <a href="{settings.PUBLIC_APP_URL}/auth/recover-mfa?recoveryToken={recovery_token}"
                           style="display: inline-block; background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; font-weight: bold;">
                            Complete MFA Recovery
                        </a>
                    </p>
                </div>

                <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;">
                    <h4 style="margin-top: 0; color: #856404;">⚠️ Important Security Information</h4>
                    <ul style="margin-bottom: 0;">
                        <li>This recovery token expires on <strong>{context['token_expires_at'].strftime('%B %d, %Y at %I:%M %p UTC')}</strong></li>
                        <li>Use this token only on the official Bricklayer platform</li>
                        <li>Do not share this token with anyone</li>
                        <li>If you did not request this recovery, contact support immediately</li>
                    </ul>
                </div>

                <h3>Next Steps:</h3>
                <ol>
                    <li>Click the "Complete MFA Recovery" button above</li>
                    <li>Follow the recovery process instructions</li>
                    <li>Set up your MFA device again for future access</li>
                </ol>

                <p>If you have any questions or need assistance, please contact our support team at <a href="mailto:{context['support_email']}">{context['support_email']}</a>.</p>

                <hr style="border: none; border-top: 1px solid #dee2e6; margin: 30px 0;">

                <p style="font-size: 12px; color: #6c757d;">
                    This email was sent by Bricklayer AI for security purposes. If you did not request MFA recovery, please contact support immediately.
                </p>
            </div>
        </body>
        </html>
        """

        # Plain text version (fallback)
        plain_message = f"""
MFA Recovery Request Approved

Hello {context['user_name']},

Your Multi-Factor Authentication (MFA) recovery request for {context['organization_name']} has been approved by {context['admin_name']}.

Recovery Link: {settings.PUBLIC_APP_URL}/auth/recover-mfa?recoveryToken={recovery_token}

IMPORTANT SECURITY INFORMATION:
- This recovery link expires on {context['token_expires_at'].strftime('%B %d, %Y at %I:%M %p UTC')}
- Use this link only on the official Bricklayer platform
- Do not share this link with anyone
- If you did not request this recovery, contact support immediately

Next Steps:
1. Click on the recovery link above or copy it to your browser
2. Follow the recovery process instructions
3. Set up your MFA device again for future access

If you have any questions or need assistance, please contact our support team at {context['support_email']}.

---
This email was sent by Bricklayer AI for security purposes. If you did not request MFA recovery, please contact support immediately.
        """

        # Send email using SES with assumed role
        response = send_ses_email(
            source=settings.INVITATION_EMAIL,
            destination=user.email,
            subject=subject,
            body_text=plain_message,
            body_html=html_message,
        )

        logger.info(
            f"MFA recovery email sent successfully to {user.email} "
            f"for recovery request {recovery_request.id}. MessageId: {response['MessageId']}"
        )

        return True

    except Exception as e:
        logger.error(
            f"Error sending MFA recovery email for request {recovery_request.id}: {str(e)}"
        )
        raise
