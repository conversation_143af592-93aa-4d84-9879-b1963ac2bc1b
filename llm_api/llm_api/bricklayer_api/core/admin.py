from core.models import Organization, User
from django.contrib import admin


@admin.register(Organization)
class OrganizationAdmin(admin.ModelAdmin):
    list_display = ("id", "business_name", "created_at")
    search_fields = ("id", "business_name")


@admin.register(User)
class UserAdmin(admin.ModelAdmin):
    list_display = ("id", "first_name", "last_name", "email")
    search_fields = ("id", "first_name", "last_name", "email")
