from django.core.management import call_command
from django.core.management.base import BaseCommand
from django.db import connections
from django.db.migrations.recorder import MigrationRecorder


class Command(BaseCommand):
    help = """
    Runs the migrations in the `core` app that need to be faked.
    As a consequence it will also run the migrations that are in between fake ones.
    """

    APP_NAME = "core"
    FAKE_MIGRATIONS: list[str] = ["0005", "0007", "0009", "0013", "0015", "0018"]

    def handle(self, *args, **options):
        connection = connections["default"]
        recorder = MigrationRecorder(connection)
        core_migrations = [
            migration_name
            for app, migration_name in recorder.applied_migrations()
            if app == self.APP_NAME
        ]

        for migration_name in sorted(self.FAKE_MIGRATIONS):
            if any(
                core_migration.startswith(f"{migration_name}")
                for core_migration in core_migrations
            ):
                self.stdout.write(
                    f"Fake migration {migration_name} already applied, skipping ..."
                )
                continue
            # Run migrations up to the one before the fake one
            self.stdout.write(f"Applying migrations up to {migration_name} ...")
            migration_num: int = int(migration_name)
            prev_migration_name: str = migration_name.replace(
                str(migration_num), str(migration_num - 1)
            )
            call_command("migrate", "core", prev_migration_name, fake=False)

            # Fake run the migration
            self.stdout.write(f"Applying fake migration {migration_name} ...")
            call_command("migrate", "core", migration_name, fake=True)

        self.stdout.write("Fake migrations have been applied")
