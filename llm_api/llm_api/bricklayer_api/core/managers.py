from core.constants import ToolType
from django.db.models import Count, Manager, Q, QuerySet


class ToolQuerySet(QuerySet):
    def coordinators(self):
        return self.filter(type=ToolType.COORDINATOR)

    def plugins(self):
        return self.filter(type=ToolType.PLUGIN)


class ProcedureQuerySet(QuerySet):
    def active(self):
        return self.filter(deleted=False)

    def deleted(self):
        return self.filter(deleted=True)

    def with_task_count(self):
        return self.annotate(task_count=Count("task", distinct=True))

    def with_procedure_run_count(self):
        return self.annotate(total_procedure_runs=Count("procedurerun", distinct=True))

    def with_task_run_count(self):
        return self.annotate(
            total_task_runs=Count("procedurerun__taskrun", distinct=True)
        )

    def with_ratings_count(self):
        return self.annotate(
            total_ratings_count=Count("procedurerun__procedurerunrating", distinct=True)
        )

    def with_thumbs_up_count(self):
        return self.annotate(
            thumbs_up_count=Count(
                "procedurerun__procedurerunrating",
                distinct=True,
                filter=Q(procedurerun__procedurerunrating__like=True),
            )
        )

    def with_tools(self):
        return self.prefetch_related("task_set__tools")


class ActiveProcedureManager(Manager.from_queryset(ProcedureQuerySet)):
    def get_queryset(self):
        return super().get_queryset().active()


class ActiveTaskManager(Manager):
    def get_queryset(self):
        return super().get_queryset().filter(deleted=False)
