from enum import StrEnum


class ToolType(StrEnum):
    COORDINATOR = "coordinator"
    PLUGIN = "plugin"
    DATASTORE = "datastore"
    SERVICE = "service"
    PUBLIC_DATA_SOURCE = "publicDataSource"
    REPORTER = "reporter"


class LLMType(StrEnum):
    COORDINATOR = "coordinator"
    PLUGIN = "plugin"
    DATASTORE = "datastore"
    SERVICE = "service"
    PUBLIC_DATA_SOURCE = "publicDataSource"
    TIDAL = "tidal"
    PATCH_TUESDAY = "patchTuesday"
    CSV_TOOL = "csvTool"
    REPORTER = "reporter"
    LONG_REPORTER = "longReporter"
    PROCEDURE = "procedure"


UI_SPEC_PROCEDURE_WRAPPER_TOOL = {
    "schema": {
        "type": "object",
        "properties": {
            "inputs": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "Name": {"type": "string", "title": "Name"},
                        "Description": {
                            "type": "string",
                            "title": "Description",
                        },
                    },
                },
                "title": "Inputs",
            },
            "procedureId": {"type": "integer", "title": "Procedure ID"},
            "procedureOverview": {
                "type": "string",
                "title": "Procedure Overview",
            },
        },
    },
    "uischema": {
        "inputs": {
            "items": {
                "ui:options": {"label": False},
                "Description": {
                    "ui:widget": "textarea",
                    "ui:options": {"rows": 3},
                },
            },
        },
        "ui:order": ["procedureId", "procedureOverview", "inputs"],
        "ui:readonly": True,
        "procedureOverview": {
            "ui:widget": "textarea",
            "ui:options": {"rows": 3},
        },
    },
}
