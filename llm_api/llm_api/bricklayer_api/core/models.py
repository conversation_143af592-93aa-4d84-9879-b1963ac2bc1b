import logging
import uuid
from datetime import timedelta

from bl_auth.cognito import get_cognito_idp_client
from bricklayer_api.cuid2 import GENERATE_CUID2
from core.managers import (
    ActiveProcedureManager,
    ActiveTaskManager,
    ProcedureQuerySet,
    ToolQuerySet,
)
from django.conf import settings
from django.core.exceptions import ValidationError
from django.db import connections, models, transaction
from django.utils import timezone

logger = logging.getLogger(__name__)


class AdvertisingProcedure(models.Model):
    id = models.AutoField(
        db_column="Id", primary_key=True
    )  # Field name made lowercase.
    name = models.TextField(db_column="Name")  # Field name made lowercase.
    ui_description = models.TextField(
        db_column="UIDescription"
    )  # Field name made lowercase.
    interested_list = models.TextField(
        db_column="InterestedList", blank=True, null=True
    )  # Field name made lowercase. This field type is a guess.
    created_at = models.DateTimeField(
        db_column="CreatedAt"
    )  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = "AdvertisingProcedure"


class ApiClient(models.Model):
    id = models.TextField(
        db_column="Id", primary_key=True
    )  # Field name made lowercase.
    organization = models.ForeignKey(
        "Organization", on_delete=models.DO_NOTHING, db_column="OrganizationId"
    )  # Field name made lowercase.
    api_key = models.TextField(db_column="ApiKey")  # Field name made lowercase.
    description = models.TextField(
        db_column="Description", blank=True, null=True
    )  # Field name made lowercase.
    name = models.TextField(
        db_column="Name", blank=True, null=True
    )  # Field name made lowercase.
    is_enabled = models.BooleanField(
        db_column="IsEnabled"
    )  # Field name made lowercase.
    usd_usage = models.FloatField(db_column="USDUsage")  # Field name made lowercase.
    created_at = models.DateTimeField(
        db_column="CreatedAt"
    )  # Field name made lowercase.
    updated_at = models.DateTimeField(
        db_column="UpdatedAt"
    )  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = "ApiClient"


class BaseTemplate(models.Model):
    id = models.AutoField(
        db_column="Id", primary_key=True
    )  # Field name made lowercase.
    spec = models.TextField(
        db_column="Spec"
    )  # Field name made lowercase. This field type is a guess.
    configurable_spec_fields = models.TextField(
        db_column="ConfigurableSpecFields", blank=True, null=True
    )  # Field name made lowercase. This field type is a guess.
    img_url = models.TextField(
        db_column="ImgUrl", blank=True, null=True
    )  # Field name made lowercase.
    created_at = models.DateTimeField(
        db_column="CreatedAt"
    )  # Field name made lowercase.
    updated_at = models.DateTimeField(
        db_column="UpdatedAt"
    )  # Field name made lowercase.
    display_description = models.TextField(
        db_column="DisplayDescription"
    )  # Field name made lowercase.
    display_name = models.TextField(
        db_column="DisplayName"
    )  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = "BaseTemplate"


class CategoryTemplate(models.Model):
    id = models.AutoField(
        db_column="Id", primary_key=True
    )  # Field name made lowercase.
    spec = models.TextField(
        db_column="Spec"
    )  # Field name made lowercase. This field type is a guess.
    configurable_spec_fields = models.TextField(
        db_column="ConfigurableSpecFields", blank=True, null=True
    )  # Field name made lowercase. This field type is a guess.
    type = models.TextField(
        db_column="Type"
    )  # Field name made lowercase. This field type is a guess.
    llm_type = models.TextField(
        db_column="LLMType", blank=True, null=True
    )  # Field name made lowercase. This field type is a guess.
    img_url = models.TextField(
        db_column="ImgUrl", blank=True, null=True
    )  # Field name made lowercase.
    created_at = models.DateTimeField(
        db_column="CreatedAt", auto_now_add=True
    )  # Field name made lowercase.
    updated_at = models.DateTimeField(
        db_column="UpdatedAt", auto_now=True
    )  # Field name made lowercase.
    display_description = models.TextField(
        db_column="DisplayDescription"
    )  # Field name made lowercase.
    display_name = models.TextField(
        db_column="DisplayName"
    )  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = "CategoryTemplate"


class Filter(models.Model):
    pk = models.CompositePrimaryKey(
        "conversation_id", "organization_id", "tool_id", "parent_tool_id"
    )
    conversation_id = models.TextField(
        db_column="ConversationId"
    )  # Field name made lowercase.
    organization_id = models.TextField(
        db_column="OrganizationId"
    )  # Field name made lowercase.
    tool_id = models.TextField(db_column="ToolId")  # Field name made lowercase.
    parent_tool_id = models.TextField(
        db_column="ParentToolId"
    )  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = "Filter"
        unique_together = (
            ("conversation_id", "organization_id", "tool_id", "parent_tool_id"),
        )


class ManagedDatastoreDocsSnapshot(models.Model):
    id = models.AutoField(
        db_column="Id", primary_key=True
    )  # Field name made lowercase.
    organization = models.ForeignKey(
        "Organization", on_delete=models.DO_NOTHING, db_column="OrganizationId"
    )  # Field name made lowercase.
    provided_document_id = models.TextField(
        db_column="ProvidedDocumentId"
    )  # Field name made lowercase.
    provider = models.TextField(
        db_column="Provider"
    )  # Field name made lowercase. This field type is a guess.
    datastores_mapping = models.JSONField(
        db_column="DatastoresMapping"
    )  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = "ManagedDatastoreDocsSnapshot"


class OrgAdminInvitation(models.Model):
    id = models.AutoField(
        db_column="Id", primary_key=True
    )  # Field name made lowercase.
    email = models.TextField(db_column="Email")  # Field name made lowercase.
    invite_code = models.TextField(db_column="InviteCode")  # Field name made lowercase.
    created_at = models.DateTimeField(
        db_column="CreatedAt"
    )  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = "OrgAdminInvitation"


class OrgMembersInvitation(models.Model):
    id = models.AutoField(
        db_column="Id", primary_key=True
    )  # Field name made lowercase.
    email = models.TextField(db_column="Email")  # Field name made lowercase.
    invite_code = models.TextField(db_column="InviteCode")  # Field name made lowercase.
    organization = models.ForeignKey(
        "Organization", on_delete=models.DO_NOTHING, db_column="OrganizationId"
    )  # Field name made lowercase.
    expiration_time = models.DateTimeField(
        db_column="ExpirationTime"
    )  # Field name made lowercase.
    created_at = models.DateTimeField(
        db_column="CreatedAt"
    )  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = "OrgMembersInvitation"


class Organization(models.Model):
    class MfaStatus(models.TextChoices):
        OFF = "OFF"
        OPTIONAL = "OPTIONAL"
        REQUIRED = "REQUIRED"

    id = models.TextField(
        db_column="Id", primary_key=True
    )  # Field name made lowercase.
    max_usd_usage = models.FloatField(
        db_column="MaxUsdUsage", default=100
    )  # Field name made lowercase.
    current_usd_usage = models.FloatField(
        db_column="CurrentUsdUsage", default=0
    )  # Field name made lowercase.
    business_name = models.TextField(
        db_column="BusinessName"
    )  # Field name made lowercase.
    industry = models.TextField(
        blank=True, db_column="Industry"
    )  # Field name made lowercase.
    license = models.TextField(
        blank=True, db_column="License"
    )  # Field name made lowercase.
    created_at = models.DateTimeField(
        auto_now_add=True, db_column="CreatedAt"
    )  # Field name made lowercase.
    updated_at = models.DateTimeField(
        auto_now=True, db_column="UpdatedAt"
    )  # Field name made lowercase.
    inactivity_timeout = models.IntegerField(
        db_column="InactivityTimeout", default=60
    )  # Field name made lowercase.

    onboarding_complete = models.BooleanField(default=False)
    onboarding_data = models.JSONField(default=dict)
    mfa_status = models.CharField(
        choices=MfaStatus, default=MfaStatus.OFF, max_length=50
    )

    class Meta:
        managed = True
        db_table = "Organization"

    @property
    def mfa_enabled(self) -> bool:
        return self.mfa_status in (
            Organization.MfaStatus.OPTIONAL,
            Organization.MfaStatus.REQUIRED,
        )

    @property
    def mfa_required(self) -> bool:
        return self.mfa_status == Organization.MfaStatus.REQUIRED

    @property
    def mfa_off(self) -> bool:
        return self.mfa_status == Organization.MfaStatus.OFF


class Procedure(models.Model):
    id = models.AutoField(
        db_column="Id", primary_key=True
    )  # Field name made lowercase.
    name = models.TextField(db_column="Name")  # Field name made lowercase.
    ui_description = models.TextField(
        db_column="UIDescription"
    )  # Field name made lowercase.
    llm_description = models.TextField(
        db_column="LLMDescription"
    )  # Field name made lowercase.
    organization = models.ForeignKey(
        Organization, on_delete=models.DO_NOTHING, db_column="OrganizationId"
    )  # Field name made lowercase.
    inputs = models.JSONField(db_column="Inputs")  # Field name made lowercase.
    long_term_memory = models.BooleanField(
        db_column="LongTermMemory"
    )  # Field name made lowercase.
    is_enabled = models.BooleanField(
        db_column="IsEnabled", default=True
    )  # Field name made lowercase.
    deleted = models.BooleanField(
        db_column="Deleted", default=False
    )  # Field name made lowercase.
    created_at = models.DateTimeField(
        db_column="CreatedAt", auto_now_add=True
    )  # Field name made lowercase.
    updated_at = models.DateTimeField(
        db_column="UpdatedAt", auto_now=True
    )  # Field name made lowercase.

    objects = ProcedureQuerySet.as_manager()
    active_objects = ActiveProcedureManager()

    class Meta:
        managed = False
        db_table = "Procedure"

    @property
    def procedure_tool_id(self) -> str:
        return f"PROCEDURE-{self.pk}"


class ProcedureRecurringRunEvents(models.Model):
    id = models.TextField(
        db_column="Id", primary_key=True
    )  # Field name made lowercase.
    procedure = models.ForeignKey(
        Procedure, on_delete=models.DO_NOTHING, db_column="ProcedureId"
    )  # Field name made lowercase.
    cron_setup = models.TextField(db_column="CronSetup")  # Field name made lowercase.
    inputs = models.JSONField(db_column="Inputs")  # Field name made lowercase.
    user = models.ForeignKey(
        "User", on_delete=models.DO_NOTHING, db_column="UserId"
    )  # Field name made lowercase.
    organization = models.ForeignKey(
        Organization, on_delete=models.DO_NOTHING, db_column="OrganizationId"
    )  # Field name made lowercase.
    created_at = models.DateTimeField(
        db_column="CreatedAt"
    )  # Field name made lowercase.
    updated_at = models.DateTimeField(
        db_column="UpdatedAt"
    )  # Field name made lowercase.
    is_enabled = models.BooleanField(
        db_column="IsEnabled"
    )  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = "ProcedureRecurringRunEvents"


class ProcedureRun(models.Model):
    id = models.TextField(
        db_column="Id", primary_key=True
    )  # Field name made lowercase.
    procedure = models.ForeignKey(
        Procedure, on_delete=models.DO_NOTHING, db_column="ProcedureId"
    )  # Field name made lowercase.
    ui_status = models.TextField(
        db_column="UIStatus"
    )  # Field name made lowercase. This field type is a guess.
    llm_status = models.TextField(
        db_column="LLMStatus"
    )  # Field name made lowercase. This field type is a guess.
    inputs = models.JSONField(db_column="Inputs")  # Field name made lowercase.
    created_at = models.DateTimeField(
        db_column="CreatedAt"
    )  # Field name made lowercase.
    outcome_location = models.TextField(
        db_column="OutcomeLocation", blank=True, null=True
    )  # Field name made lowercase.
    outcome = models.TextField(
        db_column="Outcome", blank=True, null=True
    )  # Field name made lowercase.
    executive_summary = models.TextField(
        db_column="ExecutiveSummary", blank=True, null=True
    )  # Field name made lowercase.
    failed_reason = models.TextField(
        db_column="FailedReason", blank=True, null=True
    )  # Field name made lowercase.
    key_findings = models.TextField(
        db_column="KeyFindings", blank=True, null=True
    )  # Field name made lowercase.
    summarization_cost = models.FloatField(
        db_column="SummarizationCost"
    )  # Field name made lowercase.
    trigger = models.TextField(
        db_column="Trigger"
    )  # Field name made lowercase. This field type is a guess.
    updated_at = models.DateTimeField(
        db_column="UpdatedAt"
    )  # Field name made lowercase.
    api_client = models.ForeignKey(
        ApiClient,
        on_delete=models.DO_NOTHING,
        db_column="ApiClientId",
        blank=True,
        null=True,
    )  # Field name made lowercase.
    user = models.ForeignKey(
        "User", on_delete=models.DO_NOTHING, db_column="UserId", blank=True, null=True
    )  # Field name made lowercase.
    custom_name = models.TextField(db_column="CustomName")  # Field name made lowercase.
    completed_at = models.DateTimeField(
        db_column="CompletedAt", blank=True, null=True
    )  # Field name made lowercase.
    actions_taken = models.TextField(
        db_column="ActionsTaken", blank=True, null=True
    )  # Field name made lowercase.
    deleted = models.BooleanField(db_column="Deleted")  # Field name made lowercase.
    tool_meta_profile = models.TextField(
        db_column="ToolMetaProfile", blank=True, null=True
    )  # Field name made lowercase.
    dashboard_output = models.JSONField(
        db_column="DashboardOutput", blank=True, null=True
    )

    class Meta:
        managed = True
        db_table = "ProcedureRun"


class ProcedureRunRating(models.Model):
    id = models.AutoField(
        db_column="Id", primary_key=True
    )  # Field name made lowercase.
    procedure_run = models.ForeignKey(
        ProcedureRun, on_delete=models.DO_NOTHING, db_column="ProcedureRunId"
    )  # Field name made lowercase.
    user = models.ForeignKey(
        "User", on_delete=models.DO_NOTHING, db_column="UserId"
    )  # Field name made lowercase.
    like = models.BooleanField(db_column="Like")  # Field name made lowercase.
    comment = models.TextField(
        db_column="Comment", blank=True, null=True
    )  # Field name made lowercase.
    created_at = models.DateTimeField(
        db_column="CreatedAt"
    )  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = "ProcedureRunRating"
        unique_together = (("procedure_run", "user"),)


# TODO: Keep on refactoring models
class PublicData(models.Model):
    id = models.AutoField(
        db_column="Id", primary_key=True
    )  # Field name made lowercase.
    feed = models.ForeignKey(
        "PublicDataSourceFeed", on_delete=models.DO_NOTHING, db_column="FeedId"
    )  # Field name made lowercase.
    title = models.TextField(db_column="Title")  # Field name made lowercase.
    url = models.TextField(db_column="URL")  # Field name made lowercase.
    content = models.TextField(
        db_column="Content", blank=True, null=True
    )  # Field name made lowercase.
    published_at = models.DateTimeField(
        db_column="PublishedAt", blank=True, null=True
    )  # Field name made lowercase.
    metadata = models.JSONField(
        db_column="Metadata", blank=True, null=True
    )  # Field name made lowercase.
    created_at = models.DateTimeField(
        db_column="CreatedAt"
    )  # Field name made lowercase.
    updated_at = models.DateTimeField(
        db_column="UpdatedAt"
    )  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = "PublicData"
        unique_together = (("feed", "url"),)


class PublicDataSourceFeed(models.Model):
    id = models.AutoField(
        db_column="Id", primary_key=True
    )  # Field name made lowercase.
    tool = models.ForeignKey(
        "Tool", on_delete=models.DO_NOTHING, db_column="ToolId"
    )  # Field name made lowercase.
    name = models.TextField(db_column="Name")  # Field name made lowercase.
    url = models.TextField(db_column="URL")  # Field name made lowercase.
    metadata = models.JSONField(db_column="Metadata")  # Field name made lowercase.
    created_at = models.DateTimeField(
        db_column="CreatedAt"
    )  # Field name made lowercase.
    last_checked_at = models.DateTimeField(
        db_column="LastCheckedAt", blank=True, null=True
    )  # Field name made lowercase.
    last_item_pub_date = models.DateTimeField(
        db_column="LastItemPubDate", blank=True, null=True
    )  # Field name made lowercase.
    updated_at = models.DateTimeField(
        db_column="UpdatedAt"
    )  # Field name made lowercase.
    deleted = models.BooleanField(db_column="Deleted")  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = "PublicDataSourceFeed"


class PublicDataSourceOptions(models.Model):
    id = models.AutoField(
        db_column="Id", primary_key=True
    )  # Field name made lowercase.
    tool = models.ForeignKey(
        "Tool", on_delete=models.DO_NOTHING, db_column="ToolId"
    )  # Field name made lowercase.
    organization = models.ForeignKey(
        Organization, on_delete=models.DO_NOTHING, db_column="OrganizationId"
    )  # Field name made lowercase.
    disabled_sources = models.JSONField(
        db_column="DisabledSources"
    )  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = "PublicDataSourceOptions"


class SavedTableView(models.Model):
    id = models.AutoField(
        db_column="Id", primary_key=True
    )  # Field name made lowercase.
    user = models.ForeignKey(
        "User", on_delete=models.DO_NOTHING, db_column="UserId"
    )  # Field name made lowercase.
    name = models.TextField(db_column="Name")  # Field name made lowercase.
    query_params = models.TextField(
        db_column="QueryParams"
    )  # Field name made lowercase. This field type is a guess.
    column_settings = models.TextField(
        db_column="ColumnSettings", blank=True, null=True
    )  # Field name made lowercase. This field type is a guess.
    created_at = models.DateTimeField(
        db_column="CreatedAt"
    )  # Field name made lowercase.
    updated_at = models.DateTimeField(
        db_column="UpdatedAt"
    )  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = "SavedTableView"
        unique_together = (("user", "name"),)


class Task(models.Model):
    id = models.AutoField(
        db_column="Id", primary_key=True
    )  # Field name made lowercase.
    prompt = models.TextField(db_column="Prompt")  # Field name made lowercase.
    inputs_names = models.TextField(
        db_column="InputsNames", blank=True, null=True
    )  # Field name made lowercase. This field type is a guess.
    procedure = models.ForeignKey(
        Procedure, on_delete=models.DO_NOTHING, db_column="ProcedureId"
    )  # Field name made lowercase.
    deleted = models.BooleanField(
        db_column="Deleted", default=False
    )  # Field name made lowercase.
    ui_order = models.IntegerField(db_column="UIOrder")  # Field name made lowercase.
    created_at = models.DateTimeField(
        db_column="CreatedAt", auto_now_add=True
    )  # Field name made lowercase.
    updated_at = models.DateTimeField(
        db_column="UpdatedAt", auto_now=True
    )  # Field name made lowercase.
    description = models.TextField(
        db_column="Description"
    )  # Field name made lowercase.
    tools = models.ManyToManyField("Tool", through="TasksToolsRelationship")

    _cached_children: models.QuerySet["Task"] | None = None
    _cached_parents: models.QuerySet["Task"] | None = None

    class Meta:
        db_table = "Task"

    active_objects = ActiveTaskManager()
    objects = models.Manager()

    @property
    def children_tasks(self):
        """
        Inefficient backup method to retrieve children tasks until we are able
        to add a primary key to _TasksRelationship.
        """
        if self._cached_children is None:
            cursor = connections["default"].cursor()
            cursor.execute(
                f'SELECT "B" FROM "_TasksRelationship" WHERE "A" = {self.pk}'
            )
            self._cached_children = Task.objects.filter(
                pk__in=[row[0] for row in cursor.fetchall()]
            )

        return self._cached_children

    def add_children(self, *child_task_ids):
        """
        Inefficient backup method to add child tasks.
        This will be done through a ManyToManyField later on.
        """
        child_task_relationships: list[str] = [
            f"({self.pk}, {child_task_id})" for child_task_id in child_task_ids
        ]
        cursor = connections["default"].cursor()
        cursor.execute(
            'INSERT INTO "_TasksRelationship" ("A", "B") '
            f'VALUES {", ".join(child_task_relationships)}'
        )
        # Reset cached children
        self._cached_children = None

    @property
    def parent_tasks(self):
        """
        Inefficient backup method to retrieve parent tasks until we are able
        to add a primary key to _TasksRelationship.
        """
        if self._cached_parents is None:
            cursor = connections["default"].cursor()
            cursor.execute(
                f'SELECT "A" FROM "_TasksRelationship" WHERE "B" = {self.pk}'
            )
            self._cached_parents = Task.objects.filter(
                pk__in=[row[0] for row in cursor.fetchall()]
            )

        return self._cached_parents

    def add_parents(self, *parent_task_ids):
        """
        Inefficient backup method to create a parent <-> child task relationship.
        This will be done through a ManyToManyField later on.
        """
        parent_task_relationships: list[str] = [
            f"({parent_task_id}, {self.pk})" for parent_task_id in parent_task_ids
        ]
        cursor = connections["default"].cursor()
        logger.info(
            'INSERT INTO "_TasksRelationship" ("A", "B") '
            f'VALUES {", ".join(parent_task_relationships)}'
        )
        cursor.execute(
            'INSERT INTO "_TasksRelationship" ("A", "B") '
            f'VALUES {", ".join(parent_task_relationships)}'
        )
        # Reset cached parents
        self._cached_parents = None

    def connect_tools(self, *tool_ids):
        """
        Inefficient backup method to connect a task to tools.
        This will be done through a ManyToManyField later on.
        """
        task_tool_relationships: list[str] = [
            f"({self.pk}, '{tool_id}')" for tool_id in tool_ids
        ]
        cursor = connections["default"].cursor()
        cursor.execute(
            'INSERT INTO "_TasksToolsRelationship" ("A", "B") '
            f'VALUES {", ".join(task_tool_relationships)}'
        )


class TaskRun(models.Model):
    id = models.TextField(
        db_column="Id", primary_key=True
    )  # Field name made lowercase.
    status = models.TextField(
        db_column="Status"
    )  # Field name made lowercase. This field type is a guess.
    outcome_location = models.TextField(
        db_column="OutcomeLocation", blank=True, null=True
    )  # Field name made lowercase.
    created_at = models.DateTimeField(
        db_column="CreatedAt"
    )  # Field name made lowercase.
    task = models.ForeignKey(
        Task, on_delete=models.DO_NOTHING, db_column="TaskId"
    )  # Field name made lowercase.
    procedure_run = models.ForeignKey(
        ProcedureRun, on_delete=models.DO_NOTHING, db_column="ProcedureRunId"
    )  # Field name made lowercase.
    outcome = models.TextField(
        db_column="Outcome", blank=True, null=True
    )  # Field name made lowercase.
    sources = models.JSONField(
        db_column="Sources", blank=True, null=True
    )  # Field name made lowercase.
    failed_reason = models.TextField(
        db_column="FailedReason", blank=True, null=True
    )  # Field name made lowercase.
    llm_cost = models.FloatField(db_column="LLMCost")  # Field name made lowercase.
    updated_at = models.DateTimeField(
        db_column="UpdatedAt"
    )  # Field name made lowercase.
    deleted = models.BooleanField(db_column="Deleted")  # Field name made lowercase.
    description = models.TextField(
        db_column="Description"
    )  # Field name made lowercase.
    prompt_used = models.TextField(
        db_column="PromptUsed", blank=True, null=True
    )  # Field name made lowercase.
    tools_used = models.JSONField(
        db_column="ToolsUsed", blank=True, null=True
    )  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = "TaskRun"


class TemplateProcedure(models.Model):
    id = models.AutoField(
        db_column="Id", primary_key=True
    )  # Field name made lowercase.
    name = models.TextField(db_column="Name")  # Field name made lowercase.
    created_by = models.TextField(db_column="CreatedBy")  # Field name made lowercase.
    ui_description = models.TextField(
        db_column="UIDescription"
    )  # Field name made lowercase.
    llm_description = models.TextField(
        db_column="LLMDescription"
    )  # Field name made lowercase.
    inputs = models.JSONField(db_column="Inputs")  # Field name made lowercase.
    tags = models.JSONField(db_column="Tags")  # Field name made lowercase.
    created_at = models.DateTimeField(
        db_column="CreatedAt"
    )  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = "TemplateProcedure"


class TemplateTask(models.Model):
    id = models.AutoField(
        db_column="Id", primary_key=True
    )  # Field name made lowercase.
    prompt = models.TextField(db_column="Prompt")  # Field name made lowercase.
    inputs_names = models.TextField(
        db_column="InputsNames", blank=True, null=True
    )  # Field name made lowercase. This field type is a guess.
    template_procedure = models.ForeignKey(
        TemplateProcedure, on_delete=models.DO_NOTHING, db_column="TemplateProcedureId"
    )  # Field name made lowercase.
    ui_order = models.IntegerField(db_column="UIOrder")  # Field name made lowercase.
    created_at = models.DateTimeField(
        db_column="CreatedAt"
    )  # Field name made lowercase.
    tool_template = models.ForeignKey(
        "ToolTemplate",
        on_delete=models.DO_NOTHING,
        db_column="toolTemplateId",
        blank=True,
        null=True,
    )  # Field name made lowercase.
    description = models.TextField(
        db_column="Description"
    )  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = "TemplateTask"


class Tool(models.Model):
    id = models.TextField(
        db_column="Id", default=GENERATE_CUID2, primary_key=True
    )  # Field name made lowercase.
    display_name = models.TextField(
        db_column="DisplayName"
    )  # Field name made lowercase.
    display_description = models.TextField(
        db_column="DisplayDescription"
    )  # Field name made lowercase.
    type = models.TextField(
        db_column="Type"
    )  # Field name made lowercase. This field type is a guess.
    configured_by = models.CharField(
        db_column="ConfiguredBy"
    )  # Field name made lowercase.
    spec = models.TextField(
        db_column="Spec"
    )  # Field name made lowercase. This field type is a guess.
    created_at = models.DateTimeField(
        db_column="CreatedAt", auto_now_add=True
    )  # Field name made lowercase.
    updated_at = models.DateTimeField(
        db_column="UpdatedAt", auto_now=True
    )  # Field name made lowercase.
    configurable_spec_fields = models.JSONField(
        db_column="ConfigurableSpecFields", blank=True, null=True
    )  # Field name made lowercase.
    llm_type = models.TextField(
        db_column="LLMType", blank=True, null=True
    )  # Field name made lowercase. This field type is a guess.
    img_url = models.TextField(
        db_column="ImgUrl", blank=True, null=True
    )  # Field name made lowercase.
    disabled_in_organizations = models.TextField(
        db_column="DisabledInOrganizations", blank=True, null=True
    )  # Field name made lowercase. This field type is a guess.
    template = models.ForeignKey(
        "ToolTemplate",
        on_delete=models.DO_NOTHING,
        db_column="TemplateId",
        blank=True,
        null=True,
    )  # Field name made lowercase.

    _cached_children: models.QuerySet["Tool"] | None = None
    _cached_parents: models.QuerySet["Tool"] | None = None

    objects = ToolQuerySet.as_manager()

    class Meta:
        managed = False
        db_table = "Tool"

    @property
    def children_tools(self):
        """
        Inefficient backup solution to retrieve children tools until we are able
        to add a primary key to _ToolsRelationship.
        """
        if self._cached_children is None:
            cursor = connections["default"].cursor()
            cursor.execute(
                f'SELECT "B" FROM "_ToolsRelationship" WHERE "A" = \'{self.pk}\''
            )
            self._cached_children = Tool.objects.filter(
                pk__in=[row[0] for row in cursor.fetchall()]
            )

        return self._cached_children

    def add_children(self, *child_tool_ids):
        """
        Inefficient backup method to add child tools.
        This will be done through a ManyToManyField later on.
        """
        child_tool_relationships: list[str] = [
            f"('{self.pk}', '{child_tool_id}')" for child_tool_id in child_tool_ids
        ]
        cursor = connections["default"].cursor()
        cursor.execute(
            'INSERT INTO "_ToolsRelationship" ("A", "B") '
            f'VALUES {", ".join(child_tool_relationships)}'
        )
        # Reset cached children
        self._cached_children = None

    @property
    def parent_tools(self):
        """
        Inefficient backup solution to retrieve parent tools until we are able
        to add a primary key to _ToolsRelationship.
        """
        if self._cached_parents is None:
            cursor = connections["default"].cursor()
            cursor.execute(
                f'SELECT "A" FROM "_ToolsRelationship" WHERE "B" = \'{self.pk}\''
            )
            self._cached_parents = Tool.objects.filter(
                pk__in=[row[0] for row in cursor.fetchall()]
            )

        return self._cached_parents


class ToolMeta(models.Model):
    id = models.AutoField(
        db_column="Id", primary_key=True
    )  # Field name made lowercase.
    organization = models.ForeignKey(
        Organization, on_delete=models.DO_NOTHING, db_column="OrganizationId"
    )  # Field name made lowercase.
    tool = models.ForeignKey(
        Tool, on_delete=models.DO_NOTHING, db_column="ToolId"
    )  # Field name made lowercase.
    type = models.TextField(db_column="Type")  # Field name made lowercase.
    data = models.JSONField(db_column="Data")  # Field name made lowercase.
    profile = models.TextField(db_column="Profile")  # Field name made lowercase.

    class Meta:
        db_table = "ToolMeta"
        unique_together = (("tool", "profile"),)


class ToolTemplate(models.Model):
    id = models.AutoField(
        db_column="Id", primary_key=True
    )  # Field name made lowercase.
    spec = models.TextField(
        db_column="Spec"
    )  # Field name made lowercase. This field type is a guess.
    configurable_spec_fields = models.TextField(
        db_column="ConfigurableSpecFields", blank=True, null=True
    )  # Field name made lowercase. This field type is a guess.
    type = models.TextField(
        db_column="Type"
    )  # Field name made lowercase. This field type is a guess.
    llm_type = models.TextField(
        db_column="LLMType", blank=True, null=True
    )  # Field name made lowercase. This field type is a guess.
    img_url = models.TextField(
        db_column="ImgUrl", blank=True, null=True
    )  # Field name made lowercase.
    configured_by = models.CharField(
        db_column="ConfiguredBy"
    )  # Field name made lowercase.
    created_at = models.DateTimeField(
        db_column="CreatedAt", auto_now_add=True
    )  # Field name made lowercase.
    updated_at = models.DateTimeField(
        db_column="UpdatedAt", auto_now=True
    )  # Field name made lowercase.
    display_description = models.TextField(
        db_column="DisplayDescription"
    )  # Field name made lowercase.
    display_name = models.TextField(
        db_column="DisplayName"
    )  # Field name made lowercase.
    category_template = models.IntegerField(
        db_column="CategoryTemplateId", blank=True, null=True
    )  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = "ToolTemplate"


class UnconfiguredProcedure(models.Model):
    id = models.AutoField(
        db_column="Id", primary_key=True
    )  # Field name made lowercase.
    name = models.TextField(db_column="Name")  # Field name made lowercase.
    ui_description = models.TextField(
        db_column="UIDescription"
    )  # Field name made lowercase.
    llm_description = models.TextField(
        db_column="LLMDescription"
    )  # Field name made lowercase.
    inputs = models.JSONField(db_column="Inputs")  # Field name made lowercase.
    created_at = models.DateTimeField(
        db_column="CreatedAt"
    )  # Field name made lowercase.
    created_by = models.TextField(db_column="CreatedBy")  # Field name made lowercase.
    organization = models.ForeignKey(
        Organization, on_delete=models.DO_NOTHING, db_column="OrganizationId"
    )  # Field name made lowercase.
    tool_templates_ids = models.TextField(
        db_column="ToolTemplatesIds", blank=True, null=True
    )  # Field name made lowercase. This field type is a guess.

    class Meta:
        managed = False
        db_table = "UnconfiguredProcedure"


class UnconfiguredTask(models.Model):
    id = models.AutoField(
        db_column="Id", primary_key=True
    )  # Field name made lowercase.
    prompt = models.TextField(db_column="Prompt")  # Field name made lowercase.
    inputs_names = models.TextField(
        db_column="InputsNames", blank=True, null=True
    )  # Field name made lowercase. This field type is a guess.
    ui_order = models.IntegerField(db_column="UIOrder")  # Field name made lowercase.
    created_at = models.DateTimeField(
        db_column="CreatedAt"
    )  # Field name made lowercase.
    unconfigured_procedure = models.ForeignKey(
        UnconfiguredProcedure,
        on_delete=models.DO_NOTHING,
        db_column="UnconfiguredProcedureId",
    )  # Field name made lowercase.
    description = models.TextField(
        db_column="Description"
    )  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = "UnconfiguredTask"


class User(models.Model):
    class MfaStatus(models.TextChoices):
        OFF = "OFF"
        ON = "ON"

    class MfaRecoveryState(models.TextChoices):
        NORMAL = "NORMAL"
        PENDING = "PENDING"

    id = models.TextField(
        db_column="Id", primary_key=True
    )  # Field name made lowercase.
    first_name = models.TextField(db_column="FirstName")  # Field name made lowercase.
    last_name = models.TextField(db_column="LastName")  # Field name made lowercase.
    email = models.TextField(db_column="Email")  # Field name made lowercase.
    role = models.TextField(blank=True, db_column="Role")  # Field name made lowercase.
    organization_position = models.TextField(
        blank=True, db_column="OrganizationPosition"
    )  # Field name made lowercase.
    organization = models.ForeignKey(
        Organization, on_delete=models.DO_NOTHING, db_column="OrganizationId"
    )  # Field name made lowercase.
    is_org_admin = models.BooleanField(
        db_column="IsOrgAdmin"
    )  # Field name made lowercase.
    usd_usage = models.FloatField(
        db_column="USDUsage", default=0
    )  # Field name made lowercase.
    created_at = models.DateTimeField(
        auto_now_add=True, db_column="CreatedAt"
    )  # Field name made lowercase.
    updated_at = models.DateTimeField(
        auto_now=True, db_column="UpdatedAt"
    )  # Field name made lowercase.
    cognito_id = models.TextField(
        db_column="CognitoId", unique=True
    )  # Field name made lowercase.

    mfa_status = models.CharField(
        choices=MfaStatus, default=MfaStatus.OFF, max_length=50
    )
    mfa_recovery_state = models.CharField(
        choices=MfaRecoveryState, default=MfaRecoveryState.NORMAL, max_length=20
    )

    class Meta:
        managed = True
        db_table = "User"

    @property
    def mfa_off(self) -> bool:
        return self.mfa_status == User.MfaStatus.OFF

    @property
    def mfa_on(self) -> bool:
        return self.mfa_status == User.MfaStatus.ON

    @property
    def mfa_required(self) -> bool:
        return self.organization.mfa_required or (
            self.organization.mfa_enabled and self.mfa_on
        )

    def clean(self):
        super().clean()
        if hasattr(self, "organization") and self.organization:
            # Check if trying to turn OFF MFA when organization requires it
            if self.mfa_off and self.organization.mfa_required:
                raise ValidationError(
                    "Cannot disable MFA when organization requires it"
                )

            # Check if trying to turn ON MFA when organization has it disabled
            if self.mfa_on and self.organization.mfa_off:
                raise ValidationError(
                    "Cannot enable MFA when organization has it disabled"
                )

    def save(self, *args, **kwargs):
        self.full_clean()
        super().save(*args, **kwargs)

    @transaction.atomic
    def enable_totp_mfa(self):
        client = get_cognito_idp_client()
        try:
            client.admin_set_user_mfa_preference(
                UserPoolId=settings.COGNITO_USER_POOL_ID,
                SoftwareTokenMfaSettings={"Enabled": True, "PreferredMfa": True},
                Username=self.cognito_id,
            )
        except client.exceptions.InvalidParameterException as e:
            logger.warning(
                "Failed to set MFA preference in Cognito for user %s: %s. "
                "User may need to configure MFA device first.",
                self.cognito_id,
                str(e),
            )

        # Always set the local MFA status regardless of Cognito operation result
        self.mfa_status = User.MfaStatus.ON
        self.save()

    @transaction.atomic
    def disable_totp_mfa(self):
        client = get_cognito_idp_client()
        client.admin_set_user_mfa_preference(
            UserPoolId=settings.COGNITO_USER_POOL_ID,
            SoftwareTokenMfaSettings={"Enabled": False},
            Username=self.cognito_id,
        )
        self.mfa_status = User.MfaStatus.OFF
        self.save()

    @transaction.atomic
    def reset_totp_mfa(self):
        """
        Bring the user into a state that allows him to reconfigure his TOTP MFA device.
        For now, having the user's DB `mfa_status` set to `ON` but not having MFA
        activated in Cognito means he lacks a configured TOTP mfa.

        Use with caution!
        """
        client = get_cognito_idp_client()
        client.admin_set_user_mfa_preference(
            UserPoolId=settings.COGNITO_USER_POOL_ID,
            SoftwareTokenMfaSettings={"Enabled": False},
            Username=self.cognito_id,
        )
        self.mfa_status = User.MfaStatus.ON
        self.save()


class TasksRelationship(models.Model):
    a = models.ForeignKey(
        Task, on_delete=models.DO_NOTHING, db_column="A"
    )  # Field name made lowercase.
    b = models.ForeignKey(
        Task,
        on_delete=models.DO_NOTHING,
        db_column="B",
        related_name="tasksrelationship_b_set",
    )  # Field name made lowercase.

    class Meta:
        managed = True
        db_table = "_TasksRelationship"
        unique_together = (("a", "b"),)


class TasksToolsRelationship(models.Model):
    a = models.ForeignKey(
        Task, on_delete=models.DO_NOTHING, db_column="A"
    )  # Field name made lowercase.
    b = models.ForeignKey(
        Tool, on_delete=models.DO_NOTHING, db_column="B"
    )  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = "_TasksToolsRelationship"
        unique_together = (("a", "b"),)


class TemplateTasksPublicToolsRelationship(models.Model):
    a = models.ForeignKey(
        TemplateTask, on_delete=models.DO_NOTHING, db_column="A"
    )  # Field name made lowercase.
    b = models.ForeignKey(
        Tool, on_delete=models.DO_NOTHING, db_column="B"
    )  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = "_TemplateTasksPublicToolsRelationship"
        unique_together = (("a", "b"),)


class TemplateTasksRelationship(models.Model):
    a = models.ForeignKey(
        TemplateTask, on_delete=models.DO_NOTHING, db_column="A"
    )  # Field name made lowercase.
    b = models.ForeignKey(
        TemplateTask,
        on_delete=models.DO_NOTHING,
        db_column="B",
        related_name="templatetasksrelationship_b_set",
    )  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = "_TemplateTasksRelationship"
        unique_together = (("a", "b"),)


class TemplateTasksTemplateToolsRelationship(models.Model):
    a = models.ForeignKey(
        TemplateTask, on_delete=models.DO_NOTHING, db_column="A"
    )  # Field name made lowercase.
    b = models.ForeignKey(
        ToolTemplate, on_delete=models.DO_NOTHING, db_column="B"
    )  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = "_TemplateTasksTemplateToolsRelationship"
        unique_together = (("a", "b"),)


class ToolsRelationship(models.Model):
    a = models.ForeignKey(  # NOTE: This field indicates the parent of the relationship
        Tool, on_delete=models.DO_NOTHING, db_column="A"
    )  # Field name made lowercase.
    b = models.ForeignKey(  # NOTE: This field indicates the child of the relationship
        Tool,
        on_delete=models.DO_NOTHING,
        db_column="B",
        related_name="toolsrelationship_b_set",
    )  # Field name made lowercase.
    # pretend there's a PK so Django will stop complaining
    id = models.IntegerField(primary_key=True, editable=False)

    class Meta:
        managed = False
        db_table = "_ToolsRelationship"
        unique_together = (("a", "b"),)


class UnconfiguredTasksRelationship(models.Model):
    a = models.ForeignKey(
        UnconfiguredTask, on_delete=models.DO_NOTHING, db_column="A"
    )  # Field name made lowercase.
    b = models.ForeignKey(
        UnconfiguredTask,
        on_delete=models.DO_NOTHING,
        db_column="B",
        related_name="unconfiguredtasksrelationship_b_set",
    )  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = "_UnconfiguredTasksRelationship"
        unique_together = (("a", "b"),)


class UnconfiguredTasksTemplateToolsRelationship(models.Model):
    a = models.ForeignKey(
        ToolTemplate, on_delete=models.DO_NOTHING, db_column="A"
    )  # Field name made lowercase.
    b = models.ForeignKey(
        UnconfiguredTask, on_delete=models.DO_NOTHING, db_column="B"
    )  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = "_UnconfiguredTasksTemplateToolsRelationship"
        unique_together = (("a", "b"),)


class UnconfiguredTasksToolsRelationship(models.Model):
    a = models.ForeignKey(
        Tool, on_delete=models.DO_NOTHING, db_column="A"
    )  # Field name made lowercase.
    b = models.ForeignKey(
        UnconfiguredTask, on_delete=models.DO_NOTHING, db_column="B"
    )  # Field name made lowercase.

    class Meta:
        managed = False
        db_table = "_UnconfiguredTasksToolsRelationship"
        unique_together = (("a", "b"),)


class MfaRecoveryRequest(models.Model):
    class Status(models.TextChoices):
        PENDING = "PENDING"
        APPROVED = "APPROVED"
        DENIED = "DENIED"
        COMPLETED = "COMPLETED"
        EXPIRED = "EXPIRED"

    id = models.UUIDField(primary_key=True, default=uuid.uuid4)
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="mfa_recovery_requests"
    )
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE)
    status = models.CharField(choices=Status, default=Status.PENDING, max_length=20)

    # Request details
    request_reason = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()

    # Admin approval
    reviewed_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="reviewed_mfa_requests",
    )
    reviewed_at = models.DateTimeField(null=True, blank=True)
    admin_notes = models.TextField(blank=True)

    # Recovery completion
    recovery_token = models.UUIDField(unique=True, null=True, blank=True)
    token_expires_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ["-created_at"]

    def save(self, *args, **kwargs):
        if not self.expires_at:
            self.expires_at = timezone.now() + timedelta(hours=48)
        super().save(*args, **kwargs)

    def is_expired(self):
        return timezone.now() > self.expires_at

    def generate_recovery_token(self):
        self.recovery_token = uuid.uuid4()
        self.token_expires_at = timezone.now() + timedelta(hours=24)


# NOTE: This will probably not be needed, it's Prisma's migration tracking mechanism
#
# class PrismaMigrations(models.Model):
#     id = models.CharField(primary_key=True, max_length=36)
#     checksum = models.CharField(max_length=64)
#     finished_at = models.DateTimeField(blank=True, null=True)
#     migration_name = models.CharField(max_length=255)
#     logs = models.TextField(blank=True, null=True)
#     rolled_back_at = models.DateTimeField(blank=True, null=True)
#     started_at = models.DateTimeField()
#     applied_steps_count = models.IntegerField()
#
#     class Meta:
#         managed = False
#         db_table = "_prisma_migrations"


# TODO: Check that these models reflect PGVector specific datatypes


# class LangchainPgCollection(models.Model):
#     uuid = models.UUIDField(primary_key=True)
#     name = models.CharField(unique=True)
#     cmetadata = models.TextField(blank=True, null=True)  # This field type is a guess.
#
#     class Meta:
#         managed = False
#         db_table = "langchain_pg_collection"
#
#
# class LangchainPgEmbedding(models.Model):
#     id = models.CharField(primary_key=True)
#     collection = models.ForeignKey(
#         LangchainPgCollection, on_delete=models.DO_NOTHING, blank=True, null=True
#     )
#     embedding = models.TextField(blank=True, null=True)  # This field type is a guess.
#     document = models.CharField(blank=True, null=True)
#     cmetadata = models.JSONField(blank=True, null=True)
#
#     class Meta:
#         managed = False
#         db_table = "langchain_pg_embedding"
