import logging

from core.models import Organization, User
from django.db.models.signals import post_save
from django.dispatch import receiver

logger = logging.getLogger(__name__)


@receiver(post_save, sender=Organization)
def organization_post_save(sender, instance, created, **kwargs):
    """
    Handle post-save operations for Organization model.

    When organization MFA status changes to OFF or REQUIRED,
    update all users' MFA status accordingly.
    """
    match instance.mfa_status:
        case Organization.MfaStatus.OFF:
            # If organization MFA is OFF, disable MFA for all users who have it ON
            users_to_update = User.objects.filter(
                organization=instance, mfa_status=User.MfaStatus.ON
            )

            for user in users_to_update:
                user.disable_totp_mfa()

            if users_to_update.exists():
                logger.info(
                    "Disabled MFA for %s users due to organization policy change to OFF",
                    users_to_update.count(),
                )

        case Organization.MfaStatus.REQUIRED:
            # If organization MFA is REQUIRED, enable MFA for all users who have it OFF
            users_to_update = User.objects.filter(
                organization=instance, mfa_status=User.MfaStatus.OFF
            )

            for user in users_to_update:
                user.enable_totp_mfa()

            if users_to_update.exists():
                logger.info(
                    "Enabled MFA for %s users due to organization policy change to REQUIRED",
                    users_to_update.count(),
                )
