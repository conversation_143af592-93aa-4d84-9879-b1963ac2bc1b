# Generated by Django 5.2 on 2025-04-23 15:01

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0006_alter_task_options_alter_toolmeta_options"),
    ]

    operations = [
        migrations.AddField(
            model_name="toolmeta",
            name="organization",
            field=models.ForeignKey(
                db_column="OrganizationId",
                default=None,
                on_delete=django.db.models.deletion.DO_NOTHING,
                to="core.organization",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="toolmeta",
            name="tool",
            field=models.ForeignKey(
                db_column="ToolId",
                default=None,
                on_delete=django.db.models.deletion.DO_NOTHING,
                to="core.tool",
            ),
            preserve_default=False,
        ),
        migrations.AlterUniqueTogether(
            name="toolmeta",
            unique_together={("tool", "profile")},
        ),
    ]
