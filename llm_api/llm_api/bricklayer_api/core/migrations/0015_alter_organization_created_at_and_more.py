# Generated by Django 5.2 on 2025-06-13 08:23

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0014_user_mfa_status"),
    ]

    operations = [
        migrations.AlterField(
            model_name="organization",
            name="created_at",
            field=models.DateTimeField(auto_now_add=True, db_column="CreatedAt"),
        ),
        migrations.AlterField(
            model_name="organization",
            name="current_usd_usage",
            field=models.FloatField(db_column="CurrentUsdUsage", default=0),
        ),
        migrations.AlterField(
            model_name="organization",
            name="inactivity_timeout",
            field=models.IntegerField(db_column="InactivityTimeout", default=60),
        ),
        migrations.AlterField(
            model_name="organization",
            name="industry",
            field=models.TextField(blank=True, db_column="Industry"),
        ),
        migrations.AlterField(
            model_name="organization",
            name="license",
            field=models.TextField(blank=True, db_column="License"),
        ),
        migrations.Alter<PERSON>ield(
            model_name="organization",
            name="max_usd_usage",
            field=models.FloatField(db_column="MaxUsdUsage", default=100),
        ),
        migrations.AlterField(
            model_name="organization",
            name="updated_at",
            field=models.DateTimeField(auto_now=True, db_column="UpdatedAt"),
        ),
        migrations.AlterField(
            model_name="user",
            name="created_at",
            field=models.DateTimeField(auto_now_add=True, db_column="CreatedAt"),
        ),
        migrations.AlterField(
            model_name="user",
            name="organization_position",
            field=models.TextField(blank=True, db_column="OrganizationPosition"),
        ),
        migrations.AlterField(
            model_name="user",
            name="role",
            field=models.TextField(blank=True, db_column="Role"),
        ),
        migrations.AlterField(
            model_name="user",
            name="updated_at",
            field=models.DateTimeField(auto_now=True, db_column="UpdatedAt"),
        ),
        migrations.AlterField(
            model_name="user",
            name="usd_usage",
            field=models.FloatField(db_column="USDUsage", default=0),
        ),
    ]
