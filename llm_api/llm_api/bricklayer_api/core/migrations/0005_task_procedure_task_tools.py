# Generated by Django 5.2 on 2025-04-23 11:21

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0004_alter_task_options"),
    ]

    operations = [
        migrations.AddField(
            model_name="task",
            name="procedure",
            field=models.ForeignKey(
                db_column="ProcedureId",
                default=None,
                on_delete=django.db.models.deletion.DO_NOTHING,
                to="core.procedure",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="task",
            name="tools",
            field=models.ManyToManyField(
                through="core.TasksToolsRelationship", to="core.tool"
            ),
        ),
    ]
