# Generated by Django 5.2 on 2025-04-10 22:03

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="AdvertisingProcedure",
            fields=[
                (
                    "id",
                    models.AutoField(db_column="Id", primary_key=True, serialize=False),
                ),
                ("name", models.TextField(db_column="Name")),
                ("ui_description", models.TextField(db_column="UIDescription")),
                (
                    "interested_list",
                    models.TextField(blank=True, db_column="InterestedList", null=True),
                ),
                ("created_at", models.DateTimeField(db_column="CreatedAt")),
            ],
            options={
                "db_table": "AdvertisingProcedure",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="ApiClient",
            fields=[
                (
                    "id",
                    models.TextField(db_column="Id", primary_key=True, serialize=False),
                ),
                ("api_key", models.TextField(db_column="ApiKey")),
                (
                    "description",
                    models.TextField(blank=True, db_column="Description", null=True),
                ),
                ("name", models.TextField(blank=True, db_column="Name", null=True)),
                ("is_enabled", models.BooleanField(db_column="IsEnabled")),
                ("usd_usage", models.FloatField(db_column="USDUsage")),
                ("created_at", models.DateTimeField(db_column="CreatedAt")),
                ("updated_at", models.DateTimeField(db_column="UpdatedAt")),
            ],
            options={
                "db_table": "ApiClient",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="BaseTemplate",
            fields=[
                (
                    "id",
                    models.AutoField(db_column="Id", primary_key=True, serialize=False),
                ),
                ("spec", models.TextField(db_column="Spec")),
                (
                    "configurable_spec_fields",
                    models.TextField(
                        blank=True, db_column="ConfigurableSpecFields", null=True
                    ),
                ),
                (
                    "img_url",
                    models.TextField(blank=True, db_column="ImgUrl", null=True),
                ),
                ("created_at", models.DateTimeField(db_column="CreatedAt")),
                ("updated_at", models.DateTimeField(db_column="UpdatedAt")),
                (
                    "display_description",
                    models.TextField(db_column="DisplayDescription"),
                ),
                ("display_name", models.TextField(db_column="DisplayName")),
            ],
            options={
                "db_table": "BaseTemplate",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="CategoryTemplate",
            fields=[
                (
                    "id",
                    models.AutoField(db_column="Id", primary_key=True, serialize=False),
                ),
                ("spec", models.TextField(db_column="Spec")),
                (
                    "configurable_spec_fields",
                    models.TextField(
                        blank=True, db_column="ConfigurableSpecFields", null=True
                    ),
                ),
                ("type", models.TextField(db_column="Type")),
                (
                    "llm_type",
                    models.TextField(blank=True, db_column="LLMType", null=True),
                ),
                (
                    "img_url",
                    models.TextField(blank=True, db_column="ImgUrl", null=True),
                ),
                ("created_at", models.DateTimeField(db_column="CreatedAt")),
                ("updated_at", models.DateTimeField(db_column="UpdatedAt")),
                (
                    "display_description",
                    models.TextField(db_column="DisplayDescription"),
                ),
                ("display_name", models.TextField(db_column="DisplayName")),
            ],
            options={
                "db_table": "CategoryTemplate",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="Filter",
            fields=[
                (
                    "pk",
                    models.CompositePrimaryKey(
                        "conversation_id",
                        "organization_id",
                        "tool_id",
                        "parent_tool_id",
                        blank=True,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("conversation_id", models.TextField(db_column="ConversationId")),
                ("organization_id", models.TextField(db_column="OrganizationId")),
                ("tool_id", models.TextField(db_column="ToolId")),
                ("parent_tool_id", models.TextField(db_column="ParentToolId")),
            ],
            options={
                "db_table": "Filter",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="ManagedDatastoreDocsSnapshot",
            fields=[
                (
                    "id",
                    models.AutoField(db_column="Id", primary_key=True, serialize=False),
                ),
                (
                    "provided_document_id",
                    models.TextField(db_column="ProvidedDocumentId"),
                ),
                ("provider", models.TextField(db_column="Provider")),
                ("datastores_mapping", models.JSONField(db_column="DatastoresMapping")),
            ],
            options={
                "db_table": "ManagedDatastoreDocsSnapshot",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="OrgAdminInvitation",
            fields=[
                (
                    "id",
                    models.AutoField(db_column="Id", primary_key=True, serialize=False),
                ),
                ("email", models.TextField(db_column="Email")),
                ("invite_code", models.TextField(db_column="InviteCode")),
                ("created_at", models.DateTimeField(db_column="CreatedAt")),
            ],
            options={
                "db_table": "OrgAdminInvitation",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="Organization",
            fields=[
                (
                    "id",
                    models.TextField(db_column="Id", primary_key=True, serialize=False),
                ),
                ("max_usd_usage", models.FloatField(db_column="MaxUsdUsage")),
                ("current_usd_usage", models.FloatField(db_column="CurrentUsdUsage")),
                ("business_name", models.TextField(db_column="BusinessName")),
                ("industry", models.TextField(db_column="Industry")),
                ("license", models.TextField(db_column="License")),
                ("created_at", models.DateTimeField(db_column="CreatedAt")),
                ("updated_at", models.DateTimeField(db_column="UpdatedAt")),
                (
                    "inactivity_timeout",
                    models.IntegerField(db_column="InactivityTimeout"),
                ),
            ],
            options={
                "db_table": "Organization",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="OrgMembersInvitation",
            fields=[
                (
                    "id",
                    models.AutoField(db_column="Id", primary_key=True, serialize=False),
                ),
                ("email", models.TextField(db_column="Email")),
                ("invite_code", models.TextField(db_column="InviteCode")),
                ("expiration_time", models.DateTimeField(db_column="ExpirationTime")),
                ("created_at", models.DateTimeField(db_column="CreatedAt")),
            ],
            options={
                "db_table": "OrgMembersInvitation",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="Procedure",
            fields=[
                (
                    "id",
                    models.AutoField(db_column="Id", primary_key=True, serialize=False),
                ),
                ("name", models.TextField(db_column="Name")),
                ("ui_description", models.TextField(db_column="UIDescription")),
                ("llm_description", models.TextField(db_column="LLMDescription")),
                ("inputs", models.JSONField(db_column="Inputs")),
                ("long_term_memory", models.BooleanField(db_column="LongTermMemory")),
                ("is_enabled", models.BooleanField(db_column="IsEnabled")),
                ("deleted", models.BooleanField(db_column="Deleted")),
                ("created_at", models.DateTimeField(db_column="CreatedAt")),
                ("updated_at", models.DateTimeField(db_column="UpdatedAt")),
            ],
            options={
                "db_table": "Procedure",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="ProcedureRecurringRunEvents",
            fields=[
                (
                    "id",
                    models.TextField(db_column="Id", primary_key=True, serialize=False),
                ),
                ("cron_setup", models.TextField(db_column="CronSetup")),
                ("inputs", models.JSONField(db_column="Inputs")),
                ("created_at", models.DateTimeField(db_column="CreatedAt")),
                ("updated_at", models.DateTimeField(db_column="UpdatedAt")),
                ("is_enabled", models.BooleanField(db_column="IsEnabled")),
            ],
            options={
                "db_table": "ProcedureRecurringRunEvents",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="ProcedureRun",
            fields=[
                (
                    "id",
                    models.TextField(db_column="Id", primary_key=True, serialize=False),
                ),
                ("ui_status", models.TextField(db_column="UIStatus")),
                ("llm_status", models.TextField(db_column="LLMStatus")),
                ("inputs", models.JSONField(db_column="Inputs")),
                ("created_at", models.DateTimeField(db_column="CreatedAt")),
                (
                    "outcome_location",
                    models.TextField(
                        blank=True, db_column="OutcomeLocation", null=True
                    ),
                ),
                (
                    "outcome",
                    models.TextField(blank=True, db_column="Outcome", null=True),
                ),
                (
                    "executive_summary",
                    models.TextField(
                        blank=True, db_column="ExecutiveSummary", null=True
                    ),
                ),
                (
                    "failed_reason",
                    models.TextField(blank=True, db_column="FailedReason", null=True),
                ),
                (
                    "key_findings",
                    models.TextField(blank=True, db_column="KeyFindings", null=True),
                ),
                (
                    "summarization_cost",
                    models.FloatField(db_column="SummarizationCost"),
                ),
                ("trigger", models.TextField(db_column="Trigger")),
                ("updated_at", models.DateTimeField(db_column="UpdatedAt")),
                ("custom_name", models.TextField(db_column="CustomName")),
                (
                    "completed_at",
                    models.DateTimeField(
                        blank=True, db_column="CompletedAt", null=True
                    ),
                ),
                (
                    "actions_taken",
                    models.TextField(blank=True, db_column="ActionsTaken", null=True),
                ),
                ("deleted", models.BooleanField(db_column="Deleted")),
                (
                    "tool_meta_profile",
                    models.TextField(
                        blank=True, db_column="ToolMetaProfile", null=True
                    ),
                ),
            ],
            options={
                "db_table": "ProcedureRun",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="ProcedureRunRating",
            fields=[
                (
                    "id",
                    models.AutoField(db_column="Id", primary_key=True, serialize=False),
                ),
                ("like", models.BooleanField(db_column="Like")),
                (
                    "comment",
                    models.TextField(blank=True, db_column="Comment", null=True),
                ),
                ("created_at", models.DateTimeField(db_column="CreatedAt")),
            ],
            options={
                "db_table": "ProcedureRunRating",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="PublicData",
            fields=[
                (
                    "id",
                    models.AutoField(db_column="Id", primary_key=True, serialize=False),
                ),
                ("title", models.TextField(db_column="Title")),
                ("url", models.TextField(db_column="URL")),
                (
                    "content",
                    models.TextField(blank=True, db_column="Content", null=True),
                ),
                (
                    "published_at",
                    models.DateTimeField(
                        blank=True, db_column="PublishedAt", null=True
                    ),
                ),
                (
                    "metadata",
                    models.JSONField(blank=True, db_column="Metadata", null=True),
                ),
                ("created_at", models.DateTimeField(db_column="CreatedAt")),
                ("updated_at", models.DateTimeField(db_column="UpdatedAt")),
            ],
            options={
                "db_table": "PublicData",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="PublicDataSourceFeed",
            fields=[
                (
                    "id",
                    models.AutoField(db_column="Id", primary_key=True, serialize=False),
                ),
                ("name", models.TextField(db_column="Name")),
                ("url", models.TextField(db_column="URL")),
                ("metadata", models.JSONField(db_column="Metadata")),
                ("created_at", models.DateTimeField(db_column="CreatedAt")),
                (
                    "last_checked_at",
                    models.DateTimeField(
                        blank=True, db_column="LastCheckedAt", null=True
                    ),
                ),
                (
                    "last_item_pub_date",
                    models.DateTimeField(
                        blank=True, db_column="LastItemPubDate", null=True
                    ),
                ),
                ("updated_at", models.DateTimeField(db_column="UpdatedAt")),
                ("deleted", models.BooleanField(db_column="Deleted")),
            ],
            options={
                "db_table": "PublicDataSourceFeed",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="PublicDataSourceOptions",
            fields=[
                (
                    "id",
                    models.AutoField(db_column="Id", primary_key=True, serialize=False),
                ),
                ("disabled_sources", models.JSONField(db_column="DisabledSources")),
            ],
            options={
                "db_table": "PublicDataSourceOptions",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="SavedTableView",
            fields=[
                (
                    "id",
                    models.AutoField(db_column="Id", primary_key=True, serialize=False),
                ),
                ("name", models.TextField(db_column="Name")),
                ("query_params", models.TextField(db_column="QueryParams")),
                (
                    "column_settings",
                    models.TextField(blank=True, db_column="ColumnSettings", null=True),
                ),
                ("created_at", models.DateTimeField(db_column="CreatedAt")),
                ("updated_at", models.DateTimeField(db_column="UpdatedAt")),
            ],
            options={
                "db_table": "SavedTableView",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="Task",
            fields=[
                (
                    "id",
                    models.AutoField(db_column="Id", primary_key=True, serialize=False),
                ),
                ("prompt", models.TextField(db_column="Prompt")),
                (
                    "inputs_names",
                    models.TextField(blank=True, db_column="InputsNames", null=True),
                ),
                ("deleted", models.BooleanField(db_column="Deleted")),
                ("ui_order", models.IntegerField(db_column="UIOrder")),
                ("created_at", models.DateTimeField(db_column="CreatedAt")),
                ("updated_at", models.DateTimeField(db_column="UpdatedAt")),
                ("description", models.TextField(db_column="Description")),
            ],
            options={
                "db_table": "Task",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="TaskRun",
            fields=[
                (
                    "id",
                    models.TextField(db_column="Id", primary_key=True, serialize=False),
                ),
                ("status", models.TextField(db_column="Status")),
                (
                    "outcome_location",
                    models.TextField(
                        blank=True, db_column="OutcomeLocation", null=True
                    ),
                ),
                ("created_at", models.DateTimeField(db_column="CreatedAt")),
                (
                    "outcome",
                    models.TextField(blank=True, db_column="Outcome", null=True),
                ),
                (
                    "sources",
                    models.JSONField(blank=True, db_column="Sources", null=True),
                ),
                (
                    "failed_reason",
                    models.TextField(blank=True, db_column="FailedReason", null=True),
                ),
                ("llm_cost", models.FloatField(db_column="LLMCost")),
                ("updated_at", models.DateTimeField(db_column="UpdatedAt")),
                ("deleted", models.BooleanField(db_column="Deleted")),
                ("description", models.TextField(db_column="Description")),
                (
                    "prompt_used",
                    models.TextField(blank=True, db_column="PromptUsed", null=True),
                ),
                (
                    "tools_used",
                    models.JSONField(blank=True, db_column="ToolsUsed", null=True),
                ),
            ],
            options={
                "db_table": "TaskRun",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="TasksRelationship",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
            ],
            options={
                "db_table": "_TasksRelationship",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="TasksToolsRelationship",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
            ],
            options={
                "db_table": "_TasksToolsRelationship",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="TemplateProcedure",
            fields=[
                (
                    "id",
                    models.AutoField(db_column="Id", primary_key=True, serialize=False),
                ),
                ("name", models.TextField(db_column="Name")),
                ("created_by", models.TextField(db_column="CreatedBy")),
                ("ui_description", models.TextField(db_column="UIDescription")),
                ("llm_description", models.TextField(db_column="LLMDescription")),
                ("inputs", models.JSONField(db_column="Inputs")),
                ("tags", models.JSONField(db_column="Tags")),
                ("created_at", models.DateTimeField(db_column="CreatedAt")),
            ],
            options={
                "db_table": "TemplateProcedure",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="TemplateTask",
            fields=[
                (
                    "id",
                    models.AutoField(db_column="Id", primary_key=True, serialize=False),
                ),
                ("prompt", models.TextField(db_column="Prompt")),
                (
                    "inputs_names",
                    models.TextField(blank=True, db_column="InputsNames", null=True),
                ),
                ("ui_order", models.IntegerField(db_column="UIOrder")),
                ("created_at", models.DateTimeField(db_column="CreatedAt")),
                ("description", models.TextField(db_column="Description")),
            ],
            options={
                "db_table": "TemplateTask",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="TemplateTasksPublicToolsRelationship",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
            ],
            options={
                "db_table": "_TemplateTasksPublicToolsRelationship",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="TemplateTasksRelationship",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
            ],
            options={
                "db_table": "_TemplateTasksRelationship",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="TemplateTasksTemplateToolsRelationship",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
            ],
            options={
                "db_table": "_TemplateTasksTemplateToolsRelationship",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="Tool",
            fields=[
                (
                    "id",
                    models.TextField(db_column="Id", primary_key=True, serialize=False),
                ),
                ("display_name", models.TextField(db_column="DisplayName")),
                (
                    "display_description",
                    models.TextField(db_column="DisplayDescription"),
                ),
                ("type", models.TextField(db_column="Type")),
                ("configured_by", models.CharField(db_column="ConfiguredBy")),
                ("spec", models.TextField(db_column="Spec")),
                ("created_at", models.DateTimeField(db_column="CreatedAt")),
                ("updated_at", models.DateTimeField(db_column="UpdatedAt")),
                (
                    "configurable_spec_fields",
                    models.JSONField(
                        blank=True, db_column="ConfigurableSpecFields", null=True
                    ),
                ),
                (
                    "llm_type",
                    models.TextField(blank=True, db_column="LLMType", null=True),
                ),
                (
                    "img_url",
                    models.TextField(blank=True, db_column="ImgUrl", null=True),
                ),
                (
                    "disabled_in_organizations",
                    models.TextField(
                        blank=True, db_column="DisabledInOrganizations", null=True
                    ),
                ),
            ],
            options={
                "db_table": "Tool",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="Toolmeta",
            fields=[
                (
                    "id",
                    models.AutoField(db_column="Id", primary_key=True, serialize=False),
                ),
                ("type", models.TextField(db_column="Type")),
                ("data", models.JSONField(db_column="Data")),
                ("profile", models.TextField(db_column="Profile")),
            ],
            options={
                "db_table": "ToolMeta",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="ToolsRelationship",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
            ],
            options={
                "db_table": "_ToolsRelationship",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="ToolTemplate",
            fields=[
                (
                    "id",
                    models.AutoField(db_column="Id", primary_key=True, serialize=False),
                ),
                ("spec", models.TextField(db_column="Spec")),
                (
                    "configurable_spec_fields",
                    models.TextField(
                        blank=True, db_column="ConfigurableSpecFields", null=True
                    ),
                ),
                ("type", models.TextField(db_column="Type")),
                (
                    "llm_type",
                    models.TextField(blank=True, db_column="LLMType", null=True),
                ),
                (
                    "img_url",
                    models.TextField(blank=True, db_column="ImgUrl", null=True),
                ),
                ("configured_by", models.CharField(db_column="ConfiguredBy")),
                ("created_at", models.DateTimeField(db_column="CreatedAt")),
                ("updated_at", models.DateTimeField(db_column="UpdatedAt")),
                (
                    "display_description",
                    models.TextField(db_column="DisplayDescription"),
                ),
                ("display_name", models.TextField(db_column="DisplayName")),
                (
                    "category_template",
                    models.IntegerField(
                        blank=True, db_column="CategoryTemplateId", null=True
                    ),
                ),
            ],
            options={
                "db_table": "ToolTemplate",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="UnconfiguredProcedure",
            fields=[
                (
                    "id",
                    models.AutoField(db_column="Id", primary_key=True, serialize=False),
                ),
                ("name", models.TextField(db_column="Name")),
                ("ui_description", models.TextField(db_column="UIDescription")),
                ("llm_description", models.TextField(db_column="LLMDescription")),
                ("inputs", models.JSONField(db_column="Inputs")),
                ("created_at", models.DateTimeField(db_column="CreatedAt")),
                ("created_by", models.TextField(db_column="CreatedBy")),
                (
                    "tool_templates_ids",
                    models.TextField(
                        blank=True, db_column="ToolTemplatesIds", null=True
                    ),
                ),
            ],
            options={
                "db_table": "UnconfiguredProcedure",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="UnconfiguredTask",
            fields=[
                (
                    "id",
                    models.AutoField(db_column="Id", primary_key=True, serialize=False),
                ),
                ("prompt", models.TextField(db_column="Prompt")),
                (
                    "inputs_names",
                    models.TextField(blank=True, db_column="InputsNames", null=True),
                ),
                ("ui_order", models.IntegerField(db_column="UIOrder")),
                ("created_at", models.DateTimeField(db_column="CreatedAt")),
                ("description", models.TextField(db_column="Description")),
            ],
            options={
                "db_table": "UnconfiguredTask",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="UnconfiguredTasksRelationship",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
            ],
            options={
                "db_table": "_UnconfiguredTasksRelationship",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="UnconfiguredTasksTemplateToolsRelationship",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
            ],
            options={
                "db_table": "_UnconfiguredTasksTemplateToolsRelationship",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="UnconfiguredTasksToolsRelationship",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
            ],
            options={
                "db_table": "_UnconfiguredTasksToolsRelationship",
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="User",
            fields=[
                (
                    "id",
                    models.TextField(db_column="Id", primary_key=True, serialize=False),
                ),
                ("first_name", models.TextField(db_column="FirstName")),
                ("last_name", models.TextField(db_column="LastName")),
                ("email", models.TextField(db_column="Email")),
                ("role", models.TextField(db_column="Role")),
                (
                    "organization_position",
                    models.TextField(db_column="OrganizationPosition"),
                ),
                ("is_org_admin", models.BooleanField(db_column="IsOrgAdmin")),
                ("usd_usage", models.FloatField(db_column="USDUsage")),
                ("created_at", models.DateTimeField(db_column="CreatedAt")),
                ("updated_at", models.DateTimeField(db_column="UpdatedAt")),
                ("cognito_id", models.TextField(db_column="CognitoId", unique=True)),
            ],
            options={
                "db_table": "User",
                "managed": False,
            },
        ),
    ]
