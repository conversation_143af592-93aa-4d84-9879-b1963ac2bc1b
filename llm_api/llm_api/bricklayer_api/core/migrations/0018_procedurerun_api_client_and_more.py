# Generated by Django 5.2 on 2025-06-23 14:04

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0017_alter_procedurerun_options'),
    ]

    operations = [
        migrations.AddField(
            model_name='procedurerun',
            name='api_client',
            field=models.ForeignKey(blank=True, db_column='ApiClientId', null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='core.apiclient'),
        ),
        migrations.AddField(
            model_name='procedurerun',
            name='procedure',
            field=models.ForeignKey(db_column='ProcedureId', default=django.utils.timezone.now, on_delete=django.db.models.deletion.DO_NOTHING, to='core.procedure'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='procedurerun',
            name='user',
            field=models.ForeignKey(blank=True, db_column='UserId', null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='core.user'),
        ),
    ]
