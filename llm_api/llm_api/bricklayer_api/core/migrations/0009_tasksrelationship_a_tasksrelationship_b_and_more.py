# Generated by Django 5.2 on 2025-04-29 12:28

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0008_alter_tasksrelationship_options_alter_task_managers_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="tasksrelationship",
            name="a",
            field=models.ForeignKey(
                db_column="A",
                default=None,
                on_delete=django.db.models.deletion.DO_NOTHING,
                to="core.task",
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="tasksrelationship",
            name="b",
            field=models.ForeignKey(
                db_column="B",
                default=None,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="tasksrelationship_b_set",
                to="core.task",
            ),
            preserve_default=False,
        ),
        migrations.AlterUniqueTogether(
            name="tasksrelationship",
            unique_together={("a", "b")},
        ),
    ]
