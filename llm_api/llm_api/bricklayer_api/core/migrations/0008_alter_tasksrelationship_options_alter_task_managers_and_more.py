# Generated by Django 5.2 on 2025-04-29 12:26

import django.db.models.manager
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("core", "0007_toolmeta_organization_toolmeta_tool_and_more"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="tasksrelationship",
            options={"managed": True},
        ),
        migrations.AlterModelManagers(
            name="task",
            managers=[
                ("active_objects", django.db.models.manager.Manager()),
            ],
        ),
        migrations.AlterField(
            model_name="task",
            name="deleted",
            field=models.BooleanField(db_column="Deleted", default=False),
        ),
    ]
