# Django Bricklayer API

This project is an ongoing migration from Node.js and FastAPI to Django with Django REST Framework. Vital know-how for running the project and gotchas must be documented here.

## Database Migrations

As we're converting unmanaged Django models into managed ones, certain DB migrations will need to be executed with `--fake` because they're trying to create the DB table that already exists.

There's now a custom management command inside `core/management/commands/migration_fakes.py`. It will take care of running the list of fake migrations from the `core` app, as well as the actual migrations in-between, if they have not been applied yet.

### The list of fake migrations must be kept up to date in the management command!

```python
FAKE_MIGRATIONS: list[str] = ["0005", "0007"]  # and so on ...
```

Running the command will be part of the k8s deployment, but can be run locally as

```
# Run the `core` fake migrations and migrations in-between
python manage.py migrate_fakes

# Run anything else remaining
python manage.py migrate
```
