prompts_and_inputs = [
    {
        "name": "summarise_alert",
        "prompt": "Explain the alert: \n\n {alert} \n\n Summarize the explanation in a summary. You MUST FOCUS on including ALL technical details about the alert. ALWAYS evaluate the risk as well as current status and include information on ANY RULES, ATTACK TACTICS or TECHNIQUES and associated IDs. BE SURE to include suggestions for next steps and remediations.",
        "inputs": ["alert"],
    },
    {
        "name": "identify_ioc",
        "prompt": "Identify the indicators of compromise in the alert:\n\n{alert}\n\nSpecifically identify and flag the IP Addresses, Domains and File Hashes mentioned. This section MUST be a LIST of values ONLY. No description. DO NOT EXPLAIN YOURSELF IN THIS SECTION",
        "inputs": ["alert"],
    },
    {
        "name": "analyse_ioc",
        "prompt": "Analyze the indicators of compromise listed in the context using the Alien Labs Open Threat Exchange platform one by one. Request threat intelligence on each indicator of compromise from the threat analyst one after the other. Summarize the findings. Clearly mention that the information was retrieved using Alienvault.",
        "inputs": [],
    },
    {
        "name": "analyse_ioc_2",
        "prompt": "Analyze the indicators of compromise listed in the context using the Virustotal tool one by one. Request threat intelligence on each indicator of compromise from the threat analyst one after the other. Summarize the findings. Clearly mention that information was retrieved using VirusTotal.",
        "inputs": [],
    },
    {
        "name": "analyse_ioc_3",
        "prompt": "Request threat intelligence on the ip_addresses listed in the context from the threat intel analyst using the AbuseIPDB tool. Summarize the findings. Be sure to check that the input is an IP address before using the tool. DO NOT use the tool otherwise. Clearly mention that the information was retrieved from AbuseIPDB if it was used.",
        "inputs": [],
    },
    {
        "name": "analyse_technique",
        "prompt": "Explain the alert: \n\n {alert} \n\n Summarize the explanation in an executive summary and include suggestions for next steps. Make sure to specifically flag the relevant indicators of compromise and evaluate the risk as well as current status.",
        "inputs": ["alert"],
    },
    {
        "name": "create_alert_report",
        "prompt": """
            Generate an Alert analysis report in the following TEMPLATE FORMAT using the given information about a cybersecurity alert. You MUST follow the TEMPLATE and fill its sections according to their section description. DO NOT INCLUDE ANY ADDITIONAL DETAILS EXCEPT THE REPORT ITSELF. DO NOT INCLUDE TEMPLATE FORMAT START AND END.

************TEMPLATE FORMAT START***********

----- EXECUTIVE SUMMARY & ANALYSIS -----

This section should contain:
 Executive Summary: This section should focus on the decision the report is supporting and the highest level detail an executive would care about. It should focus on the single largest takeaway from the alert analysis and how it fits into the larger risk landscape. This section should not summarize the underlying reports used to create the analysis. It should be able to convey the most important analysis to the reader, so that they can skip the rest of the report and still be able to take an informed action.
 Analysis Details: This section should focus on the technical details of the analysis. This should be able to convey all the technical details a SOC Analyst might require to further investigate or verify the claim made in the alert analysis report.

----- SUPPORT -----

These bullets should summarize all the supporting analysis performed using external tools: 
Which external tools were used to further analyze the alert and its indicators of compromise? 
What did they report back individually? 
 What was the conclusion the external tools aided in reaching? 
Why is this conclusion important?
This section should not summarise the alert or the details obtained from it but rather convey the details gather from external analysis using various tools when further analysing its indicators of compromise.

----- ASSESSMENT -----

Conclusion or Final Assessment associated with the campaign. It indicates whether the alert is a true positive or a false positive or whether it requires further investigation.  This section MUST be a single sentence at maximum. No description. DO NOT EXPLAIN YOURSELF IN THIS SECTION

----- METHODOLOGY -----

This section should list out the types of TTPs leveraged. This does not need to be an exhaustive list of tool names but rather a description of its strategy. This section MUST include the Rule attack strategy, the Rule attack technique and the Rule attacck ID.

----- CYBER KILL-CHAIN -----

This section is the cyber kill-cahain phase associated with the alert. It states exactly which phase out of 8 we have recieved the alert at. Values could be 'Reconnaissance', 'Intrusion', 'Exploitation', 'Privilege Escalation', 'Lateral Movement', 'Obfuscation / Anti-forensics', 'Denial of Service' or 'Exfiltration'. This section MUST be a single value ONLY. No description. DO NOT EXPLAIN YOURSELF IN THIS SECTION

----- VICTIMS -----

This section should list the users or systems targetted by the incident along with all their identification details. It focuses on the victim component of the diamond model.

----- CAPABILITY -----

This section should focus on what we know of cabability of the bad actor in the alert incident. It focuses on the capability component of the diamond model.

----- INFRASTRUCTURE -----

This section should list the types of infrastructure leveraged in the incident for command and control, initial intrusion, and exfiltration from network and systems. It focuses on the infrastructure component of the diamond model.



**********TEMPLATE FORMAT ENDS************

            """,
        "inputs": [],
    },
    {
        "name": "output_processing",
        "prompt": "Create 2 sections based on the following context. The SECTIONS are: \n\nEXECUTIVE SUMMARY\nKEY FINDINGS",
        "inputs": [],
    },
]

tasks = {
    # In dev, we use ids from Abhishek's org
    "dev": {
        "f8ee5981-2f0a-4267-9502-c720ed39f117": {
            # summarise_alert
            "summarise_alert": {
                "specialist": "9f671a40-9f2e-4212-8859-edfba4781d23",
                "tools": [],
                **prompts_and_inputs[0],
            },
            # identify_ioc
            "identify_ioc": {
                "specialist": "9f671a40-9f2e-4212-8859-edfba4781d23",
                "tools": [],
                **prompts_and_inputs[1],
            },
            # analyse_ioc
            "analyse_ioc": {
                "specialist": "647eca62-2b3f-4802-802e-23c026c2f3db",
                "tools": ["e9ea59a6-c3e9-4ef3-b3d0-de7bdd88f90b"],
                **prompts_and_inputs[2],
            },
            # analyse_ioc_2
            "analyse_ioc_2": {
                "specialist": "647eca62-2b3f-4802-802e-23c026c2f3db",
                "tools": ["df7b7ab6-1ada-4434-9905-ef48d33fd607"],
                **prompts_and_inputs[3],
            },
            # analyse_ioc_3
            "analyse_ioc_3": {
                "specialist": "647eca62-2b3f-4802-802e-23c026c2f3db",
                "tools": ["78d3dda2-063a-4399-bc28-49fba1664fd8"],
                **prompts_and_inputs[4],
            },
            # analyse_technique
            "analyse_technique": {
                "specialist": "647eca62-2b3f-4802-802e-23c026c2f3db",
                "tools": ["bf4065f8-3445-445e-b0b9-70d3615d0a6b"],
                **prompts_and_inputs[5],
            },
            # create_alert_report
            "create_alert_report": {
                "specialist": "9f671a40-9f2e-4212-8859-edfba4781d23",
                "tools": [],
                **prompts_and_inputs[6],
            },
            # output_processing
            "output_processing": {
                "specialist": "",
                "tools": [],
                **prompts_and_inputs[7],
            },
        }
    },
    # In production, we use ids from the CDI org
    "prod": {
        "065679f3-53a8-4c0a-b089-9e6fa105860f": {
            # summarise_alert
            "summarise_alert": {
                "specialist": "a4b1d982-ef69-4a44-81a2-2313ca5bd5b2",
                "tools": [],
                **prompts_and_inputs[0],
            },
            # identify_ioc
            "identify_ioc": {
                "specialist": "a4b1d982-ef69-4a44-81a2-2313ca5bd5b2",
                "tools": [],
                **prompts_and_inputs[1],
            },
            # analyse_ioc
            "analyse_ioc": {
                "specialist": "de59be82-a662-4b90-a00a-c27dfba3126a",
                "tools": ["2d1303af-6ce4-4292-a96b-d58ce4e60be4"],
                **prompts_and_inputs[2],
            },
            # analyse_ioc_2
            "analyse_ioc_2": {
                "specialist": "de59be82-a662-4b90-a00a-c27dfba3126a",
                "tools": ["560aeb76-ff5b-445e-94b4-e4e5b9677fc7"],
                **prompts_and_inputs[3],
            },
            # analyse_ioc_3
            "analyse_ioc_3": {
                "specialist": "de59be82-a662-4b90-a00a-c27dfba3126a",
                "tools": ["4aee3ba6-4e8e-4467-aa4f-73567bafc3a6"],
                **prompts_and_inputs[4],
            },
            # analyse_technique
            "analyse_technique": {
                "specialist": "de59be82-a662-4b90-a00a-c27dfba3126a",
                "tools": ["bf4065f8-3445-445e-b0b9-70d3615d0a6b"],
                **prompts_and_inputs[5],
            },
            # create_alert_report
            "create_alert_report": {
                "specialist": "a4b1d982-ef69-4a44-81a2-2313ca5bd5b2",
                "tools": [],
                **prompts_and_inputs[6],
            },
            # output_processing
            "output_processing": {
                "specialist": "",
                "tools": [],
                **prompts_and_inputs[7],
            },
        },
        "4ee89b1f-e99a-4387-8cae-309ee19954f8": {
            # summarise_alert
            "summarise_alert": {
                "specialist": "97887aae-7c10-4f94-9fba-ae90246f4836",
                "tools": [],
                **prompts_and_inputs[0],
            },
            # identify_ioc
            "identify_ioc": {
                "specialist": "97887aae-7c10-4f94-9fba-ae90246f4836",
                "tools": [],
                **prompts_and_inputs[1],
            },
            # analyse_ioc
            "analyse_ioc": {
                "specialist": "4e63a412-dfe4-4f99-823a-18f03aeed4c7",
                "tools": ["e5cfff34-ca97-4b97-8864-8dd32a327345"],
                **prompts_and_inputs[2],
            },
            # analyse_ioc_2
            "analyse_ioc_2": {
                "specialist": "4e63a412-dfe4-4f99-823a-18f03aeed4c7",
                "tools": ["1a8d0748-9ce0-4532-b88c-89e6f9e6f9dd"],
                **prompts_and_inputs[3],
            },
            # analyse_ioc_3
            "analyse_ioc_3": {
                "specialist": "4e63a412-dfe4-4f99-823a-18f03aeed4c7",
                "tools": ["f458594d-e18c-4091-aeec-149651fe7742"],
                **prompts_and_inputs[4],
            },
            # analyse_technique
            "analyse_technique": {
                "specialist": "4e63a412-dfe4-4f99-823a-18f03aeed4c7",
                "tools": ["bf4065f8-3445-445e-b0b9-70d3615d0a6b"],
                **prompts_and_inputs[5],
            },
            # create_alert_report
            "create_alert_report": {
                "specialist": "97887aae-7c10-4f94-9fba-ae90246f4836",
                "tools": [],
                **prompts_and_inputs[6],
            },
            # output_processing
            "output_processing": {
                "specialist": "",
                "tools": [],
                **prompts_and_inputs[7],
            },
        },
    },
}
