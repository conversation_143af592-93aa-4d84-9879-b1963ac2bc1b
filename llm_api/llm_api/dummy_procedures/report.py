import csv
import io
import json
import logging
import os
from datetime import datetime
from typing import List

from llm_api.dummy_procedures.cdi_procedure import (
    PROCEDURE_ID_FORMAT,
    PROCEDURES_BUCKET,
)
from llm_api.dummy_procedures.s3 import get_object, list_folder

logger = logging.getLogger(__name__)


def create_report(
    env: str,
    orgs: List[str] = [],
    since_str: str = "",
    until_str: str = "",
) -> io.StringIO:
    logger.info(
        f"create_report {env}: orgs={orgs} ; since: {since_str}; until: {until_str}"
    )

    since_datetime = datetime.min
    if since_str:
        since_datetime = datetime.strptime(since_str, "%Y_%m_%d")
    until_datetime = datetime.max
    if until_str:
        until_datetime = datetime.strptime(until_str, "%Y_%m_%d")

    logger.info(f"Will report procedures between: [{since_datetime}, {until_datetime})")

    f = io.StringIO()
    csv_writer = csv.writer(f)
    csv_writer.writerow(
        ["org_id", "procedure_run_id", "procedure_id", "cost", "duration_seconds"]
    )

    for org_folder in list_folder(bucket_name=PROCEDURES_BUCKET, prefix=f"{env}"):
        logger.info(org_folder)
        read_org_folder = True
        if orgs and org_folder[:-1] not in orgs:
            read_org_folder = False
        if read_org_folder:
            for procedure_folder in list_folder(
                bucket_name=PROCEDURES_BUCKET, prefix=f"{env}/{org_folder}"
            ):
                logger.info(procedure_folder)
                for procedure_run in list_folder(
                    bucket_name=PROCEDURES_BUCKET,
                    prefix=f"{env}/{org_folder}{procedure_folder}",
                ):
                    logger.info(procedure_run)
                    procedure_datetime = datetime.strptime(
                        procedure_run[:-1],
                        PROCEDURE_ID_FORMAT,
                    )

                    if (
                        procedure_datetime >= since_datetime
                        and procedure_datetime < until_datetime
                    ):
                        logger.info(f"Reporting: {procedure_run[:-1]}")
                        proc_str = get_object(
                            bucket=PROCEDURES_BUCKET,
                            key=f"{env}/{org_folder}{procedure_folder}{procedure_run}procedure_output.json",
                        )
                        if proc_str:
                            procedure_output = json.loads(proc_str)
                            proc_id = (
                                procedure_output["procedure_id"]
                                if procedure_output.get("procedure_id")
                                else ""
                            )
                            csv_writer.writerow(
                                [
                                    org_folder[:-1],
                                    procedure_output["id"],
                                    proc_id,
                                    procedure_output["cost"],
                                    procedure_output["duration_sec"],
                                ]
                            )

    return f
