import json
import logging
import os
import re
import time
from asyncio import TaskGroup
from datetime import datetime
from typing import List, Optional

import tiktoken
from httpx import AsyncClient, HTTPStatusError
from pydantic import BaseModel, ValidationError

from llm_api.blai_api.dtos import CdiProcedureOutput, MessageResponse
from llm_api.dummy_procedures.cdi_procedure_comp import tasks
from llm_api.dummy_procedures.s3 import upload_to_s3

logger = logging.getLogger(__name__)

env = os.environ["BLAI_ENV"]
PROCEDURES_BUCKET = "ba-procedure-runs"
PROCEDURE_ID_FORMAT = "%Y_%m_%d_%H_%M_%S_%f"

ALLOWED_ORGS = [
    "065679f3-53a8-4c0a-b089-9e6fa105860f",
]


class Unauthorized(Exception):
    pass


class BudgetExceeded(Exception):
    pass


class Task(BaseModel):
    name: str
    specialist: str
    tools: List[str]
    prompt: str
    inputs: List[str]


class TaskOutput(BaseModel):
    input_task: Task
    response: Optional[MessageResponse] = None
    duration_sec: float
    cost: float


# util functions
async def make_api_call(
    user_query: str,
    specialist: str = "",
    tools: List[str] = [],
    use_formatting: bool = False,
    api_key: str = "p~BOS6otOpIkqmnhPUSqLU5S5Edrj",
) -> Optional[MessageResponse]:
    api_url = (
        "https://dev.bricklayer.ai/api/v1/apiClient/message"
        if env == "dev"
        else "https://app.bricklayer.ai/api/v1/apiClient/message"
    )

    async with AsyncClient(
        headers={
            "Content-Type": "application/json",
            "Authorization": api_key,
        },
        timeout=3600,
    ) as client:
        if not specialist:
            specialists = []
        else:
            specialists = [
                {
                    "id": specialist,
                    "children": tools,
                }
            ]

        payload = {
            "userQuery": user_query,
            "AIFilterComponents": specialists,
            "formatFinalAnswer": use_formatting,
        }
        try:
            # Make the POST API call using the requests library
            response = await client.post(api_url, json=payload)
            # Check if the request was successful (status code 200)
            response = response.raise_for_status()

            # Parse and use the response data
            data = response.json()
            logger.info(f"API Response: {response.status_code} {data}")
            return MessageResponse.parse_obj(data)

        except ValidationError as err:
            # this happens when the actual LLM response was an error
            # but the API responds with a 200 status code
            logger.error(err)

        except HTTPStatusError as e:
            logger.error(f"An error occurred: {e} | {e.request} | {e.response}")
            if e.response.status_code == 402:
                logger.error("Raising budget exceeded")
                raise BudgetExceeded()
        return None


def ioc_regex_identifier(text: str) -> List[str]:
    # Regex patterns
    sha1_pattern = re.compile(r"\b[0-9a-f]{40}\b", re.IGNORECASE)
    sha256_pattern = re.compile(r"\b[0-9a-f]{64}\b", re.IGNORECASE)
    md5_pattern = re.compile(r"\b[0-9a-f]{32}\b", re.IGNORECASE)

    domain_pattern = re.compile(
        r"\b(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}\b"
    )

    ipv4_pattern = re.compile(
        r"\b(?:(?:25[0-5]|2[0-4][0-9]|1?[0-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1?[0-9][0-9]|[0-9])\b"
    )
    ipv6_pattern = re.compile(
        r"\b((?:[0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|(?:[0-9a-fA-F]{1,4}:){1,7}:|(?:[0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|(?:[0-9a-fA-F]{1,4}:){1,5}(?::[0-9a-fA-F]{1,4}){1,2}|(?:[0-9a-fA-F]{1,4}:){1,4}(?::[0-9a-fA-F]{1,4}){1,3}|(?:[0-9a-fA-F]{1,4}:){1,3}(?::[0-9a-fA-F]{1,4}){1,4}|(?:[0-9a-fA-F]{1,4}:){1,2}(?::[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:(?:(?::[0-9a-fA-F]{1,4}){1,6})|:(?:(?::[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(?::[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(?:ffff(?::0{1,4}){0,1}:){0,1}(?:(?:25[0-5]|(?:2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(?:25[0-5]|(?:2[0-4]|1{0,1}[0-9]){0,1}[0-9])|(?:[0-9a-fA-F]{1,4}:){1,4}:(?:(?:25[0-5]|(?:2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(?:25[0-5]|(?:2[0-4]|1{0,1}[0-9]){0,1}[0-9]))(?=\s|$)"
    )

    # identify
    sha1_hashes = sha1_pattern.findall(text)
    sha256_hashes = sha256_pattern.findall(text)
    md5_hashes = md5_pattern.findall(text)
    ip_v4 = ipv4_pattern.findall(text)
    ip_v6 = ipv6_pattern.findall(text)
    domains = domain_pattern.findall(text)
    # create list
    ioc_list = sha1_hashes + sha256_hashes + md5_hashes + ip_v4 + ip_v6 + domains
    # return
    return ioc_list


def merge_context(prompt: str, context: Optional[List[str]]) -> str:
    if not context:
        return prompt
    else:
        context_string = "\n\n\n********** CONTEXT **********"
        for context_element in context:
            context_string += f"\n\n{context_element}"
        final_prompt = prompt + context_string + "\n\n********** CONTEXT ENDS**********"
        return final_prompt


async def run_task(
    organization_id: str,
    procedure_id: str,
    procedure_run_id: str,
    task: Task,
    context: Optional[List[str]],
    api_key: str,
    use_formatting: bool = False,
    s3_file_suffix: str = "",
    **kwargs,
) -> TaskOutput:
    logger.info(f"{procedure_id}: {task} | {context} | {kwargs}")
    start = time.perf_counter()
    # Assert that all inputs in the task are present in kwargs
    # and format the prompt using them
    assert len(task.inputs) == len(kwargs.keys())
    for inp in task.inputs:
        assert inp in kwargs.keys()

    formatted_prompt = task.prompt
    if kwargs:
        formatted_prompt = task.prompt.format(**kwargs)

    # merge the context to get the final prompt
    final_prompt = merge_context(formatted_prompt, context)

    response = await make_api_call(
        user_query=final_prompt,
        specialist=task.specialist,
        tools=task.tools,
        api_key=api_key,
        use_formatting=use_formatting,
    )

    end = time.perf_counter()

    output = TaskOutput(
        input_task=task,
        response=response,
        duration_sec=(end - start),
        cost=response.cost if response else 0,
    )

    logger.info(f"{procedure_id}: {output}")

    obj_for_s3 = output.dict()
    obj_for_s3.update(kwargs)
    obj_for_s3.update({"context": context})

    upload_to_s3(
        object=obj_for_s3,
        organization_id=organization_id,
        procedure_id=procedure_id,
        procedure_run_id=procedure_run_id,
        task_name=f"{task.name}{s3_file_suffix}",
        bucket_name=PROCEDURES_BUCKET,
    )

    return output


def output_processing(output=None):
    output = output[0]
    key_pattern = re.compile(r"----- ([A-Z\& \-]+) -----\n\n")
    section_pattern = re.compile(r"----- [A-Z\& \-]+ -----\n\n")
    keys = key_pattern.findall(output)
    sections = re.split(section_pattern, output)
    final_output = dict()
    for i in range(len(keys)):
        final_output[keys[i]] = sections[i + 1]
    return json.dumps(final_output)


async def cdi_api_call_to_bricklayer(
    procedure_input: str,
    organization_id: str,
    api_key: str,
    procedure_id: str,
) -> CdiProcedureOutput:
    logger.info(f"Running procedure {procedure_id} for org: {organization_id}")

    # generate procedure_id
    procedure_run_id = datetime.utcnow().strftime(PROCEDURE_ID_FORMAT)

    procedure_start = {"input": procedure_input}

    upload_to_s3(
        object=procedure_start,
        organization_id=organization_id,
        procedure_run_id=procedure_run_id,
        procedure_id=procedure_id,
        task_name="procedure_input",
        bucket_name=PROCEDURES_BUCKET,
    )

    tasks_repo = tasks[env].get(organization_id)

    if not tasks_repo:
        raise Unauthorized()

    start = time.perf_counter()

    total_cost = 0

    encoding = tiktoken.get_encoding("cl100k_base")
    tokens = encoding.encode(procedure_input)
    logger.info(f"Input length (tokens): {len(tokens)}")
    was_truncated = False

    all_sources = []

    async with TaskGroup() as tg:
        summarize_task = tg.create_task(
            run_task(
                organization_id=organization_id,
                procedure_id=procedure_id,
                procedure_run_id=procedure_run_id,
                task=Task.parse_obj(tasks_repo["summarise_alert"]),
                context=None,
                api_key=api_key,
                alert=procedure_input,
            )
        )
        ioc_task = tg.create_task(
            run_task(
                organization_id=organization_id,
                procedure_id=procedure_id,
                procedure_run_id=procedure_run_id,
                task=Task.parse_obj(tasks_repo["identify_ioc"]),
                context=None,
                api_key=api_key,
                alert=procedure_input,
            )
        )

    summary_output = summarize_task.result()
    total_cost += summary_output.cost
    ioc_output = ioc_task.result()
    ioc_list = ""
    if ioc_output.response:
        ioc_list = ioc_output.response.answer
    total_cost += ioc_output.cost

    processed_ioc_list = ioc_regex_identifier(ioc_list)
    logger.info(f"IDENTIFIED IOCS: {ioc_list}")
    all_analysis = []

    for idx, i in enumerate(processed_ioc_list):
        async with TaskGroup() as tg:
            ioc_analysis_task = tg.create_task(
                run_task(
                    organization_id=organization_id,
                    procedure_id=procedure_id,
                    procedure_run_id=procedure_run_id,
                    task=Task.parse_obj(tasks_repo["analyse_ioc"]),
                    s3_file_suffix=f"_{idx}",
                    context=[i],
                    api_key=api_key,
                )
            )

            ioc_analysis_2_task = tg.create_task(
                run_task(
                    organization_id=organization_id,
                    procedure_id=procedure_id,
                    procedure_run_id=procedure_run_id,
                    task=Task.parse_obj(tasks_repo["analyse_ioc_2"]),
                    s3_file_suffix=f"_{idx}",
                    context=[i],
                    api_key=api_key,
                )
            )

            ioc_analysis_3_task = tg.create_task(
                run_task(
                    organization_id=organization_id,
                    procedure_id=procedure_id,
                    procedure_run_id=procedure_run_id,
                    task=Task.parse_obj(tasks_repo["analyse_ioc_3"]),
                    s3_file_suffix=f"_{idx}",
                    context=[i],
                    api_key=api_key,
                )
            )

        ioc_analysis_output = ioc_analysis_task.result()
        ioc_analysis_2_output = ioc_analysis_2_task.result()
        ioc_analysis_3_output = ioc_analysis_3_task.result()

        total_cost += (
            ioc_analysis_output.cost
            + ioc_analysis_2_output.cost
            + ioc_analysis_3_output.cost
        )
        ioc_analysis, ioc_analysis_2, ioc_analysis_3 = "", "", ""
        if ioc_analysis_output.response:
            ioc_analysis = ioc_analysis_output.response.answer
            all_sources.extend(ioc_analysis_output.response.sources)
        if ioc_analysis_2_output.response:
            ioc_analysis_2 = ioc_analysis_2_output.response.answer
            all_sources.extend(ioc_analysis_2_output.response.sources)
        if ioc_analysis_3_output.response:
            ioc_analysis_3 = ioc_analysis_3_output.response.answer
            all_sources.extend(ioc_analysis_3_output.response.sources)
        all_analysis.extend([ioc_analysis, ioc_analysis_2, ioc_analysis_3])

    total_context = (
        [
            summary_output.response.answer if summary_output.response else "",
            ioc_list,
        ]
        + all_analysis
        + all_sources
    )  # [technique_analysis]

    # if procedure_id == 2:

    alert_analysis_report_output = await run_task(
        organization_id=organization_id,
        procedure_id=procedure_id,
        procedure_run_id=procedure_run_id,
        task=Task.parse_obj(tasks_repo["create_alert_report"]),
        context=total_context,
        api_key=api_key,
        # alert=procedure_input,
    )
    total_cost += alert_analysis_report_output.cost

    output = output_processing(
        [
            (
                alert_analysis_report_output.response.answer
                if alert_analysis_report_output.response
                else ""
            )
        ]
    )

    end = time.perf_counter()

    output = CdiProcedureOutput(
        id=procedure_run_id,
        procedure_id=procedure_id,
        answer=output,
        cost=total_cost,
        duration_sec=(end - start),
        input_truncated=was_truncated,
        sources=all_sources,
    )

    upload_to_s3(
        object=output.dict(),
        procedure_id=procedure_id,
        procedure_run_id=procedure_run_id,
        organization_id=organization_id,
        task_name="procedure_output",
        bucket_name=PROCEDURES_BUCKET,
    )

    return output


# if __name__ == "__main__":
#     alert = "{'tags': [], 'alerts': [{'title': 'MediaArena' unwanted software was detected during a scheduled scan', 'status': 'Resolved', 'alertId': 'da52451e4e-ac14-43e7-9864- f62e1da5ff24_1', 'devices': [{'tags': [], 'osBuild': 19045, 'version': '22H2', 'frstSeen': '2022-09-09T19:01:00.677Z', 'riskScore': 'None', 'osPlatform': 'Windows10', 'vmMetadata': None, 'aadDeviceId': 'dfae4573-9e28-49d3-a419- ecac0de087fc', 'osProcessor': 'x64', 'healthStatus': 'Active', 'deviceDnsName': 'wh-2612lt.int.hopeservices.org', 'loggedOnUsers': [{'domainName': 'HOPE', 'accountName': 'knakahama'}], 'mdatpDeviceId': '40d2276ee066b03da622a501561539a49fb89f36', 'rbacGroupName': None, 'defenderAvStatus': 'Updated', 'onboardingStatus': 'Onboarded'}], 'category': 'DefenseEvasion', 'entities': [{'sha1': '33c02d70abb2f1f12a79cfd780d875a94e7fe877', 'sha256': 'e248b01e3ccde76b4d8e8077d4fcb4d0b70e5200bf4e738b45a0bd28fbc2cae6', 'verdict': 'Malicious', 'deviceId': '40d2276ee066b03da622a501561539a49fb89f36', 'fleName': 'PDFpower (1).exe', 'flePath': 'C:\\Users\\<USER>\\Downloads', 'entityType': 'File', 'detectionStatus': 'Detected', 'remediationStatus': 'Blocked', 'evidenceCreationTime': '2023-05-08T01:51:55.84Z', 'remediationStatusDetails': 'Entity was pre-remediated by Windows Defender'}], 'severity': 'Informational', 'actorName': None, 'assignedTo': 'Automation', 'detectorId': '910e98ad-0eb4-480b-8aa6-841e9ecad15e', 'incidentId': 5119, 'description': 'Potentially unwanted software is a category of applications that install and perform undesirable activity without adequate user consent. These applications are not necessarily malicious, but their behaviors often negatively impact the computing experience, even appearing to invade user privacy. Many of these applications display advertising, modify browser settings, and install bundled software.', 'creationTime': '2023-05-08T01:51:55.6556557Z', 'lastActivity': '2023-05-08T01:50:14.7525415Z', 'resolvedTime': '2023-05-08T02:08:39.9587147Z', 'determination': None, 'frstActivity': '2023-05-08T01:50:14.7525415Z', 'serviceSource': 'MicrosoftDefenderForEndpoint', 'classifcation': None, 'detectionSource': 'WindowsDefenderAv', 'investigationId': 84, 'lastUpdatedTime': '2023-05-08T02:08:40.0766667Z', 'mitreTechniques': [], 'providerAlertId': '52451e4e-ac14-43e7-9864-f62e1da5ff24_1', 'threatFamilyName': 'MediaArena', 'investigationState': 'SuccessfullyRemediated'}], 'status': 'Resolved', 'comments': [], 'severity': 'Informational', 'assignedTo': None, 'incidentId': 5119, 'createdTime': '2023-05-08T01:51:56.0166667Z', 'incidentUri': 'https://security.microsoft.com/incidents/5119? tid=a6a9dcec-3d59-40bf-9076-27a1ad4fe1f9', 'incidentName': 'MediaArena unwanted software was detected during a scheduled scan on one endpoint', 'determination': 'NotAvailable', 'classifcation': 'Unknown', 'lastUpdateTime': '2023-05-08T02:08:40.0766667Z', 'redirectIncidentId': None}]"
#     print(cdi_api_call_to_bricklayer(alert))
