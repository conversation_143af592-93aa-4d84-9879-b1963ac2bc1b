import json
import logging
import os
from typing import Optional

import boto3
import botocore.config

logger = logging.getLogger(__name__)


env = os.environ["BLAI_ENV"]


def upload_to_s3(
    object: dict,
    organization_id: str,
    procedure_id: str,
    procedure_run_id: str,
    task_name: str,
    bucket_name: str,
):
    key = f"{env}/{organization_id}/{procedure_id}/{procedure_run_id}/{task_name}.json"

    logger.info(f"Uploading to S3: {bucket_name}/{key}")

    config = botocore.config.Config(retries={"mode": "adaptive", "max_attempts": 4})
    client = boto3.client("s3", config=config)

    client.put_object(Body=json.dumps(object, indent=2), Bucket=bucket_name, Key=key)


def list_folder(bucket_name: str, prefix: str):
    config = botocore.config.Config(retries={"mode": "adaptive", "max_attempts": 4})
    client = boto3.client("s3", config=config)

    if not prefix.endswith("/"):
        prefix += "/"

    params = {
        "Bucket": bucket_name,
        "Delimiter": "/",
        "MaxKeys": 200,
    }
    if prefix != "/":
        params["Prefix"] = prefix

    resp = client.list_objects_v2(**params)
    if not "CommonPrefixes" in resp:
        return
    common_prefixes = [
        r["Prefix"][len(prefix) if prefix != "/" else 0 :]
        for r in resp["CommonPrefixes"]
        if prefix in r["Prefix"]
    ]
    yield from common_prefixes

    cont_token = resp.get("NextContinuationToken")

    while cont_token:
        params["ContinuationToken"] = cont_token
        resp = client.list_objects_v2(**params)

        common_prefixes = [
            r["Prefix"][len(prefix) :]
            for r in resp["CommonPrefixes"]
            if prefix in r["Prefix"]
        ]
        yield from common_prefixes

        cont_token = resp.get("NextContinuationToken")


def get_object(bucket: str, key: str) -> Optional[str]:
    config = botocore.config.Config(retries={"mode": "adaptive", "max_attempts": 4})
    client = boto3.client("s3", config=config)

    try:
        obj = client.get_object(Bucket=bucket, Key=key)
        text = obj["Body"].read()
        return text.decode("utf-8")
    except Exception as e:
        logger.warning(f"Not found: {bucket}/{key}: {e}")
        return None
