from llm_api.models import ProblemDetails


class ContentFilteringError(Exception):
    pass


class HTTPProblem(Exception):
    """
    This exception should be raised when an HTTP error in the form of
    the RFC9457 problem details has been generated.
    """

    problem: ProblemDetails
    status: int

    def __init__(self, problem: ProblemDetails, status: int):
        self.problem = problem
        self.status = status
