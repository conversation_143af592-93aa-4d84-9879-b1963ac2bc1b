import hashlib
import json
import logging
import tempfile
from contextlib import asynccontextmanager, contextmanager
from pathlib import Path
from typing import I<PERSON>, List, Optional, Tuple, Union

import boto3
import fitz
from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings
from pymupdf import Page, Rect

from llm_api.blai_api.dtos import LTRB, PDFAnnotationRequest, PDFCoordinates
from llm_api.blai_llm.constants import S3_BUCKET

logger = logging.getLogger(__name__)


class AnnotatePDFSettings(BaseSettings):
    s3_bucket: Optional[str] = Field(default=S3_BUCKET, description="S3 bucket name")
    local_root: Path = Field(description="Local root directory for storing results.")
    zoom: int = Field(default=200, description="Zoom of page")


import aioboto3
from aiofile import async_open


@asynccontextmanager
async def atemp_download_from_s3(bucket: str, key: str):
    session = aioboto3.Session()

    async with session.client("s3") as s3:
        with tempfile.TemporaryDirectory() as temp_dir:
            bucket_path = Path(temp_dir) / bucket
            file_path = bucket_path / key

            if not file_path.exists():
                logger.info(f"Downloading {bucket}: {key} to {file_path}")
                file_path.parent.mkdir(parents=True, exist_ok=True)

                response = await s3.get_object(Bucket=bucket, Key=key)
                async with async_open(file_path, "wb") as f:
                    async for chunk in response["Body"].iter_chunks():
                        await f.write(chunk)

            yield file_path


@contextmanager
def temp_download_from_s3(bucket: str, key: str):
    s3 = boto3.client("s3")
    with tempfile.TemporaryDirectory() as temp_dir:
        bucket_path = Path(temp_dir) / bucket
        file_path = bucket_path / key
        if not file_path.exists():
            logger.info(f"Downloading {bucket}: {key} to {file_path}")
            bucket_path.mkdir(parents=True, exist_ok=True)
            s3.download_file(bucket, key, file_path)
        yield file_path


class AnnotatePDF(BaseModel):
    settings: AnnotatePDFSettings

    @staticmethod
    def _annotate_pdf(
        file_path: Path,
        request: PDFAnnotationRequest,
        local_root: Optional[Path] = None,
    ) -> Path:
        # !! Requires mirrored implementation in Front End
        md5_hash = hashlib.md5(
            json.dumps(request.model_dump(), sort_keys=True).encode("utf-8")
        ).hexdigest()
        root_path = file_path.parent if local_root is None else local_root
        root_path.mkdir(parents=True, exist_ok=True)
        annotated_file_path = root_path / md5_hash

        # TODO: Cache annotation and fragments
        if annotated_file_path.exists():
            return annotated_file_path

        with fitz.open(file_path) as pdf_doc:
            for pdf_coord in request.coordinates:
                page: Page = pdf_doc[
                    pdf_coord.page_number - 1
                ]  # page number to index map
                offset = (
                    pdf_coord.layout_width - page.rect.width
                ) / 2  # Sometimes the document layout and actual reference are misaligned
                coord = pdf_coord.coord
                x0, x1 = coord.x0, coord.x1
                x0 -= offset
                x1 -= offset
                quad = Rect(x0, coord.y0, x1, coord.y1)
                highlight = page.add_rect_annot(quad)
                info = highlight.info
                highlight.set_info(info)
                highlight.update()
            pdf_doc.save(annotated_file_path)
        return annotated_file_path

    def _handle_s3(
        self, bucket: str, request: PDFAnnotationRequest, local_root: Path
    ) -> Path:
        with temp_download_from_s3(bucket, request.source) as file_path:
            annotated_file_path = self._annotate_pdf(file_path, request, local_root)
            return annotated_file_path

    async def _ahandle_s3(
        self, bucket: str, request: PDFAnnotationRequest, local_root: Path
    ):
        async with atemp_download_from_s3(bucket, request.source) as file_path:
            annotated_file_path = self._annotate_pdf(file_path, request, local_root)
            return annotated_file_path

    def __call__(self, request: PDFAnnotationRequest) -> Path:
        match self.settings:
            case AnnotatePDFSettings(s3_bucket=None, local_root=local_root):
                return self._annotate_pdf(Path(request.source), request, local_root)
            case AnnotatePDFSettings(s3_bucket=s3_bucket, local_root=local_root):
                return self._handle_s3(s3_bucket, request, local_root)
            case _:
                raise ValueError("Invalid settings")

    async def acall(self, request: PDFAnnotationRequest) -> Path:
        match self.settings:
            case AnnotatePDFSettings(s3_bucket=None, local_root=local_root):
                return self._annotate_pdf(Path(request.source), request, local_root)
            case AnnotatePDFSettings(s3_bucket=s3_bucket, local_root=local_root):
                return await self._ahandle_s3(s3_bucket, request, local_root)
            case _:
                raise ValueError("Invalid settings")


class PDFSearch(BaseModel):
    case_sensitive: bool = False
    hit_max: int = 1
    category: str = "NarrativeText"

    def __call__(
        self, pdf: Union[str, Path, IO[bytes]], search_string: str
    ) -> List[PDFCoordinates]:
        if isinstance(pdf, (str, Path)):
            doc = fitz.open(pdf)
        elif hasattr(pdf, "read"):
            doc = fitz.open(stream=pdf, filetype="pdf")
        else:
            raise TypeError("pdf must be a file path or a binary stream (IO[bytes])")

        results: List[PDFCoordinates] = []

        with doc:
            for page_num, page in enumerate(doc, start=1):
                layout_width = page.rect.width
                flags = (
                    fitz.TEXT_PRESERVE_WHITESPACE
                    | fitz.TEXT_DEHYPHENATE
                    | fitz.TEXT_PRESERVE_LIGATURES
                    | fitz.TEXT_MEDIABOX_CLIP
                    | fitz.TEXT_USE_CID_FOR_UNKNOWN_UNICODE
                )
                matches = page.search_for(search_string, flags=flags)

                if matches:
                    rect = matches[0]
                    coord = LTRB(x0=rect.x0, y0=rect.y0, x1=rect.x1, y1=rect.y1)
                else:
                    coord = LTRB(x0=0, y0=0, x1=1, y1=1)

                results.append(
                    PDFCoordinates(
                        coord=coord,
                        page_number=page_num,
                        category=self.category,
                        layout_width=layout_width,
                        excerpt=search_string,
                    )
                )

        results = results[: min(self.hit_max, len(results))]
        return results
