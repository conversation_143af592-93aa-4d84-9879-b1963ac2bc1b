import json
import logging
import re
from typing import Any, Dict, List, Tuple
from uuid import UUID

from langchain_core.callbacks import <PERSON>CallbackHandler
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.tools import BaseTool
from pydantic import Extra, root_validator
from sympy import Q

from llm_api.blai_api.dtos import Evidence, EvidenceOrigin
from llm_api.blai_llm.utils import PlanLogger, sanitize_tool_name
from llm_api.callbacks import LoggingCallbackHandler
from llm_api.llm.factory import default_4_gpt_spec_data, get_model_from_spec
from llm_api.mixins import LLMPromptTruncationMixin
from llm_api.specs.llm_spec import LLMSpec, LLMType
from llm_api.specs.reporter_spec import ReporterSpec, ReportType
from llm_api.utils import store_evidence

logger = logging.getLogger(__name__)


class Reporter(LLMPromptTruncationMixin):
    def __init__(
        self,
        spec: ReporterS<PERSON>,
        store_json_location: str,
        logging_cb: Logging<PERSON><PERSON>back<PERSON><PERSON><PERSON>,
        plan_id: UUID,
        **_,
    ):
        self.spec = spec
        self.store_json_location = store_json_location
        self.logging_cb = logging_cb
        self.plan_id = plan_id

    async def acall(self, input_text, callbacks: List[BaseCallbackHandler] = []):
        truncated_input = self.truncate_context(input_text)
        if len(truncated_input) < len(input_text):
            log = PlanLogger()
            log.addToLog(f"Input for {self.spec.name} has been truncated", self.plan_id)
            logger.warning(
                "Input for %s has been truncated. New input: %s",
                self.spec.name,
                truncated_input,
            )
            input_text = truncated_input

        match self.spec.report_type:
            case ReportType.Short:
                evidence, response = await self.create_short_report(
                    input_text=input_text,
                    callbacks=callbacks,
                )
            case ReportType.Long:
                evidence, response = await self.create_long_report()

        if not evidence:
            self.logging_cb.format_msg("No evidence stored", "acall")

        ret_value = {
            "output": (
                f"I stored the result at the following location: {evidence.evidence_location}"
                if evidence
                else response
            ),
            "intermediate_steps": [
                [{}, {"evidence": [evidence if evidence else response]}],
            ],
        }
        return ret_value

    async def create_short_report(
        self,
        input_text: str,
        callbacks: List[BaseCallbackHandler],
    ) -> Tuple[Evidence | None, str]:
        # prepare the prompt by parsing the given sections
        final_prompt = self.prepare_prompt()

        llm = get_model_from_spec(
            self.spec.llm
            if self.spec.llm
            else LLMSpec(
                type=LLMType.AzureChatOpenAI,
                data=default_4_gpt_spec_data,
            )
        )

        if not llm:
            return None, ""

        response = (
            await llm.ainvoke(
                [
                    SystemMessage(content=final_prompt),
                    HumanMessage(
                        content=(
                            input_text["input"]
                            if isinstance(input_text, dict)
                            else input_text
                        )
                    ),
                ]
            )
        ).content

        key_pattern = re.compile(self.spec.section_name_pattern)
        keys = key_pattern.findall(response)
        logger.info(
            self.logging_cb.format_msg(
                f"Identified keys: {keys}", func="create_short_report"
            )
        )

        section_pattern = re.compile(self.spec.section_text_pattern)
        sections = re.split(section_pattern, response)

        final_output = {keys[i]: sections[i + 1] for i in range(len(keys))}

        evidence = store_evidence(
            evidence_text=json.dumps(final_output),
            evidence_name="Report",
            evidence_type=EvidenceOrigin.Report,
            evidence_location=self.store_json_location,
        )
        return evidence, response

    async def create_long_report(self) -> Tuple[Evidence | None, str]:
        pass

    def prepare_prompt(self):

        section_texts = [
            self.spec.section_template.format(**section)
            for section in self.spec.sections
        ]

        return self.spec.short_prompt.format(sections="".join(section_texts))


class ReporterTool(BaseTool):
    reporter: Reporter

    class Config:
        extra = Extra.allow

    args_schema: dict = {
        "type": "object",
        "properties": {
            "context": {
                "type": "string",
                "description": "All the information needed to generate the report.",
            }
        },
        "required": ["context"],
    }

    @root_validator(pre=True)
    def parse_params(cls, values: Dict[str, Any]) -> Dict[str, Any]:
        values["reporter"] = Reporter(**values)

        values["name"] = sanitize_tool_name(values["spec"].name)
        values["description"] = values["spec"].description

        return values

    def _run(self, context: str):
        return NotImplementedError(
            f"You are calling a sync method on {self.__class__}. Please don't!"
        )

    async def _arun(self, *args, **kwargs):
        context = kwargs.get("context")
        if not context and args:
            context = args[0]
        log = PlanLogger()
        log_text = f"Used '{self.name}' with the input '{context}'"
        log.addToLog(log_text, self.plan_id)

        if len(context) < 150:
            logger.error(
                f"Insufficient context to Create Report. Got context: {context}",
            )
            raise ValueError(
                "Insufficient context to Create Report. "
                "Be sure to include the entire context if any."
            )

        output = await self.reporter.acall(
            context, callbacks=[self.reporter.logging_cb]
        )

        all_sources = []
        all_apis = []
        all_evidence = []
        intermediate_steps = output.get("intermediate_steps")
        if intermediate_steps:
            try:
                for interm_step in intermediate_steps:
                    if "source_documents" in interm_step[1].keys():
                        source_docs = interm_step[1]["source_documents"]
                        all_sources.extend(source_docs)
                    if "source_api" in interm_step[1].keys():
                        source_api = interm_step[1]["source_api"]
                        all_apis.extend(source_api)
                    if "evidence" in interm_step[1].keys():
                        evidence = interm_step[1]["evidence"]
                        all_evidence.extend(evidence)
            except Exception as error:
                logger.error(f"aresearch {error}")
                pass

        reply = {
            "context": context,
            "answer": output["output"] if isinstance(output, dict) else output,
        }
        reply["source_documents"] = all_sources
        reply["source_api"] = all_apis
        reply["evidence"] = all_evidence

        log_text = f"'{self.name}' is done"
        log.addToLog(log_text, self.plan_id)

        return reply
