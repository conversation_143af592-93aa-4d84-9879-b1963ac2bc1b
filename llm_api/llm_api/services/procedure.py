import logging
import os
import re
from typing import Any, Dict, List

from httpx import AsyncClient
from langchain_core.callbacks import <PERSON>CallbackHandler
from langchain_core.tools import BaseTool
from pydantic import Extra, root_validator

from llm_api.blai_llm.utils import PlanLogger, sanitize_tool_name
from llm_api.callbacks import LoggingCallbackHandler
from llm_api.procedures.types import ProcedureResult
from llm_api.specs.procedure_spec import ProcedureToolSpec
import re
from typing import Any, Dict, Optional, Type
from langchain.tools import BaseTool
from pydantic import BaseModel, root_validator, Field, Extra, create_model

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

class Procedure:

    def __init__(
        self,
        spec: ProcedureToolSpec,
        logging_cb: LoggingCallbackHandler,
        org_id: str,
        consumer_id: str,
        consumer_type: int,
        message_id: str,
        conversation_id: str,
        **_,
    ):
        self.spec = spec
        self.logging_cb = logging_cb
        self.org_id = org_id
        self.consumer_id = consumer_id
        self.consumer_type = consumer_type
        self.message_id = message_id
        self.conversation_id = conversation_id

    async def acall(self, input_text, callbacks: List[BaseCallbackHandler] = []):
        try:
            ret_value = {}

            input_values = re.findall(self.spec.input_match_regex, input_text)

            if not input_values:
                logger.error(f"No inputs extracted from {input_text}")
                return {"output": "An error occurred while running the procedure!"}

            inputs = []
            for input_pair in input_values:
                key_value = input_pair.split(",", 1)
                if not key_value:
                    logger.error(f"Faulty inputs found in: {input_text}")
                    return {"output": "An error occurred while running the procedure!"}

                inputs.append(
                    {
                        "name": key_value[0].strip(),
                        "value": key_value[1].strip(),
                    }
                )

            # check if we got enough inputs from GPT
            extracted_input_names = set([inp["name"] for inp in inputs])
            procedure_input_names = set([inp.name for inp in self.spec.inputs])
            if extracted_input_names != procedure_input_names:
                logger.warning(
                    self.logging_cb.format_msg(
                        f"extracted input: {inputs} | expected input: {self.spec.inputs}",
                        "acall",
                    )
                )
                return {
                    "output": "Couldn't run the procedure because there isn't enough information to extract the procedure inputs. Consider rephrasing your question."
                }

            payload = {
                "procedureId": self.spec.procedure_id,
                "organizationId": self.org_id,
                "consumerId": self.consumer_id,
                "consumerType": self.consumer_type,
                "inputs": inputs,
                "messageCorrelationId": self.message_id,
                "conversationId": self.conversation_id,
            }

            backend_url = os.environ["PUBLIC_BACKEND_URL"]
            # HACK until we figure these env vars...
            backend_url = backend_url.replace("/api/v1", "")
            backend_procedure_run_sync_url = (
                f"{backend_url}/internal/webhooks/procedures/conversation/run"
            )

            async with AsyncClient(timeout=600) as client:
                logger.info(
                    self.logging_cb.format_msg(
                        f"running procedure with payload: {payload} - {backend_procedure_run_sync_url}",
                        "acall",
                    )
                )
                resp = await client.post(
                    url=backend_procedure_run_sync_url,
                    json=payload,
                )

            if resp.status_code != 200:
                logger.error(
                    self.logging_cb.format_msg(
                        f"ERROR {resp.status_code} {backend_procedure_run_sync_url} - {payload}",
                        "acall",
                    )
                )
                return {"output": "An error occurred while running the procedure!"}

            result = ProcedureResult.parse_obj(resp.json())

            logger.info(self.logging_cb.format_msg(f"Got result: {result}", "acall"))

            # for reports / sources add extra flag to signal FE that it needs
            # to read from a different bucket
            for ev in result.evidence:
                ev.readFromProcedure = True

            ret_value = {
                "output": result.executive_summary,
                "intermediate_steps": [
                    [{}, {"evidence": [ev for ev in result.evidence]}],
                ],
            }
        except Exception as e:
            logger.error(
                self.logging_cb.format_msg(f"ERROR running procedure: {e}", "acall")
            )
            return {"output": "An error occurred while running the procedure!"}

        return ret_value


class ProcedureTool(BaseTool):
    procedure_tool: Procedure
    args_schema: Optional[Type[BaseModel]] = None
    input_field_mapping: Dict[str, str] = {}

    class Config:
        extra = Extra.allow

    @root_validator(pre=True)
    def parse_params(cls, values: Dict[str, Any]) -> Dict[str, Any]:
        values["procedure_tool"] = Procedure(**values)

        procedure_name = (
            values["spec"].procedure_name
            if values["spec"].procedure_name
            else values["spec"].procedure_id
        )
        values["name"] = sanitize_tool_name(f"PROCEDURE[{procedure_name}]")
        description = values["spec"].procedure_overview + "\n"
        
        # Create dynamic input fields and mapping
        input_fields = {}
        field_mapping = {}

        for idx, proc_input in enumerate(values["spec"].inputs, start=1):
            field_name = f"input{idx}"
            input_fields[field_name] = (
                str,
                Field(..., description=f"{proc_input.name} → {proc_input.description}")
            )
            field_mapping[field_name] = proc_input.name

        DynamicInputModel = create_model("DynamicInputModel", **input_fields)
        values["args_schema"] = DynamicInputModel
        values["input_field_mapping"] = field_mapping

        description += "INPUT should be a LIST of comma separated name and value pairs corresponding to the following descriptions:\n"

        for proc_input in values["spec"].inputs:
            description += (
                f"Name: {proc_input.name} | Description: {proc_input.description}\n"
            )

        description += """EXAMPLE:
         * [[[input_name1, value1]]], [[[input_name2, value2]]]
        """

        values["description"] = description

        return values

    def _run(self, *args, **kwargs):
        raise NotImplementedError(
            f"You are calling a sync method on {self.__class__}. Please don't!"
        )

    async def _arun(self, input: str = None, **kwargs):
        log = PlanLogger()

        # Case 1: openai_functions=False
        if input and not kwargs:
            structured_input = input
        # Case 2: openai_functions=True
        elif kwargs:
            parts = [
                f"[[[{self.input_field_mapping.get(k, k)}, {v}]]]"
                for k, v in kwargs.items()
            ]
            structured_input = ", ".join(parts)
            logger.debug(f"Mapped structured input: {structured_input}")

        else:
            raise ValueError("No valid input received in ProcedureTool")
        
        question = structured_input
        output = await self.procedure_tool.acall(
            question, callbacks=[self.procedure_tool.logging_cb]
        )

        all_sources = []
        all_apis = []
        all_evidence = []
        intermediate_steps = output.get("intermediate_steps")
        if intermediate_steps:
            try:
                for interm_step in intermediate_steps:
                    if "source_documents" in interm_step[1].keys():
                        source_docs = interm_step[1]["source_documents"]
                        all_sources.extend(source_docs)
                    if "source_api" in interm_step[1].keys():
                        source_api = interm_step[1]["source_api"]
                        all_apis.extend(source_api)
                    if "evidence" in interm_step[1].keys():
                        evidence = interm_step[1]["evidence"]
                        all_evidence.extend(evidence)
            except Exception as error:
                logger.error(f"aresearch {error}")
                pass

        reply = {
            "question": question,
            "answer": output["output"] if isinstance(output, dict) else output,
            "source_documents": all_sources,
            "source_api": all_apis,
            "evidence": all_evidence,
        }

        log_text = f"'{self.name}' is done"
        log.addToLog(log_text, self.plan_id)

        return reply
