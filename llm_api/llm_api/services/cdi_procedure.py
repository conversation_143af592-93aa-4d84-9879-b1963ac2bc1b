import logging
import os
from typing import Any, Coroutine, Dict, Optional, Tu<PERSON>
from uuid import UUID

from httpx import AsyncClient
from langchain_core.tools import BaseTool
from pydantic import Extra, root_validator

from llm_api.blai_llm.constants import COMPONENT_INPUT_TRUNCATION_LENGTH
from llm_api.blai_llm.utils import PlanLogger, sanitize_tool_name
from llm_api.callbacks import LoggingCallbackHandler
from llm_api.specs.api_plugin_spec import ApiCredentialsSpec, ApiCredentialType
from llm_api.specs.procedure_spec import CdiProcedureSpec

logger = logging.getLogger(__name__)


env = os.environ["BLAI_ENV"]


class CdiProcedureService(BaseTool):
    spec: CdiProcedureSpec
    plan_id: UUID

    logging_cb: LoggingCallbackHandler

    organization_id: str

    @root_validator(pre=True)
    def validate_basic(cls, values: Dict) -> Dict:
        if not values.get("name"):
            values["name"] = sanitize_tool_name(values["spec"].name)
        if not values.get("description"):
            values["description"] = values["spec"].description

        return values

    class Config:
        extra = Extra.allow

    def _run(self, question: str):
        return NotImplementedError(
            f"You are calling a sync method on {self.__class__}. Illegal!"
        )

    async def _arun(self, question: str) -> Coroutine[Any, Any, Any]:
        log = PlanLogger()
        if len(question) > COMPONENT_INPUT_TRUNCATION_LENGTH:
            log_text = f"Used '{self.name}' with the input '{question[:COMPONENT_INPUT_TRUNCATION_LENGTH]}...'"
        log.addToLog(log_text, self.plan_id)
        response, source = await self.call_api(question)
        if source:
            return {"question": question, "answer": response, "source_api": source}
        else:
            return response

    async def call_api(self, question: str) -> Tuple[str, str]:

        if not self.spec.extra_headers:
            self.spec.extra_headers = {}
        self.spec.extra_headers.update(
            {"Authorization": self.spec.credentials.data.key.get_secret_value()}
        )

        async with self.prepare_client(
            credentials=self.spec.credentials,
            extra_headers=self.spec.extra_headers,
        ) as client:
            response = await client.post(
                url=self.spec.procedure_endpoint,
                json={"input": question},
            )

        if response.status_code == 200:
            logger.info(f"API response len: {len(response.text)}")
            logger.info(f"API response: {response.text}")

            resp_obj = response.json()

            s3_link_template = "https://s3.console.aws.amazon.com/s3/buckets/ba-procedure-runs?region=us-east-1&bucketType=general&prefix={env}/{organization_id}/{procedure_id}/&showversions=false"
            final_source = s3_link_template.format(
                env=env,
                organization_id=self.organization_id,
                procedure_id=resp_obj["id"],
            )

            all_sources = [final_source]
            procedure_sources = resp_obj.get("sources")
            if procedure_sources:
                all_sources.extend(procedure_sources)

            return (
                resp_obj["answer"],
                all_sources,
            )

        logger.error(f"ERROR - {response.status_code}")
        return (
            f"Error retrieving threat intelligence data. Status Code: {response.status_code}",
            "",
        )

    def prepare_client(
        self,
        credentials: Optional[ApiCredentialsSpec],
        extra_headers: Optional[Dict[str, Any]],
    ) -> AsyncClient:
        client_params = {}

        # set a 10m timeout for procedure calls
        client_params["timeout"] = 600

        if credentials:
            match credentials.type:
                case ApiCredentialType.ApiKey:
                    client_params["headers"] = {
                        credentials.data.header: credentials.data.key.get_secret_value()
                    }
                case ApiCredentialType.BasicAuth:
                    client_params["auth"] = (
                        credentials.data.username.get_secret_value(),
                        credentials.data.password.get_secret_value(),
                    )

            if extra_headers:
                client_params["headers"].update(extra_headers)

        client = AsyncClient(**client_params)

        return client
