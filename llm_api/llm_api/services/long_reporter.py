import json
import logging
from typing import Any, Dict, List
from uuid import UUID

from langchain_core.callbacks import <PERSON>CallbackHandler
from langchain_core.messages import HumanMessage
from langchain_core.tools import BaseTool
from pydantic import Extra, root_validator

from llm_api.blai_llm.utils import PlanLogger, sanitize_tool_name
from llm_api.callbacks import <PERSON>ggingCallbackHandler
from llm_api.llm.factory import get_model_from_spec
from llm_api.mixins import LLMPromptTruncationMixin
from llm_api.specs.long_reporter_spec import LongReporterSpec
from llm_api.utils import store_evidence

logger = logging.getLogger(__name__)


class LongReporter(LLMPromptTruncationMixin):
    def __init__(
        self,
        spec: LongReporterSpec,
        store_json_location: str,
        logging_cb: LoggingCallbackHandler,
        plan_id: UUID,
        **_,
    ):
        self.spec = spec
        self.store_json_location = store_json_location
        self.logging_cb = logging_cb
        self.plan_id = plan_id

    async def acall(self, input_text, callbacks: List[BaseCallbackHandler] = []):
        truncated_input = self.truncate_context(input_text)
        if len(truncated_input) < len(input_text):
            log = PlanLogger()
            log.addToLog(f"Input for {self.spec.name} has been truncated", self.plan_id)
            logger.warning(
                "Input for %s has been truncated. New input: %s",
                self.spec.name,
                truncated_input,
            )
            input_text = truncated_input

        section_texts = []
        sections_completed = "Nothing so far"
        report_title = self.spec.report_title

        sections = self.spec.sections

        agg_context = input_text

        llm = get_model_from_spec(self.spec.llm)

        for section in sections:
            section_name = section.name

            section_description = section.description
            prompt = f"Generate a few lines to fill in a section titled {section_name} in a cyber threat intelligence report. Earlier parts of the report might have already been written. This section can be described as the following:\n\n{section_description}\n\nWrite this section for a report titled {report_title}. DO NOT INCLUDE the name of the section.\n Here is some information you can use to compose the content:\n\n************INFORMATION***********\n\n"
            prompt += agg_context
            prompt += "\n\n********** INFORMATION ENDS ************\n\n"
            prompt += "************** PREVIOUS SECTIONS *************\n\n"
            prompt += sections_completed
            prompt += "\n\n*********************************************\n\n"
            prompt += f"{section_name}:\n"
            # prompts.append(prompt)
            logger.info(
                self.logging_cb.format_msg(
                    msg=f"calling llm for section: {section_name} using prompt: {prompt}",
                    func="acall",
                )
            )
            res = llm([HumanMessage(content=prompt)], callbacks=callbacks)
            section_text = res.content
            section_texts.append(section_text)
            sections_completed = ""
            for sec, sec_text in zip(sections, section_texts):
                sections_completed += "\n" + sec.name + "\n\n"
                sections_completed += sec_text
                sections_completed += "\n\n"

        final_output = {
            sec.name: sec_text for sec, sec_text in zip(sections, section_texts)
        }
        response = ""
        for sec, sec_text in zip(sections, section_texts):
            response += sec.name + "\n"
            response += sec_text + "\n"

        evidence = store_evidence(
            evidence_text=json.dumps(final_output),
            evidence_name="Report",
            evidence_type="report",
            evidence_location=self.store_json_location,
        )

        ret_value = {
            "output": (
                f"I stored the result at the following location: {evidence.evidence_location}"
                if evidence
                else response
            ),
            "intermediate_steps": [
                [{}, {"evidence": [evidence if evidence else response]}],
            ],
        }
        return ret_value


class LongReporterTool(BaseTool):
    reporter: LongReporter

    class Config:
        extra = Extra.allow

    args_schema: dict = {
        "type": "object",
        "properties": {
            "context": {
                "type": "string",
                "description": "All the information needed to generate the report.",
            }
        },
        "required": ["context"],
    }

    @root_validator(pre=True)
    def parse_params(cls, values: Dict[str, Any]) -> Dict[str, Any]:
        values["reporter"] = LongReporter(**values)

        values["name"] = sanitize_tool_name(values["spec"].name)
        values["description"] = values["spec"].description

        return values

    def _run(self, context: str):
        return NotImplementedError(
            f"You are calling a sync method on {self.__class__}. Please don't!"
        )

    async def _arun(self, context: str):
        log = PlanLogger()
        log_text = f"Used '{self.name}' with the input '{context}'"
        log.addToLog(log_text, self.plan_id)

        output = await self.reporter.acall(
            context, callbacks=[self.reporter.logging_cb]
        )

        all_sources = []
        all_apis = []
        all_evidence = []
        intermediate_steps = output.get("intermediate_steps")
        if intermediate_steps:
            try:
                for interm_step in intermediate_steps:
                    if "source_documents" in interm_step[1].keys():
                        source_docs = interm_step[1]["source_documents"]
                        all_sources.extend(source_docs)
                    if "source_api" in interm_step[1].keys():
                        source_api = interm_step[1]["source_api"]
                        all_apis.extend(source_api)
                    if "evidence" in interm_step[1].keys():
                        evidence = interm_step[1]["evidence"]
                        all_evidence.extend(evidence)
            except Exception as error:
                logger.error(f"aresearch {error}")
                pass

        reply = {
            "context": context,
            "answer": output["output"] if isinstance(output, dict) else output,
        }
        reply["source_documents"] = all_sources
        reply["source_api"] = all_apis
        reply["evidence"] = all_evidence

        log_text = f"'{self.name}' is done"
        log.addToLog(log_text, self.plan_id)

        return reply
