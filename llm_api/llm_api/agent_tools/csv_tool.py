import logging
import os
import time

import boto3
from langchain_core.tools import BaseTool
from pydantic import root_validator

from llm_api.blai_api.dtos import Evidence, EvidenceOrigin
from llm_api.blai_llm.constants import S3_BUCKET
from llm_api.csv.csv_answer_pipeline import CSVAnswerPipeline
from llm_api.csv.csv_utils import MAX_CSV_RESULT_LENGTH
from llm_api.csv.data_loaders import SingleFileLoader
from llm_api.llm.factory import get_model_from_spec
from llm_api.specs.csv_tool_spec import CSVToolSpec

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


class CSVTool(BaseTool):
    spec: CSVToolSpec

    args_schema: dict = {
        "type": "object",
        "properties": {
            "question": {
                "type": "string",
                "description": "User query and S3 key separated by ' --- '",
            }
        },
        "required": ["question"],
    }

    name: str = "CSVTool"
    description: str = ""
    organization_id: str = ""

    @root_validator(pre=True)
    def populate_organization_id(cls, values):
        spec = values.get("spec")
        if spec and hasattr(spec, "organization_id"):
            values["organization_id"] = spec.organization_id
        values["name"] = "CSVTool"
        values[
            "description"
        ] = """
useful for processing CSV files stores in S3. 
These files are annotated in the conversation content with the following formats:
1) [S3File key=<key>] (ex. [S3File key="org-files/abc/test.csv"], the key the s3 key of the file)
2) [CSV s3_key=<key>]...[/CSV] (ex. [CSV s3_key="org-files/abc/test.csv"]foo\nbar[/CSV], the s3_key is the s3 key of the file)
The tool input should be the users query, the separator ( --- ) and the key of the file, for example:
How many false positives were detected? --- org-files/pzpesrx7o5h6xz873m7gv3hs/test.csv"""
        return values

    def _run(self, question: str):
        raise NotImplementedError("Use async method _arun instead.")

    async def _arun(self, *args, **kwargs):
        input_str = kwargs.get("question", args[0] if args else "")
        question = None
        s3_key = None
        if "---" in input_str:
            question, s3_key = [p.strip() for p in input_str.split("---", 1)]
        else:
            question = input_str
            if "key" in kwargs:
                s3_key = kwargs["key"]
            if "s3_key" in kwargs:
                s3_key = kwargs["s3_key"]
        if not s3_key or not question:
            return {"answer": "Input must be in format: <question> --- <s3_key>"}

        loader = SingleFileLoader(s3_key)

        llm = get_model_from_spec(self.spec.llm)
        output_prefix = f"org-files/{self.organization_id}/csv-tool/"
        pipeline = CSVAnswerPipeline(llm, loader, "", output_prefix)

        try:
            answer, context, code = await pipeline.run(question)
        except Exception as e:
            logger.error(e)
            return {"answer": str(e)}

        evidence = [
            Evidence(
                name=s3_key.split("/")[-1],
                evidenceLocation=s3_key,
                origin=EvidenceOrigin.Datastore,
            )
        ]

        # save code in the /code subfolder of the CSV datastore
        code_file_name = f"code_{int(time.time())}.py"
        code_path = os.path.join(s3_key, "code", code_file_name)
        boto3.client("s3").put_object(
            Bucket=S3_BUCKET, Key=code_path, Body=code.encode("utf-8")
        )
        evidence.append(
            Evidence(
                name="code.py",
                evidenceLocation=code_path,
                origin=EvidenceOrigin.Code,
            )
        )

        return {
            "answer": answer,
            "source_documents": [],
            "evidence": evidence,
            "query": question,
        }
