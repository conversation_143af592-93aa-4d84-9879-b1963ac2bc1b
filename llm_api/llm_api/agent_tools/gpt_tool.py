from blai_llm.openai import azure_openai_chat_llm
from blai_llm.utils import PlanLogger, sanitize_tool_name
from langchain_core.messages import HumanMessage
from langchain_core.tools import BaseTool


class BackupTool(BaseTool):
    name = "Backup Tool"
    description = "useful for answering questions no other tool is able to. Is the second best at everything, fro threat intelligence to reporting and summarization"
    plan_id: str = ""

    def __init__(self, plan_id) -> None:
        super().__init__()
        self.name = sanitize_tool_name(self.name)
        self.plan_id = plan_id

    def _run(self, question: str):
        log = PlanLogger()
        log_text = f"Used '{self.name}' with the input '{question}'"
        log.addToLog(log_text, self.plan_id)

        res = azure_openai_chat_llm([HumanMessage(content=question)])
        return res.content

    async def _arun(self, question: str):
        log = PlanLogger()
        log_text = f"Used '{self.name}' with the input '{question}'"
        log.addToLog(log_text, self.plan_id)

        res = azure_openai_chat_llm([HumanMessage(content=question)])
        return res.content
