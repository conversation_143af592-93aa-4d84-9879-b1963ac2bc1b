alienvault_endpoints = [
    {
        "endpoint": "/api/v1/indicators/IPv4/[value]/general",
        "definition": "This endpoint should retrieve information related to the IPv4 indicator specified by the IP address.",
        "arguments": {"IPv4 Address": {"examples": ["*******", "**********"]}},
    },
    {
        "endpoint": "/api/v1/indicators/IPv6/[value]/general",
        "definition": "This endpoint should retrieve information related to the IPv6 indicator specified by the IP address.",
        "arguments": {
            "IPv6 Address": {
                "examples": [
                    "2001:db8:3333:4444:5555:6666:7777:8888",
                    "2001:db8::",
                    "2001:0db8:0001:0000:0000:0ab9:C0A8:0102",
                ]
            }
        },
    },
    {
        "endpoint": "/api/v1/indicators/domain/[value]/general",
        "definition": "This endpoint should retrieve information related to the domain indicator specified by the domain name.",
        "arguments": {"Domain Name": {"examples": ["example.com", "spywaresite.info"]}},
    },
    {
        "endpoint": "/api/v1/indicators/hostname/[value]/general",
        "definition": "This endpoint should retrieve information related to the hostname indicator specified by the hostname.",
        "arguments": {
            "Host Name": {
                "examples": [
                    "otx.alienvault.com",
                    "bad-guys.no-ip.org",
                    "alpha.beta.google.co.uk",
                ]
            }
        },
    },
    {
        "endpoint": "/api/v1/indicators/file/[value]/general",
        "definition": "This endpoint should retrieve information related to the file indicator specified by the file hash.",
        "arguments": {
            "File Hash": {"examples": ["6c5360d41bd2b14b1565f5b18e5c203cf512e493"]}
        },
    },
    {
        "endpoint": "/api/v1/indicators/url/[value]/general",
        "definition": "This endpoint should retrieve information related to the URL indicator specified by the URL.",
        "arguments": {
            "URL": {
                "examples": [
                    "http://www.fotoidea.com/sport/4x4_san_ponso/slides/IMG_0068.html"
                ]
            }
        },
    },
    {
        "endpoint": "/api/v1/indicators/cve/[value]/general",
        "definition": "This endpoint should retrieve information related to the Common Vulnerabilities and Exposures (CVE) indicator specified by the CVE identifier.",
        "arguments": {"CVE": {"examples": ["CVE-2014-0160"]}},
    },
]


nvd_endpoints = [
    {
        "endpoint": "/rest/json/cves/2.0?cpeName=[value]",
        "definition": 'This endpoint should retrieve all Common Vulnerabilities (CVE) associated with a specific CPE identified by its CPE name. A CPE Name is a string of characters comprised of 13 colon separated values that describe a product. In CPEv2.3 the first two values are always \“cpe\” and \“2.3\”. The 11 values that follow are referred to as the CPE components. When filtering by cpeName the part, vendor, product, and version components are REQUIRED to contain values other than "*".',
        "arguments": {
            "CPE Name": {
                "examples": [
                    "cpe:2.3:o:microsoft:windows_10:1607:*:*:*:*:*:*:*",
                    "cpe:2.3:a:microsoft:internet_explorer:8.0.6001:beta:*:*:*:*:*:*",
                ]
            }
        },
    },
    {
        "endpoint": "/rest/json/cves/2.0?cveId=[value]",
        "definition": "This endpoint returns information about a specific vulnerability identified by its unique Common Vulnerabilities and Exposures identifier (the CVE ID).",
        "arguments": {"CVE ID": {"examples": ["CVE-2019-1010218", "CVE-2022-22954"]}},
    },
    {
        "endpoint": "/rest/json/cves/2.0?cvssV2Metrics=[value]",
        "definition": "This endpoint returns only the CVEs that match the provided CVSSv2 vector string. Either full or partial vector strings may be used.",
        "arguments": {
            "CVSS V2 Vector string": {
                "examples": ["AV:N/AC:H/Au:N/C:C/I:C/A:C", "AV:L/AC:H/Au:M/C:N/I:N/A:N"]
            }
        },
    },
    {
        "endpoint": "/rest/json/cves/2.0?cvssV3Metrics=[value]",
        "definition": "This endpoint returns only the CVEs that match the provided CVSS V3 vector string. Either full or partial vector strings may be used.",
        "arguments": {
            "CVSS V3 vector string": {
                "examples": ["AV:L/AC:L/PR:L/UI:R/S:U/C:N/I:L/A:L"]
            }
        },
    },
    {
        "endpoint": "/rest/json/cves/2.0?cweId=[value]",
        "definition": "This endpoint returns only the CVE that include a weakness identified by Common Weakness Enumeration using the provided CWE ID.",
        "arguments": {"CWE ID": {"examples": ["CWE-287", "CWE-89"]}},
    },
    {
        "endpoint": "/rest/json/cves/2.0?keywordSearch=[value]",
        "definition": "This endpoint returns only the CVEs where a word or phrase is found in the current description.",
        "arguments": {"Keywords": {"examples": ["Microsoft", "Debian"]}},
    },
    {
        "endpoint": "/rest/json/cves/2.0/?pubStartDate=[value 1]&pubEndDate=[value 2]",
        "definition": 'This endpoint return only the CVEs that were added to the NVD (i.e., published) during the specified period defaulting to GMT. If filtering by the published date, both pubStartDate and pubEndDate are REQUIRED. The maximum allowable range when using any date range parameters is 120 consecutive days. Values must be entered in the extended ISO-8061 date/time format: [YYYY][“-”][MM][“-”][DD][“T”][HH][“:”][MM][“:”][SS][Z]. The "T" is a literal to separate the date from the time. The Z indicates an optional offset-from-UTC. ',
        "arguments": {
            "Start Date": {
                "examples": ["2021-08-04T00:00:00.000", "2021-10-22T00:00:00.000"]
            },
            "End Date": {
                "examples": ["2021-08-04T00:00:00.000", "2021-10-22T00:00:00.000"]
            },
        },
    },
]

tidal_id_selection_endpoints = [
    {
        "endpoint": "api/v1/tactic/",
        "definition": "This endpoint should list common cyberattack tactics and their IDs.",
        "arguments": {},
    },
    {
        "endpoint": "api/v1/technique/",
        "definition": "This endpoint lists common malicious techniques used by threat actors and their IDs.",
        "arguments": {},
    },
    {
        "endpoint": "api/v1/sectors/",
        "definition": "This endpoint lists information about all sectors and their IDs.",
        "arguments": {},
    },
    {
        "endpoint": "api/v1/product-registry/products/",
        "definition": "This endpoint lists information about all products in the product registry and their IDs.",
        "arguments": {},
    },
    {
        "endpoint": "api/v1/software/",
        "definition": "This endpoint lists information about all softwares and their IDs.",
        "arguments": {},
    },
    {
        "endpoint": "api/v1/groups/",
        "definition": "This endpoint lists information about all threat groups and their IDs.",
        "arguments": {},
    },
]


tidal_data_fetching_endpoints = [
    {
        "endpoint": "api/v1/tactic/[tactic_ID]",
        "definition": "This endpoint returns information about a specific tactic identified by its unique identifier (the tactic ID).",
        "arguments": {
            "tactic_ID": {
                "examples": [
                    "2706dc98-724b-4cf0-84b6-56cc20b0698e",
                    "TA0043",
                    "b17dde68-dbcf-4cfd-9bb8-be014ec65c37",
                    "TA0004",
                ]
            }
        },
    },
    {
        "endpoint": "api/v1/technique/[technique_ID]",
        "definition": "This endpoint returns information about a specific malicious technique identified by its unique identifier (the technique ID).",
        "arguments": {
            "technique_ID": {
                "examples": [
                    "ac7d9875-d18b-48f6-93e6-47c565f9526b",
                    "1423e8c1-7cbf-4cfb-a70d-b6fe8e1a8041",
                    "T1134",
                    "T1548",
                ]
            }
        },
    },
    {
        "endpoint": "api/v1/sectors/[sector_name]/",
        "definition": "This endpoint returns information about a specific sector identified by its name.",
        "arguments": {"sector_name": {"examples": ["aerospace", "agriculture"]}},
    },
    {
        "endpoint": "api/v1/product-registry/products/[product_ID]",
        "definition": "This endpoint returns information about a specific product identified by its unique identifier (the ID)",
        "arguments": {
            "product_ID": {
                "examples": [
                    "21d1f996-7590-4f25-b03d-9e2e31cbdd61",
                    "10a524e4-11ad-55bc-9c58-2ddd4a554491",
                ]
            }
        },
    },
    {
        "endpoint": "api/v1/software/[software_ID]",
        "definition": "This endpoint returns information about a specific software identified by its unique identifier (the ID)",
        "arguments": {
            "software_ID": {
                "examples": [
                    "c4c68b11-321e-43b5-9649-708092b1eceb",
                    "4bcfc5fb-bcd4-4cd1-8769-3607d089be83",
                ]
            }
        },
    },
    {
        "endpoint": "api/v1/groups/[group_ID]",
        "definition": "This endpoint returns information about a specific threat actor group identified by its unique identifier (the ID)",
        "arguments": {
            "group_ID": {
                "examples": [
                    "8567136b-f84a-45ed-8cce-46324c7da60e",
                    "132feaeb-a9a1-4ecc-b7e9-86c008c15218",
                    "G5010",
                ]
            }
        },
    },
]
