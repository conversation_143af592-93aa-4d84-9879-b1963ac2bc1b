import logging
from collections import deque
from typing import Any, Dict, List, Sequence
from uuid import UUID

from langchain_core.agents import AgentA<PERSON>, AgentFinish
from langchain_core.callbacks import Async<PERSON><PERSON>back<PERSON>andler
from langchain_core.documents import Document
from langchain_core.messages import BaseMessage
from langchain_core.outputs import LLMResult
from tenacity import RetryCallState

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


class LoggingCallbackHandler(AsyncCallbackHandler):
    def __init__(self, message_id, **kwargs):
        super().__init__(**kwargs)

        self.message_id = message_id
        self.component_stack = deque()

    def format_msg(self, msg: str, func: str):
        msg = msg.replace("\n", " ")
        return (
            f"{self.message_id} [{' > '.join(list(self.component_stack))}] {func} {msg}"
        )

    async def on_llm_start(
        self,
        serialized: Dict[str, Any],
        prompts: List[str],
        *,
        run_id: UUID,
        parent_run_id: UUID | None = None,
        tags: List[str] | None = None,
        metadata: Dict[str, Any] | None = None,
        **kwargs: Any,
    ) -> None:
        logger.debug(self.format_msg(msg=f"", func="on_llm_start"))

    async def on_chat_model_start(
        self,
        serialized: Dict[str, Any],
        messages: List[List[BaseMessage]],
        *,
        run_id: UUID,
        parent_run_id: UUID | None = None,
        tags: List[str] | None = None,
        metadata: Dict[str, Any] | None = None,
        **kwargs: Any,
    ) -> Any:
        logger.debug(self.format_msg(msg=f"{messages}", func="on_chat_model_start"))

    async def on_llm_end(
        self,
        response: LLMResult,
        *,
        run_id: UUID,
        parent_run_id: UUID | None = None,
        tags: List[str] | None = None,
        **kwargs: Any,
    ) -> None:
        logger.debug(self.format_msg(msg=f"{response}", func="on_llm_end"))

    async def on_llm_error(
        self,
        error: Exception | KeyboardInterrupt,
        *,
        run_id: UUID,
        parent_run_id: UUID | None = None,
        tags: List[str] | None = None,
        **kwargs: Any,
    ) -> None:
        logger.error(self.format_msg(msg=f"{error}", func="on_llm_error"))

    async def on_chain_start(
        self,
        serialized: Dict[str, Any],
        inputs: Dict[str, Any],
        *,
        run_id: UUID,
        parent_run_id: UUID | None = None,
        tags: List[str] | None = None,
        metadata: Dict[str, Any] | None = None,
        **kwargs: Any,
    ) -> None:
        if "constructor" not in serialized["type"]:
            input_text = inputs.get("input")
            input_documents = []
            if inputs.get("input_documents"):
                input_documents = [
                    doc.metadata["source"] for doc in inputs.get("input_documents")
                ]
            logger.info(
                self.format_msg(
                    msg=f"Input: {input_text}; input_documents: {input_documents}",
                    func="on_chain_start",
                )
            )

    async def on_chain_end(
        self,
        outputs: Dict[str, Any],
        *,
        run_id: UUID,
        parent_run_id: UUID | None = None,
        tags: List[str] | None = None,
        **kwargs: Any,
    ) -> None:
        output = outputs.get("output")

        if not outputs.get("intermediate_steps_steps") and not outputs.get("text"):
            logger.info(
                self.format_msg(
                    msg=f"output: {output}",
                    func="on_chain_end",
                )
            )

    async def on_chain_error(
        self,
        error: Exception | KeyboardInterrupt,
        *,
        run_id: UUID,
        parent_run_id: UUID | None = None,
        tags: List[str] | None = None,
        **kwargs: Any,
    ) -> None:
        logger.error(self.format_msg(msg=f"{error}", func="on_chain_error"))

    async def on_tool_start(
        self,
        serialized: Dict[str, Any],
        input_str: str,
        *,
        run_id: UUID,
        parent_run_id: UUID | None = None,
        tags: List[str] | None = None,
        metadata: Dict[str, Any] | None = None,
        **kwargs: Any,
    ) -> None:
        self.component_stack.append(serialized["name"])
        logger.info(self.format_msg(msg=f" input: {input_str}", func="on_tool_start"))

    async def on_tool_end(
        self,
        output: str,
        *,
        run_id: UUID,
        parent_run_id: UUID | None = None,
        tags: List[str] | None = None,
        **kwargs: Any,
    ) -> None:
        logger.info(
            self.format_msg(
                msg="...",
                func="on_tool_end",
            )
        )
        self.component_stack.pop()

    async def on_tool_error(
        self,
        error: Exception | KeyboardInterrupt,
        *,
        run_id: UUID,
        parent_run_id: UUID | None = None,
        tags: List[str] | None = None,
        **kwargs: Any,
    ) -> None:
        logger.error(self.format_msg(msg=f"{error}", func="on_tool_error"))

    async def on_agent_action(
        self,
        action: AgentAction,
        *,
        run_id: UUID,
        parent_run_id: UUID | None = None,
        tags: List[str] | None = None,
        **kwargs: Any,
    ) -> None:
        logger.info(self.format_msg(msg=f"{action}", func="on_agent_action"))

    async def on_agent_finish(
        self,
        finish: AgentFinish,
        *,
        run_id: UUID,
        parent_run_id: UUID | None = None,
        tags: List[str] | None = None,
        **kwargs: Any,
    ) -> None:
        logger.debug(f"{finish}")

    async def on_retry(
        self,
        retry_state: RetryCallState,
        *,
        run_id: UUID,
        parent_run_id: UUID | None = None,
        **kwargs: Any,
    ) -> Any:
        logger.warning(self.format_msg(msg=f"{retry_state}", func="on_retry"))

    async def on_retriever_start(
        self,
        serialized: Dict[str, Any],
        query: str,
        *,
        run_id: UUID,
        parent_run_id: UUID | None = None,
        tags: List[str] | None = None,
        metadata: Dict[str, Any] | None = None,
        **kwargs: Any,
    ) -> None:
        self.component_stack.append(serialized["id"][-1])
        logger.info(self.format_msg(msg=f"query: {query}", func="on_retriever_start"))

    async def on_retriever_end(
        self,
        documents: Sequence[Document],
        *,
        run_id: UUID,
        parent_run_id: UUID | None = None,
        tags: List[str] | None = None,
        **kwargs: Any,
    ) -> None:
        urls_and_scores = [
            (doc.metadata["source"], doc.metadata["score"]) for doc in documents
        ]
        logger.info(
            self.format_msg(
                msg=f"Got {len(documents)} from retriever: {urls_and_scores}",
                func="on_retriever_end",
            )
        )
        self.component_stack.pop()

    async def on_retriever_error(
        self,
        error: Exception | KeyboardInterrupt,
        *,
        run_id: UUID,
        parent_run_id: UUID | None = None,
        tags: List[str] | None = None,
        **kwargs: Any,
    ) -> None:
        logger.error(self.format_msg(msg=f"{error}", func="on_retriever_error"))
