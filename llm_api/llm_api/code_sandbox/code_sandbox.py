import io
import logging
import os
import tarfile
import tempfile
import threading
import time
import uuid
from dataclasses import dataclass
from queue import Queue
from typing import Dict, Optional

import docker
from docker.errors import APIError, ImageNotFound
from docker.tls import TLSConfig

_pool = None
_lock = threading.RLock()

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


def _get_docker_client():
    from docker import utils

    ca = "/code/docker-certs/ca.pem"
    cert = "/code/docker-certs/client-cert.pem"
    key = "/code/docker-certs/client-key.pem"

    params = utils.kwargs_from_env()
    if all(os.path.exists(p) for p in (ca, cert, key)):
        params["tls"] = TLSConfig(client_cert=(cert, key), ca_cert=ca, verify=True)

    return docker.DockerClient(**params)


SOCKET_HOST_PATH = "/var/run/llm.sock"
SOCKET_CONT_PATH = "/run/llm.sock"


def _tar_bytes(dest_path: str, content: bytes) -> bytes:
    rel_path = dest_path.lstrip("/")
    buf = io.BytesIO()
    with tarfile.open(fileobj=buf, mode="w") as tar:
        info = tarfile.TarInfo(name=rel_path)
        info.size = len(content)
        tar.addfile(info, io.BytesIO(content))
    buf.seek(0)
    return buf.read()


@dataclass
class ExecResult:
    returncode: int
    stdout: str
    stderr: str = ""


class _CodeRunnerPool:
    def __init__(self, sandbox_image: str, pool_size: int):
        self.client = _get_docker_client()
        self.sandbox_image = sandbox_image
        self.pool_size = pool_size
        self.sanitized = sandbox_image.replace("/", "_").replace(":", "_")

        self._ensure_image()
        self._init_pool()

    def _ensure_image(self):
        try:
            self.image = self.client.images.get(self.sandbox_image)
        except ImageNotFound:
            self.image = self.client.images.pull(self.sandbox_image)
        self.image_id = self.image.id

    def _list_existing(self):
        return [
            c
            for c in self.client.containers.list(all=True)
            if c.name.startswith(f"code-runner-{self.sanitized}-")
        ]

    def _create_container(self):
        name = f"code-runner-{self.sanitized}-{uuid.uuid4()}"
        container = self.client.containers.run(
            self.image.id,
            name=name,
            command=["python", "-c", "import signal; signal.pause()"],
            detach=True,
            network_mode="none",
            volumes={SOCKET_HOST_PATH: {"bind": SOCKET_CONT_PATH, "mode": "ro"}},
            user="1000:1000",
            security_opt=["no-new-privileges"],
            pids_limit=128,
            mem_limit="512m",
            nano_cpus=500_000_000,
            labels={"runner": self.sanitized},
        )
        return container

    def _refresh_container(self, container):
        try:
            container.reload()
            if container.status != "running" or container.image.id != self.image_id:
                raise RuntimeError("stale")
        except Exception:
            try:
                container.remove(force=True)
            finally:
                return self._create_container()
        return container

    def _init_pool(self):
        self._containers = []
        self._available = Queue()
        for c in self._list_existing():
            c = self._refresh_container(c)
            self._containers.append(c)
            self._available.put(c)
        while len(self._containers) < self.pool_size:
            c = self._create_container()
            self._containers.append(c)
            self._available.put(c)

    def _ensure_dir(self, container, path: str):
        dir_path = os.path.dirname(path)
        if dir_path and dir_path != "/":
            container.exec_run(["mkdir", "-p", dir_path], stdout=False, stderr=False)

    def _put_file(self, container, dest_path: str, content: bytes):
        self._ensure_dir(container, dest_path)
        container.put_archive("/", _tar_bytes(dest_path, content))

    def _remove_file(self, container, path: str):
        try:
            container.exec_run(["rm", "-f", path], stdout=False, stderr=False)
        except Exception:
            pass

    def execute_code(self, code: str, files: Optional[Dict[str, bytes]] = None):
        container = self._available.get(timeout=30)
        written = []
        try:
            container = self._refresh_container(container)

            script_path = "/tmp/script.py"
            self._put_file(container, script_path, code.encode())
            if files:
                for fp, content in files.items():
                    data = content if isinstance(content, bytes) else content.encode()
                    self._put_file(container, fp, data)
                    written.append(fp)

            exec_run = container.exec_run(["python3", script_path], demux=True)
            stdout_b, stderr_b = exec_run.output or (b"", b"")
            stdout = (stdout_b or b"").decode()
            stderr = (stderr_b or b"").decode()
            if not stderr and exec_run.exit_code != 0:
                stderr = stdout
            return ExecResult(exec_run.exit_code, stdout, stderr)
        finally:
            self._remove_file(container, script_path)
            for p in written:
                self._remove_file(container, p)
            self._available.put(container)

    def cleanup(self):
        while not self._available.empty():
            self._available.get_nowait()
        for c in list(self._containers):
            try:
                c.remove(force=True)
            except Exception:
                pass
        self._containers.clear()


def init_code_runner_pool(
    sandbox_image: str = None, pool_size: int = 3, retry: bool = True
):
    global _pool
    try:
        with _lock:
            if _pool:
                for c in list(_pool._containers):
                    _pool._refresh_container(c)
            else:
                sandbox_image = sandbox_image or os.environ.get(
                    "SANDBOX_IMAGE", "bricklayerai/python_sandbox:1.0"
                )
                _pool = _CodeRunnerPool(sandbox_image, pool_size)
    except Exception as e:
        logger.debug(f"Error initializing pool: {e}")
        if retry:
            time.sleep(5)
            init_code_runner_pool(sandbox_image, pool_size, retry=False)


def execute_code(code: str, files: Optional[Dict[str, bytes]] = None) -> ExecResult:
    global _pool
    attempt = 0
    while attempt < 2:
        with _lock:
            if _pool is None:
                init_code_runner_pool()
                if _pool is None:
                    raise RuntimeError("Unable to initialize code runner pool.")
            try:
                return _pool.execute_code(code, files)
            except APIError as e:
                logger.debug(f"Docker error: {e}. Reinitializing pool and retrying.")
                cleanup_code_runner_pool()
                init_code_runner_pool()
                attempt += 1
            except Exception:
                raise
    raise RuntimeError("Execution failed after retrying.")


def cleanup_code_runner_pool():
    global _pool
    with _lock:
        if _pool:
            _pool.cleanup()
            _pool = None


if os.environ.get("FF_CSV_DATASTORES_ENABLED", "False").lower() == "true":
    init_code_runner_pool(
        sandbox_image=os.environ.get(
            "SANDBOX_IMAGE", "bricklayerai/python_sandbox:1.0"
        ),
        pool_size=3,
    )
