import tiktoken


class LLMPromptTruncationMixin:
    """
    Mixin class to handle token truncation for LLM prompts.
    This class provides methods to truncate text based on token limits.

    We can expand to more than context in the future.
    """

    def truncate_tokens(self, text: str, max_tokens: int) -> str:
        """
        Truncate the text to fit within the specified token limit.
        """
        encoding = tiktoken.get_encoding("cl100k_base")
        tokens = encoding.encode(text)

        if len(tokens) > max_tokens:
            tokens = tokens[:max_tokens]

        return encoding.decode(tokens)

    def truncate_context(self, text: str) -> str:
        return self.truncate_tokens(text, 128_000)  # 4o-mini context tokens upper limit
