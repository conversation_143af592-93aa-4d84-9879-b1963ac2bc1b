import json
import logging
import os
from datetime import datetime

from llm_api.retrievers.opensearch_retriever import build_query_dict

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


def get_test_cases():
    size_value = int(os.environ.get("LLM__OPENSEARCH__MAXQUERYRESULTS", "4"))
    named_entity_boost = float(os.environ.get("NAMED_ENTITY_BOOST", "3"))

    def phrase(word):
        return {"match_phrase": {"content": word}}

    def boosted_phrase(word):
        return {
            "match_phrase": {"content": {"query": word, "boost": named_entity_boost}}
        }

    def date_filter(date_keywords):
        if isinstance(date_keywords, str):
            filters = [{"term": {"published_date": date_keywords}}]
        elif isinstance(date_keywords, tuple):
            filters = [
                {
                    "range": {
                        "published_date": {
                            "gte": date_keywords[0],
                            "lte": date_keywords[1],
                        }
                    }
                }
            ]
        elif isinstance(date_keywords, list):
            filters = []
            for item in date_keywords:
                if isinstance(item, str):
                    filters.append({"term": {"published_date": item}})
                elif isinstance(item, tuple):
                    filters.append(
                        {"range": {"published_date": {"gte": item[0], "lte": item[1]}}}
                    )
        else:
            raise ValueError("Invalid date_keywords format")

        return {"bool": {"should": filters, "minimum_should_match": 1}}

    sort_by_date_filter = [
        {"_score": {"order": "desc"}},
        {"published_date": {"order": "desc"}},
    ]
    dont_sort_by_date_filter = [{"_score": {"order": "desc"}}]

    return [
        {
            "input": {
                "query": "ransomware",
                "filter_by_date": False,
                "disabled_feed_ids": [],
            },
            "expected_output": {
                "query": {
                    "bool": {
                        "should": [phrase("ransomware")],
                    }
                },
                "sort": dont_sort_by_date_filter,
                "size": size_value,
            },
        },
        {
            "input": {
                "query": "phishing,2023-03-01:2023-03-10",
                "filter_by_date": True,
                "disabled_feed_ids": [101, 102],
            },
            "expected_output": {
                "query": {
                    "bool": {
                        "should": [phrase("phishing")],
                        "filter": date_filter([("2023-03-01", "2023-03-10")]),
                        "must_not": [{"terms": {"feed_id": ["101", "102"]}}],
                    }
                },
                "sort": sort_by_date_filter,
                "size": size_value,
            },
        },
        {
            "input": {
                "query": "malware, 2023-01-15,",
                "filter_by_date": True,
                "disabled_feed_ids": [],
            },
            "expected_output": {
                "query": {
                    "bool": {
                        "should": [phrase("malware")],
                        "filter": date_filter("2023-01-15"),
                    }
                },
                "sort": sort_by_date_filter,
                "size": size_value,
            },
        },
        {
            "input": {
                "query": "phishing, 2023-05-20, spam",
                "filter_by_date": True,
                "disabled_feed_ids": [],
            },
            "expected_output": {
                "query": {
                    "bool": {
                        "should": [phrase("phishing"), phrase("spam")],
                        "filter": date_filter("2023-05-20"),
                    }
                },
                "sort": sort_by_date_filter,
                "size": size_value,
            },
        },
        {
            "input": {
                "query": "update, 2023-03-03:2023-03-04",
                "filter_by_date": True,
                "disabled_feed_ids": [301, 302],
            },
            "expected_output": {
                "query": {
                    "bool": {
                        "should": [phrase("update")],
                        "filter": date_filter([("2023-03-03", "2023-03-04")]),
                        "must_not": [{"terms": {"feed_id": ["301", "302"]}}],
                    }
                },
                "sort": sort_by_date_filter,
                "size": size_value,
            },
        },
        {
            "input": {
                "query": "breach, 2023-07-15",
                "filter_by_date": True,
                "disabled_feed_ids": [],
            },
            "expected_output": {
                "query": {
                    "bool": {
                        "should": [phrase("breach")],
                        "filter": date_filter("2023-07-15"),
                    }
                },
                "sort": sort_by_date_filter,
                "size": size_value,
            },
        },
        {
            "input": {
                "query": "2023-08-01:2023-08-02, patch",
                "filter_by_date": True,
                "disabled_feed_ids": [],
            },
            "expected_output": {
                "query": {
                    "bool": {
                        "should": [phrase("patch")],
                        "filter": date_filter([("2023-08-01", "2023-08-02")]),
                    }
                },
                "sort": sort_by_date_filter,
                "size": size_value,
            },
        },
        {
            "input": {
                "query": "vulnerabilities, 2023-09-01:2023-09-04",
                "filter_by_date": True,
                "disabled_feed_ids": [],
            },
            "expected_output": {
                "query": {
                    "bool": {
                        "should": [phrase("vulnerabilities")],
                        "filter": date_filter([("2023-09-01", "2023-09-04")]),
                    }
                },
                "sort": sort_by_date_filter,
                "size": size_value,
            },
        },
        {
            "input": {
                "query": "indicators of compromise, 2023-06-01",
                "filter_by_date": True,
                "disabled_feed_ids": [],
            },
            "expected_output": {
                "query": {
                    "bool": {
                        "should": [phrase("indicators of compromise")],
                        "filter": date_filter("2023-06-01"),
                    }
                },
                "sort": sort_by_date_filter,
                "size": size_value,
            },
        },
        {
            "input": {
                "query": "firewall, 2023-02-01:2023-02-10",
                "filter_by_date": True,
                "disabled_feed_ids": [],
            },
            "expected_output": {
                "query": {
                    "bool": {
                        "should": [phrase("firewall")],
                        "filter": date_filter([("2023-02-01", "2023-02-10")]),
                    }
                },
                "sort": sort_by_date_filter,
                "size": size_value,
            },
        },
        {
            "input": {
                "query": "exploit, 2023-03-01:2023-03-03, 2023-03-10:2023-03-12",
                "filter_by_date": True,
                "disabled_feed_ids": [],
            },
            "expected_output": {
                "query": {
                    "bool": {
                        "should": [phrase("exploit")],
                        "filter": date_filter(
                            [("2023-03-01", "2023-03-03"), ("2023-03-10", "2023-03-12")]
                        ),
                    }
                },
                "sort": sort_by_date_filter,
                "size": size_value,
            },
        },
        {
            "input": {
                "query": "+OpenAI, security",
                "filter_by_date": False,
                "disabled_feed_ids": [],
            },
            "expected_output": {
                "query": {
                    "bool": {
                        "should": [boosted_phrase("OpenAI"), phrase("security")],
                    }
                },
                "sort": dont_sort_by_date_filter,
                "size": size_value,
            },
        },
        {
            "input": {
                "query": "+Tesla, +SpaceX, breach",
                "filter_by_date": False,
                "disabled_feed_ids": [],
            },
            "expected_output": {
                "query": {
                    "bool": {
                        "should": [
                            boosted_phrase("Tesla"),
                            boosted_phrase("SpaceX"),
                            phrase("breach"),
                        ],
                    }
                },
                "sort": dont_sort_by_date_filter,
                "size": size_value,
            },
        },
        {
            "input": {
                "query": "+Meta, 2023-10-01:2023-10-07, AI",
                "filter_by_date": True,
                "disabled_feed_ids": [],
            },
            "expected_output": {
                "query": {
                    "bool": {
                        "should": [
                            boosted_phrase("Meta"),
                            phrase("AI"),
                        ],
                        "filter": date_filter(("2023-10-01", "2023-10-07")),
                    }
                },
                "sort": sort_by_date_filter,
                "size": size_value,
            },
        },
    ]


def test_build_query_dict():
    test_cases = get_test_cases()
    results = []
    for idx, test in enumerate(test_cases, start=1):
        input_data = test["input"]
        expected = test["expected_output"]
        try:
            result = build_query_dict(
                input_data["query"],
                input_data["filter_by_date"],
                input_data["disabled_feed_ids"],
            )
        except Exception as e:
            logger.error(f"Error in case {idx}: {e}")
            import traceback

            logger.error(traceback.format_exc())
            result = {"error": str(e)}
        outcome = "✅" if result == expected else "❌"
        results.append(
            {
                "description": f"Case {idx}: Input query '{input_data['query']}'",
                "result": outcome,
                "output": result,
                "expected": expected,
            }
        )
    return {"results": results}
