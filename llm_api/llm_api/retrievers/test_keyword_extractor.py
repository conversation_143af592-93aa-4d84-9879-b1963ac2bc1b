"""
Improved tester for the keyword extractor.

Key points
----------
1. Case-insensitive matching.
2. A hit is valid if the term is in either `keywords` or `top_keywords`.
3. On failure we print only the missing terms and the raw JSON from the LLM.
4. Each test line shows its runtime in ms.
"""

import json
import logging
import textwrap
import time
from typing import Any, Dict, List, Set

from langchain_core.messages import SystemMessage

from llm_api.llm.factory import (
    default_4_gpt_spec_data_json_enabled,
    get_model_from_spec,
)
from llm_api.retrievers import keyword_extractor as kwx
from llm_api.specs.llm_spec import LLMSpec, LLMType

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


# ───────────────────────────── test cases ──────────────────────────────
def get_test_cases() -> List[Dict[str, Any]]:
    return [
        # baseline 10
        {
            "query": "Latest news on cloud security breaches",
            "expected": ["cloud", "security", "breaches", "news"],
        },
        {
            "query": "Recent ransomware attacks targeting hospitals",
            "expected": ["ransomware", "attacks", "hospitals"],
        },
        {
            "query": "Kubernetes CVEs disclosed in 2025",
            "expected": ["Kubernetes", "CVEs", "disclosed"],
        },
        {
            "query": "Zero-day exploit affecting FortiGate firewalls",
            "expected": ["Zero-day", "exploit", "FortiGate", "firewalls"],
        },
        {
            "query": "Phishing trends in the finance sector",
            "expected": ["phishing", "trends", "finance", "sector"],
        },
        {
            "query": "Supply chain attack on SolarWinds revisited",
            "expected": ["Supply chain", "attack", "SolarWinds"],
        },
        {
            "query": "APT28 phishing campaigns uncovered",
            "expected": ["APT28", "phishing", "campaigns", "uncovered"],
        },
        {
            "query": "Critical vulnerability in OpenSSL library",
            "expected": ["vulnerability", "OpenSSL", "library"],
        },
        {
            "query": "Insider threats: data leaks by employees",
            "expected": ["Insider threats", "data leaks", "employees"],
        },
        {
            "query": "Security patch released for Log4j",
            "expected": ["Security", "patch", "released", "Log4j"],
        },
        # 15 harder cases
        {
            "query": "Any zero-day privilege-escalation exploits in Windows Server?",
            "expected": [
                "zero-day",
                "privilege-escalation",
                "Windows Server",
                "exploits",
            ],
        },
        {
            "query": "Updates on advanced persistent threat groups this quarter",
            "expected": ["advanced persistent threat", "groups", "updates"],
        },
        {
            "query": "Supply-chain compromises detected across Asia-Pacific CISOs",
            "expected": [
                "supply-chain",
                "compromises",
                "Asia-Pacific",
                "CISOs",
                "detected",
            ],
        },
        {
            "query": "Is new malware abusing F5 BIG-IP auth bypass CVEs?",
            "expected": ["malware", "F5 BIG-IP", "auth bypass", "CVEs", "abusing"],
        },
        {
            "query": "Apache Struts remote code execution — any fresh CVE advisories?",
            "expected": ["Apache Struts", "remote code execution", "CVE", "advisories"],
        },
        {
            "query": "Credential-stuffing attacks against Spotify Premium users",
            "expected": ["credential-stuffing", "attacks", "Spotify Premium", "users"],
        },
        {
            "query": "Emerging fileless-ransomware techniques in healthcare",
            "expected": ["fileless-ransomware", "techniques", "healthcare", "emerging"],
        },
        {
            "query": "What did CISA publish on water-sector cyber resilience?",
            "expected": ["CISA", "water-sector", "cyber resilience", "publish"],
        },
        {
            "query": "Intel on dark-web markets selling MFA-bypass kits",
            "expected": ["dark-web", "markets", "MFA-bypass", "kits"],
        },
        {
            "query": "Recent BEC scams spoofing CFO emails at tech startups",
            "expected": ["BEC", "scams", "spoofing", "CFO", "emails", "tech startups"],
        },
        {
            "query": "Brute-force SSH hits from IP ************** in March logs",
            "expected": [
                "brute-force",
                "SSH",
                "**************",
                "hits",
                "logs",
                "March",
            ],
        },
        {
            "query": "Any container-escape bugs disclosed for Kubernetes clusters?",
            "expected": [
                "container-escape",
                "bugs",
                "Kubernetes clusters",
                "disclosed",
            ],
        },
        {
            "query": "News about quantum-resistant encryption in banking apps",
            "expected": ["quantum-resistant encryption", "banking apps", "news"],
        },
        {
            "query": "ALPHV/BlackCat affiliate activity targeting VMware ESXi",
            "expected": [
                "ALPHV",
                "BlackCat",
                "affiliate activity",
                "VMware ESXi",
                "targeting",
            ],
        },
        {
            "query": "Is LockBit using CLR-loader payload delivery now?",
            "expected": ["LockBit", "CLR-loader", "payload delivery", "using"],
        },
    ]


# ─────────────── helpers for plural / singular comparison ──────────────
def _plural_variants(term: str) -> List[str]:
    """Return the most common singular ↔ plural forms of a term."""
    t = term.lower()
    variants = {t}

    # plural → singular
    if t.endswith("ies") and len(t) > 4:  # policies → policy
        variants.add(t[:-3] + "y")
    elif t.endswith("es") and len(t) > 3 and not t.endswith(("ses", "xes")):
        variants.add(t[:-2])  # breaches → breach
    elif t.endswith("s") and len(t) > 3 and not t.endswith(("ss", "us")):
        variants.add(t[:-1])  # threats → threat
    else:
        # singular → plural (very naive rules)
        if t.endswith("y") and len(t) > 3:
            variants.add(t[:-1] + "ies")  # policy → policies
        elif t.endswith(("ch", "sh", "x", "s", "z")):
            variants.add(t + "es")  # patch → patches
        else:
            variants.add(t + "s")  # breach → breaches

    return list(variants)


def _is_match(expected: str, hits: Set[str]) -> bool:
    """True if expected (or its simple plural/singular form) is in hits."""
    for variant in _plural_variants(expected):
        if variant in hits:
            return True
    return False


# ─────────────────────────── test runner ───────────────────────────────
def run_keyword_extractor_tests() -> List[Dict[str, Any]]:
    llm = get_model_from_spec(
        LLMSpec(type=LLMType.AzureChatOpenAI, data=default_4_gpt_spec_data_json_enabled)
    )

    summary: List[Dict[str, Any]] = []

    for idx, case in enumerate(get_test_cases(), 1):
        query, expected = case["query"], case["expected"]
        start = time.perf_counter()

        try:
            keywords, top_keywords, raw_json = kwx._extract_keywords(llm, query)
            dur_ms = (time.perf_counter() - start) * 1000

            hits = {k.lower() for k in keywords + top_keywords}
            missing = [kw for kw in expected if not _is_match(kw, hits)]

            if missing:
                print(f"\n--- Test {idx} ❌ FAILED ({dur_ms:.0f} ms) ---")
                print(f"Query   : {query}")
                print(f"Missing : {missing}")
                print("Raw JSON:")
                print(
                    textwrap.indent(
                        raw_json[:500] + ("…" if len(raw_json) > 500 else ""), "  "
                    )
                )
                result = "❌ FAILED"
            else:
                print(f"\n--- Test {idx} ✅ PASSED ({dur_ms:.0f} ms) ---")
                print(f"Query   : {query}")
                result = "✅ PASSED"

            print(f"Keywords    : {keywords}")
            print(f"Top keywords: {top_keywords}\n")

            summary.append(
                {
                    "case": idx,
                    "query": query,
                    "expected": expected,
                    "keywords": keywords,
                    "top_keywords": top_keywords,
                    "missing": missing,
                    "duration_ms": round(dur_ms, 2),
                    "result": result,
                }
            )

        except Exception as exc:
            dur_ms = (time.perf_counter() - start) * 1000
            logger.error("Exception on test %d: %s", idx, exc)
            print(f"\n--- Test {idx} ❌ EXCEPTION ({dur_ms:.0f} ms) ---")
            print(f"Query : {query}\nError : {exc}\n")
            summary.append(
                {
                    "case": idx,
                    "query": query,
                    "expected": expected,
                    "keywords": [],
                    "top_keywords": [],
                    "missing": expected,
                    "duration_ms": round(dur_ms, 2),
                    "result": "❌ EXCEPTION",
                    "error": str(exc),
                }
            )

            import traceback

            traceback.print_exc()

    return summary


if __name__ == "__main__":
    print(json.dumps(run_keyword_extractor_tests(), indent=2))


if __name__ == "__main__":
    print(json.dumps(run_keyword_extractor_tests(), indent=2))
