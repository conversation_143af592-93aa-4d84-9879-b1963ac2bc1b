import logging
import time
from datetime import date, datetime, timedelta

from dateutil.relativedelta import relativedelta

from llm_api.llm.factory import (
    default_4_gpt_spec_data_json_enabled,
    get_model_from_spec,
)
from llm_api.retrievers.date_extractor import compute_date_keywords
from llm_api.specs.llm_spec import LLMSpec, LLMType

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


def format_date(dt):
    return dt.strftime("%Y-%m-%d")


def get_range(days):
    today = datetime.today().date()
    return f"{format_date(today - timedelta(days=days))}:{format_date(today)}"


def get_month_range(month_offset=0):
    today = datetime.today().date()
    target = today.replace(day=1) + relativedelta(months=month_offset)
    start = target
    end = (start + relativedelta(months=1)) - timedelta(days=1)
    return f"{format_date(start)}:{format_date(end)}"


def get_fixed_month_range(month: int, year: int):
    start = datetime(year, month, 1).date()
    end = (start + relativedelta(months=1)) - timedelta(days=1)
    return f"{format_date(start)}:{format_date(end)}"


def get_test_cases():
    """
    deterministic tests:
    • queries with a time-reference and an exact expected date or range
    • 5 decoy queries that MUST return no dates (empty list)
    """
    today = datetime.today().date()
    fmt = lambda d: d.strftime("%Y-%m-%d")
    year_range = lambda y: f"{y}-01-01:{y}-12-31"
    quarter_range = lambda y, q: (
        f"{y}-{(q-1)*3+1:02d}-01:"
        f"{y}-{(q*3):02d}-{(date(y, q*3, 1)+relativedelta(months=1)-timedelta(days=1)).day:02d}"
    )
    week_range = lambda y, w: (lambda s: f"{fmt(s)}:{fmt(s+timedelta(days=6))}")(
        datetime.strptime(f"{y}-W{w:02d}-1", "%G-W%V-%u").date()
    )

    deterministic = [
        # --- rolling day/week/month ------------------------------------------
        {"query": "What's the news today?", "expected_dates": [fmt(today)]},
        {
            "query": "Cybersecurity news yesterday?",
            "expected_dates": [fmt(today - timedelta(days=1))],
        },
        {
            "query": "News from the day before yesterday?",
            "expected_dates": [fmt(today - timedelta(days=2))],
        },
        {
            "query": "Any breaches this week?",
            "expected_dates": [
                f"{fmt(today - timedelta(days=today.weekday()))}:{fmt(today)}"
            ],
        },
        {"query": "Cyber events last week?", "expected_dates": [get_range(7)]},
        {"query": "Highlights this month?", "expected_dates": [get_month_range(0)]},
        {"query": "Incidents last month?", "expected_dates": [get_month_range(-1)]},
        {"query": "Any breaches past 30 days?", "expected_dates": [get_range(30)]},
        {"query": "Findings in the last 60 days?", "expected_dates": [get_range(60)]},
        {"query": "Problems in the past 90 days?", "expected_dates": [get_range(90)]},
        # --- fixed calendar months ------------------------------------------
        {
            "query": "Cybersecurity news in January 2022",
            "expected_dates": [get_fixed_month_range(1, 2022)],
        },
        {
            "query": "What happened in May 2023?",
            "expected_dates": [get_fixed_month_range(5, 2023)],
        },
        {
            "query": "News in July 2021?",
            "expected_dates": [get_fixed_month_range(7, 2021)],
        },
        {
            "query": "Reports from August 2022?",
            "expected_dates": [get_fixed_month_range(8, 2022)],
        },
        {
            "query": "Alerts in September 2023?",
            "expected_dates": [get_fixed_month_range(9, 2023)],
        },
        {
            "query": "Updates in October 2024?",
            "expected_dates": [get_fixed_month_range(10, 2024)],
        },
        # --- News recent - implicitly last 7 days ---------------------------
        {"query": "What's new with AI?", "expected_dates": [get_range(7)]},
        {"query": "Latest news on cloud security?", "expected_dates": [get_range(7)]},
        {"query": "Any Kubernetes news lately?", "expected_dates": [get_range(7)]},
        # --- multiple specific months ---------------------------------------
        {
            "query": "Vulnerabilities disclosed in March and April 2023",
            "expected_dates": [
                get_fixed_month_range(3, 2023),
                get_fixed_month_range(4, 2023),
            ],
        },
        # --- fixed single days ----------------------------------------------
        {
            "query": "Any incidents reported on 2022-11-03?",
            "expected_dates": ["2022-11-03"],
        },
        {
            "query": "What happened on February 29 2020?",
            "expected_dates": ["2020-02-29"],
        },
        {"query": "What happened on March 15, 2024?", "expected_dates": ["2024-03-15"]},
        # --- calendar years --------------------------------------------------
        {"query": "Events in 2021?", "expected_dates": [year_range(2021)]},
        {"query": "Incidents reported in 2022?", "expected_dates": [year_range(2022)]},
        {"query": "Alerts in 2024?", "expected_dates": [year_range(2024)]},
        {"query": "Breaches logged in 2015?", "expected_dates": [year_range(2015)]},
        # --- quarters --------------------------------------------------------
        {
            "query": "Issues in the second quarter of 2024?",
            "expected_dates": [quarter_range(2024, 2)],
        },
        {"query": "Attacks in Q3 2023?", "expected_dates": [quarter_range(2023, 3)]},
        {"query": "Results for Q4 2022?", "expected_dates": [quarter_range(2022, 4)]},
        # --- ISO week --------------------------------------------------------
        {
            "query": "Incidents during week 42 of 2023?",
            "expected_dates": [week_range(2023, 42)],
        },
        # --- year-to-date / last-year ---------------------------------------
        {"query": "Any risks this year?", "expected_dates": [year_range(today.year)]},
        {"query": "Threats last year?", "expected_dates": [year_range(today.year - 1)]},
        # --- fiscal shorthand / FY ------------------------------------------
        {"query": "Any issues in FY22?", "expected_dates": [year_range(2022)]},
        # --- holiday ---------------------------------------------------------
        {"query": "Breaches on Christmas 2024?", "expected_dates": ["2024-12-25"]},
        # --- since / before --------------------------------------------------
        {
            "query": "Events since the start of 2020?",
            "expected_dates": [f"2020-01-01:{format_date(today)}"],
        },
        {
            "query": "Changes after mid-2023?",
            "expected_dates": [f"2023-07-01:{format_date(today)}"],
        },
        {
            "query": "Incidents before 2019?",
            "expected_dates": [f"0001-01-01:2018-12-31"],
        },
        # --- explicit ranges -------------------------------------------------
        {
            "query": "Events between 2023-01-01 and 2023-06-30?",
            "expected_dates": ["2023-01-01:2023-06-30"],
        },
        {
            "query": "Issues from 15 May 2023 to 20 May 2023?",
            "expected_dates": ["2023-05-15:2023-05-20"],
        },
        # --- rolling n-day windows ------------------------------------------
        {
            "query": "Attacks during the last 14 days?",
            "expected_dates": [get_range(14)],
        },
        {
            "query": "Vulnerabilities found in the previous 10 days?",
            "expected_dates": [get_range(10)],
        },
        # --- rolling fortnight ----------------------------------------------
        {
            "query": "Problems during the previous fortnight?",
            "expected_dates": [get_range(14)],
        },
        {"query": "News from the past fortnight?", "expected_dates": [get_range(14)]},
        # --- decade range ----------------------------------------------------
        {
            "query": "Incidents within the past decade?",
            "expected_dates": [get_range(3650)],
        },
    ]

    # --- 5 decoys (no date expected) ----------------------------------------
    decoys = [
        "May I see the notes from the last meeting?",
        "We need to march forward with this plan.",
        "The product version is 2.023.15 — any feedback?",
        "Can you spot eleven issues in this code?",
        "Our goal is to double revenue by Q-up, not a real quarter.",
    ]
    decoy_dicts = [{"query": q, "expected_dates": []} for q in decoys]

    tests = deterministic + decoy_dicts
    return tests


def get_news_test_cases():
    """
    targeted tests for day-level and week-level references only
    """
    today = datetime.today().date()
    yesterday = today - timedelta(days=1)
    fmt = lambda d: d.strftime("%Y-%m-%d")

    return [
        {"query": "What's the news today?", "expected_dates": [fmt(today)]},
        {"query": "Any cyber news today?", "expected_dates": [fmt(today)]},
        {"query": "What’s the news in security today?", "expected_dates": [fmt(today)]},
        {"query": "Security news for today?", "expected_dates": [fmt(today)]},
        {
            "query": "Any recent cybersecurity-related news today?",
            "expected_dates": [fmt(today)],
        },
        {
            "query": "Cybersecurity news yesterday?",
            "expected_dates": [fmt(yesterday)],
        },
        {
            "query": "What happened in cyber yesterday?",
            "expected_dates": [fmt(yesterday)],
        },
        {
            "query": "Any cyber updates from yesterday?",
            "expected_dates": [fmt(yesterday)],
        },
        {
            "query": "Security-related news yesterday?",
            "expected_dates": [fmt(yesterday)],
        },
        {
            "query": "Any security incidents reported yesterday?",
            "expected_dates": [fmt(yesterday)],
        },
        {
            "query": "Cybersecurity news last week?",
            "expected_dates": [get_range(7)],
        },
    ]


def run_date_keyword_tests():
    llm_spec = LLMSpec(
        type=LLMType.AzureChatOpenAI, data=default_4_gpt_spec_data_json_enabled
    )
    llm = get_model_from_spec(llm_spec)

    test_cases = get_news_test_cases()  # get_test_cases()
    results = []

    for i, test in enumerate(test_cases, 1):
        query = test["query"]
        expected_dates = set(test["expected_dates"])

        logger.info(f"Running test case {i}/{len(test_cases)}: {query}")
        start_time = time.perf_counter()

        try:
            extracted_dates = set(compute_date_keywords(llm, query))
            logger.debug(f"Extracted dates: {extracted_dates}")
            passed = expected_dates == extracted_dates
            if not passed:
                logger.debug(f"Expected: {sorted(expected_dates)}")
                logger.debug(f"Got: {sorted(extracted_dates)}")
            error_msg = ""
        except Exception as e:
            duration_ms = (time.perf_counter() - start_time) * 1000
            logger.error(f"Exception on test case {i}: {e} (took {duration_ms:.2f} ms)")
            results.append(
                {
                    "case": i,
                    "query": query,
                    "expected": sorted(expected_dates),
                    "extracted": [],
                    "error": str(e),
                    "duration_ms": round(duration_ms, 2),
                    "result": "❌ EXCEPTION",
                }
            )
            continue

        duration_ms = (time.perf_counter() - start_time) * 1000
        result_mark = "✅" if passed else "❌"

        logger.info(f"Result for case {i}: {result_mark} (took {duration_ms:.2f} ms)")

        results.append(
            {
                "case": i,
                "query": query,
                "expected": sorted(expected_dates),
                "extracted": sorted(extracted_dates),
                "duration_ms": round(duration_ms, 2),
                "result": result_mark,
                "error": error_msg,
            }
        )

    logger.info("All tests completed.")
    return results


if __name__ == "__main__":
    test_results = run_date_keyword_tests()
    for r in test_results:
        print(r)
