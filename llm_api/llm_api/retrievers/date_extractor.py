import calendar
import datetime
import json
import logging
from typing import Any, Dict, List, Optional

import pytz
from dateutil.relativedelta import relativedelta
from langchain_core.language_models import BaseChatModel
from langchain_core.messages import SystemMessage

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


# ───────────────────────── extract_date_references ─────────────────────────
def extract_date_references(
    llm: BaseChatModel,
    query: str,
    local_tz: str = "UTC",
) -> List[Dict[str, Any]]:
    tz = pytz.timezone(local_tz)
    today = datetime.datetime.now(tz).strftime("%Y-%m-%d")

    prompt = (
        f"""
You are an **NLP date-filter extractor**.  
Return **one JSON object** whose key `extracted_date_filter`
maps to a list of structured objects (see schemas).  
Do **NOT** explain, do **NOT** do date math.
Your goal is to extract the date filters from a user query. 
If the user intent is general and doesn't want to query information from specific dates, return an empty list.

### Schemas + examples:

relative_day
{{"raw":"yesterday","type":"relative_day","offset_days":-1}}
{{"raw":"day before yesterday","type":"relative_day","offset_days":-2}}
{{"raw":"today","type":"relative_day","offset_days":0}}

relative_weekday
{{"raw":"last Friday","type":"relative_weekday","weekday":4,"modifier":"last"}}
{{"raw":"next Tuesday","type":"relative_weekday","weekday":1,"modifier":"next"}}

relative_interval
{{"raw":"this month","type":"relative_interval","unit":"month","amount":0,"modifier":"this"}}
{{"raw":"last month","type":"relative_interval","unit":"month","amount":1,"modifier":"last"}}
{{"raw":"last 3 months","type":"relative_interval","unit":"month","amount":3,"modifier":"last"}}
{{"raw":"last quarter","type":"relative_interval","unit":"month","amount":3,"modifier":"last"}}
{{"raw":"past decade","type":"relative_interval","unit":"decade","amount":1,"modifier":"last"}}
{{"raw":"this week","type":"relative_interval","unit":"week","amount":0,"modifier":"this"}}

named_month
{{"raw":"March 2023","type":"named_month","month":3,"year":2023}}

named_month_list
{{"raw":"March and May 2023","type":"named_month_list","months":[3,5],"year":2023}}
{{"raw":"Q3 2023","type":"named_month_list","months":[7,8,9],"year":2023}}
{{"raw":"the second quarter of 2024","type":"named_month_list","months":[4,5,6],"year":2024}}

named_year
{{"raw":"2022","type":"named_year","year":2022}}

named_week
{{"raw":"week 42 of 2023","type":"named_week","week":42,"year":2023}}

exact_date
{{"raw":"2024-03-15","type":"exact_date","date":"2024-03-15"}}

explicit_date_range
{{"raw":"between 2023-01-01 and 2023-06-30","type":"explicit_date_range","start":"2023-01-01","end":"2023-06-30"}}

before_date
{{"raw":"before 2019","type":"before_date","end":"2018-12-31"}}

after_date
{{"raw":"since the start of 2020","type":"after_date","start":"2020-01-01"}}

Return exactly:
{{"extracted_date_filter":[ …objects… ]}}"""
        + """

### Date-extraction rules (read carefully)

- **Dates are always dates not ranges.** - ex. `"2023-01-01"` not `"2023-01-01:2023-01-31"`

- **Never invent dates.** Only turn words that clearly refer to time into objects.

- **Explicit > implicit.**  
  If a query already contains a clear time word (“today”, “yesterday”, “last week”, a month name, a year, etc.) do **not** add any implicit window.

- **Implicit 7-day “news” window**  
  When the query contains words such as **news**, **latest**, **what’s new**, **updates**, **new stuff** *and* has **no explicit timeframe**, add  
  ```json
  { "raw":"last 7 days (implicit)",
    "type":"relative_interval",
    "unit":"day",
    "amount":7,
    "modifier":"last"
  }
````

* **Relative-day phrases**

  * today → `offset_days: 0`
  * yesterday → `offset_days: -1`
  * day before yesterday / two days ago → `offset_days: -2`

* **“Last / past / previous N days”**
  Return

  ```json
  { "type":"relative_interval", "unit":"day", "amount":N, "modifier":"last" }
  ```

* **“Last week / last month / last quarter”**

  * week → `unit:"week", amount:1`
  * month → **named\_month** of the prior calendar month
  * quarter → `unit:"month", amount:3, modifier:"last"`

* **“This week / this month / this year”**

  * week → `relative_interval` with `unit:"week", amount:0, modifier:"this"`
  * month → same pattern but `unit:"month"`
  * year → same pattern but `unit:"year"`

* **Named quarters (Q1, Q2…)**
  Convert to `named_month_list` with the three correct months.

* **Ranges with “between / from … to …”**
  Produce one `explicit_date_range` object holding `start` and `end`.

* **“Since … / after …”** → `after_date` with a `start` field.
  **“Before …”** → `before_date` with an `end` field.

Follow these rules exactly; output only the JSON container described above.


### Worked query → output examples (edge-case guidance)

1. **Query:** “Give me highlights from the **previous week**”  
   ```json
   {
     "extracted_date_filter": [
       {
         "raw": "previous week",
         "type": "relative_interval",
         "unit": "week",
         "amount": 1,
         "modifier": "last"
       }
     ]
   }
````

2. **Query:** “Show incidents during the **prior month**”

   ```json
   {
     "extracted_date_filter": [
       {
         "raw": "prior month",
         "type": "named_month",
         "month": 4,      // if today is in May
         "year": 2025     // adjust year based on today
       }
     ]
   }
   ```

3. **Query:** “List updates over the **last 30 days**”

   ```json
   {
     "extracted_date_filter": [
       {
         "raw": "last 30 days",
         "type": "relative_interval",
         "unit": "day",
         "amount": 30,
         "modifier": "last"
       }
     ]
   }
   ```

   *(Interpret as an inclusive 30-day window: today − 29 days … today.)*

4. **Query:** “Report issues from the **fortnight leading up to today**”

   ```json
   {
     "extracted_date_filter": [
       {
         "raw": "previous fortnight",
         "type": "relative_interval",
         "unit": "day",
         "amount": 14,
         "modifier": "last"
       }
     ]
   }
   ```

5. **Query:** “Summarize trends from the **last ten years**”

   ```json
   {
     "extracted_date_filter": [
       {
         "raw": "last ten years",
         "type": "relative_interval",
         "unit": "decade",
         "amount": 1,
         "modifier": "last"
       }
     ]
   }
   ```

6. **Query:** “Compare activity across **June and July 2022**”

   ```json
   {
     "extracted_date_filter": [
       { "raw": "June 2022",  "type": "named_month", "month": 6, "year": 2022 },
       { "raw": "July 2022",  "type": "named_month", "month": 7, "year": 2022 }
     ]
   }
   ```

### Implicit “news / what’s new / latest” window

If the user doesn't specify a date range, but the query contains words like
**news**, **latest**, **what’s new**, **updates**, **new stuff**, etc., add a
7-day relative-interval object. This is a **default** behavior for queries
that ask for **news** or **what’s new**.

*Example — query:* “What’s new with container security?”

```json
{
  "extracted_date_filter": [
    {
      "raw": "last 7 days (implicit)",
      "type": "relative_interval",
      "unit": "day",
      "amount": 7,
      "modifier": "last"
    }
  ]
}
```

Do **NOT** add an implicit window if the query already contains a clear
date reference in the query (“today”, “yesterday”, “last week”, a month name, a year, etc.)
*Example — query:* “What’s new with container security since last week?” -> last week is the date filter.

So if there is a date in the query, that is the date filter - there is not implicit
window.
*Example — query:* “What’s news today?”
```json
{
  "extracted_date_filter": [
    {
      "raw": "today",
      "type": "relative_day",
      "offset_days": 0
    }
  ]
}
```

"""
        + f"""
Today is {today}  
Query: "{query}"
"""
    )
    response = llm.invoke([SystemMessage(content=prompt)])
    data = json.loads(response.content)
    return data.get("extracted_date_filter", [])


# ───────────────────────── compute_date_keywords ─────────────────────────
def compute_date_keywords(
    llm: BaseChatModel,
    query: str,
    local_tz: Optional[str] = "UTC",
) -> List[str]:
    tz = pytz.timezone(local_tz)
    today = datetime.datetime.now(tz).date()
    refs = extract_date_references(llm, query, local_tz)

    keywords: List[str] = []
    UNIT_ALIAS = {"fortnight": ("day", 14), "decade": ("day", 3650)}

    def month_range(year: int, month: int) -> str:
        start = datetime.date(year, month, 1)
        end = datetime.date(year, month, calendar.monthrange(year, month)[1])
        return f"{start.isoformat()}:{end.isoformat()}"

    def week_start(d: datetime.date) -> datetime.date:
        return d - datetime.timedelta(days=d.weekday())

    for ref in refs:
        rtype = ref["type"]

        if rtype == "exact_date":
            keywords.append(ref["date"])

        elif rtype == "relative_day":
            date = today + datetime.timedelta(days=ref["offset_days"])
            keywords.append(date.isoformat())

        elif rtype == "relative_weekday":
            wd, mod = ref["weekday"], ref["modifier"]
            cur = today.weekday()
            if mod == "last":
                delta = (cur - wd) % 7 or 7
                date = today - datetime.timedelta(days=delta)
            elif mod == "next":
                delta = (wd - cur) % 7 or 7
                date = today + datetime.timedelta(days=delta)
            else:
                date = today + datetime.timedelta(days=wd - cur)
            keywords.append(date.isoformat())

        elif rtype == "relative_interval":
            unit, amount, mod = ref["unit"], ref["amount"], ref["modifier"]
            if unit in UNIT_ALIAS:
                unit, amount = UNIT_ALIAS[unit]

            if unit == "day":
                start = (
                    today if amount == 0 else today - datetime.timedelta(days=amount)
                )
                end = today
            elif unit == "week":
                start = (
                    week_start(today)
                    if mod == "this" and amount == 0
                    else today - datetime.timedelta(days=amount * 7)
                )
                end = today
            elif unit == "month":
                if mod == "this" and amount == 0:
                    start = today.replace(day=1)
                    end = today.replace(
                        day=calendar.monthrange(today.year, today.month)[1]
                    )
                else:
                    anchor = (today - relativedelta(months=amount)).replace(day=1)
                    start = anchor
                    end = today
            elif unit == "year":
                if mod == "this" and amount == 0:
                    start = today.replace(month=1, day=1)
                    end = today.replace(month=12, day=31)
                else:
                    start = today.replace(year=today.year - amount, month=1, day=1)
                    end = today
            else:
                logger.warning(f"Unknown unit '{unit}' in {ref}")
                continue
            keywords.append(f"{start.isoformat()}:{end.isoformat()}")

        elif rtype == "named_month":
            keywords.append(month_range(ref["year"], ref["month"]))

        elif rtype == "named_month_list":
            months = sorted(ref["months"])
            year = ref["year"]
            is_quarter = (
                len(months) == 3
                and months[2] - months[0] == 2
                and months[0] in (1, 4, 7, 10)
            )
            if is_quarter:
                start = datetime.date(year, months[0], 1)
                end = datetime.date(
                    year, months[-1], calendar.monthrange(year, months[-1])[1]
                )
                keywords.append(f"{start.isoformat()}:{end.isoformat()}")
            else:
                for m in months:
                    keywords.append(month_range(year, m))

        elif rtype == "named_year":
            y = ref["year"]
            keywords.append(f"{y}-01-01:{y}-12-31")

        elif rtype == "named_week":
            y, w = ref["year"], ref["week"]
            start = datetime.datetime.strptime(f"{y}-W{w:02d}-1", "%G-W%V-%u").date()
            end = start + datetime.timedelta(days=6)
            keywords.append(f"{start.isoformat()}:{end.isoformat()}")

        elif rtype == "explicit_date_range":
            keywords.append(f"{ref['start']}:{ref['end']}")

        elif rtype == "before_date":
            keywords.append(f"0001-01-01:{ref['end']}")

        elif rtype == "after_date":
            keywords.append(f"{ref['start']}:{today.isoformat()}")

        else:
            logger.warning(f"Unrecognized type {rtype}: {ref}")

    return sorted(dict.fromkeys(keywords))
