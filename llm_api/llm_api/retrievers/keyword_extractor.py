import json
import logging
from typing import Any, Dict, List, Tuple

from langchain_core.language_models import BaseChatModel
from langchain_core.messages import SystemMessage

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


def _dedupe_keep_order(items: List[str]) -> List[str]:
    seen = set()
    out: List[str] = []
    for it in items:
        if it not in seen:
            out.append(it)
            seen.add(it)
    return out


_PROMPT_TEMPLATE = """
# ROLE
You are an expert keyword-extraction agent in a document-search pipeline.

# OBJECTIVE
Understand the user’s intent, identify the meaningful terms, and suggest natural
variants so OpenSearch can match documents even when authors phrase things
differently.

# BUILD EACH KEY
• **user_intent** – ≤ 25 words, plain-English sentences answering:  
  1. What result or information is the user seeking?  
  2. What are the central subjects/entities and actions of the query?  
  3. What are the key phrases that should be matched?
• **search_units** – minimal phrases or tokens that must be matched exactly. The extracted search units can have at most 3-4 words to be able to make a good match. Shorter is better because the search engine looks for exact match. Also include all sub-search units (espacially of 2 words) if the phrase is longer in **search_units**.
• **sub_search_units** – the ultra complete list of the atomic sub-phrases of search_units that are meaningful and should be matched exactly when searching (they should have 2 or 3 words max). Include the COMPLETE list of ALL sub-phrases for the the search_units with 3 more words or empty list if none. Ask yourself what are the atomic parts of the longer search unit that you should add. For example "advanced persistent threat groups" → "advanced persistent threat", "persistent threat groups", "advanced persistent threat groups".
• **keywords**       – all search-worthy terms. Don't forget to include the main action verbs and subject nouns. Think of how you would do a web search.
• **named_entities** – all the named entities detected in the query that are proper names (people, organizations, companies, products, places). For products and other similar cases include the full phrase as a keyword and named entity. For example "Stripe Payments" not just "Stripe" and "Payments" since they are not as meaningful on their own, you need the full list of keywords for a good match (e.g. "Stripe Payments", "Stripe", "Payments").
• **top_keywords**   – up to three terms from keywords that best capture the query
  focus. The main verb often belongs here (e.g. query “decline of the US dollar”
  → top keyword “decline”). Top keywords will be boosted - the search algorithm looks for exact matches - choose them wisely. Short phrases are better than long ones. Think like a search engine. Choose 2-3 keywords to boost (3 is better). Don't choose more than 3 keywords. Date/time keywords should never be in the top keywords.
• **variations**     – for any keyword that may appear differently, list ≤ 4 common
  spellings, abbreviations, or synonyms

# PHRASE HANDLING
• When two adjacent words form a meaningful concept, add  
  – the full phrase (original spacing & casing)  
  – each individual word

• When a phrase has **three or more** words, add  
  – the full phrase  
  – every individual word  
  – every contiguous two-word sub-phrase  
    (“A B C” → “A B”, “B C”; “A B C D” → “A B”, “B C”, “C D”)

• If the phrase ends with a generic collector (“group(s)”, “activity”, “attack(s)”,
  “event(s)”, etc.), also add the phrase *minus* that last word.  
  – “advanced persistent threat groups” → “advanced persistent threat”

• Count the complete phrase as **one** item when selecting **top_keywords**.  
• Apply the same rule to multi-word named entities.  
• All sub-words of any phrase must appear in **keywords**.  
• Treat hyphenated words as one token (“anti-virus” stays “anti-virus”).  
• If a three-word phrase’s two-word sub-phrase is not in **keywords**, include it
  under **variations**.  
• If correct matching requires ≥ 2 words in order, treat them as a phrase  
  (e.g. “delivery method” → phrase).

# ADVANCED PHRASE RULES
• For every phrase of ≥ 2 words, also output all contiguous two-word slices  
  (e.g. “edge device monitoring” → “edge device”, “device monitoring”).

• For every phrase of ≥ 3 words, additionally output the single (n-1)-word  
  core slice obtained by dropping the final word  
  (“post quantum cryptography standards” → “post quantum cryptography”).

• Keep event verbs that convey the action (detected, disclosed, abusing, etc.).  
  Do not treat them as stop-words.

• If terms are joined by “/”, “,”, “&”, etc., treat the fused token **and**  
  each part as keywords (“TrickBot/Emotet” → “TrickBot”, “Emotet”).

# EXAMPLES
  • **edge device monitoring**  
    → `["edge device monitoring", "edge device", "device monitoring",
        "edge", "device", "monitoring"]`

  • **post quantum cryptography standards**  
    → `["post quantum cryptography standards",
        "post quantum cryptography",            // core slice
        "quantum cryptography standards",
        "post quantum", "quantum cryptography", "cryptography standards",
        "post", "quantum", "cryptography", "standards"]`

  • **leak prevention**  
    → `["leak prevention", "leak", "prevention"]`

  • **TrickBot/Emotet**  
    → `["TrickBot", "Emotet"]`

  • **observed intrusions**  
    → `["observed intrusions", "observed", "intrusions"]`

# NON-SPACE SEPARATORS
If tokens share a slash, comma, or similar, treat each part as separate keywords. For exmaple Google/Facebook → Google, Facebook. ALWAYS split and include the individual parts in the keywords list since the grouping alone might not be relevant in the search. 

# RULES
• named_entities ⊆ keywords  
• top_keywords   ⊆ keywords and len(top_keywords) ≤ 3  
• no duplicates in any list  
• “news” is always important – you MUST include it in **keywords** when present in the query.  
• Action verbs that express a security activity (detected, using, abusing,
  targeting, spoofing, etc.) are keywords and strong candidates for
  **top_keywords**.
• For any phrase of ≥ 3 words, output *every* contiguous 2-word slice (even if it sits in the middle).
• If a phrase begins with a named entity, also output the remainder of the phrase (entity stripped) as a sub_search_unit and keyword.
• Abreviations and acronyms are usually keywords. Include the full form in variations.
• If a phrase has a industry specific abbreviation, include the abbreviation in
  variations. For example "Advanced Threat Protection" → "ATP" in variations.
• Extract all technical phrases that relevant in the query in the search_units - example "remote access trojan".
• IPs, links, domains, and other identifiers should be extracted individually as keywords. If they should be boosted boosted them as individual keywords. For example "IP ***********" should be boosted as "***********" not "IP ***********".
• Hyphenated words are treated as one token. For example "anti-virus" is one token and should be extracted as "anti-virus" not "anti" and "virus" or "anti virus".

# OUTPUT FORMAT
Return **only** one JSON object (no markdown, no commentary):

{{
  "user_intent": "...",
  "search_units": ["..."],
  "sub_search_units": ["..."],
  "keywords": ["..."],
  "named_entities": ["..."],
  "top_keywords": ["..."],
  "variations": {{"keyword": ["alt1", "alt2"]}}
}}

# WORKED EXAMPLES  (***none appear in the tests***)

• Two-word phrase — **session hijacking**  
  {{
    "user_intent": "Find technical material on session hijacking attacks.",
    "search_units": ["session hijacking"],
    "sub_search_units": [],
    "keywords": ["session hijacking", "session", "hijacking"],
    "named_entities": [],
    "top_keywords": ["session hijacking"],
    "variations": {{}}
  }}

• Two-word phrase — **malware triage**  
  {{
    "user_intent": "Learn workflows and tools for effective malware triage.",
    "search_units": ["malware triage"],
    "sub_search_units": [],
    "keywords": ["malware triage", "malware", "triage"],
    "named_entities": [],
    "top_keywords": ["malware", "triage"],
    "variations": {{}}
  }}

• Three-word phrase with collector — **edge network telemetry**  
  {{
    "user_intent": "Research edge network telemetry techniques.",
    "search_units": ["edge network telemetry"],
    "sub_search_units": ["edge network", "network telemetry"],
    "keywords": ["edge network telemetry",
                 "edge network", "network telemetry",
                 "edge", "network", "telemetry"],
    "named_entities": [],
    "top_keywords": ["edge network telemetry"],
    "variations": {{}}
  }}

• Four-word named entity — **QuantumShield Labs Ltd**  
  {{
    "user_intent": "Gather information about the company QuantumShield Labs Ltd.",
    "search_units": ["QuantumShield Labs Ltd"],
    "sub_search_units": ["QuantumShield Labs"],
    "keywords": ["QuantumShield Labs Ltd",
                 "QuantumShield Labs", "Labs Ltd",
                 "QuantumShield", "Labs", "Ltd"],
    "named_entities": ["QuantumShield Labs Ltd"],
    "top_keywords": ["QuantumShield Labs Ltd"],
    "variations": {{}}
  }}

# QUERY
{query}
""".strip()


def _extract_keywords(
    llm: BaseChatModel,
    query: str,
    retries: int = 3,
) -> Tuple[List[str], List[str], str]:
    prompt = _PROMPT_TEMPLATE.format(query=query)

    try:
        raw = llm.invoke([SystemMessage(content=prompt)]).content.strip()
        data: Dict[str, Any] = json.loads(raw)

        keywords = _dedupe_keep_order(data.get("keywords", []))
        search_units = data.get("search_units", [])
        sub_search_units = data.get("sub_search_units", [])

        # include each search_unit in keywords
        for unit in search_units + sub_search_units:
            if unit not in keywords:
                keywords.append(unit)

        # for every phrase in keywords (spaces) add components
        for kw in keywords[:]:
            if " " in kw.strip():
                for part in kw.split():
                    if part and part not in keywords:
                        keywords.append(part)

        keywords = _dedupe_keep_order(keywords)

        named_entities = data.get("named_entities", [])
        llm_top = data.get("top_keywords", [])

        # include *all* named entities in top_keywords
        top_keywords = _dedupe_keep_order(named_entities + llm_top)

        return keywords, top_keywords, raw

    except Exception as exc:
        logger.error("Keyword extraction failed: %s", exc)
        if retries > 1:
            return _extract_keywords(llm, query, retries - 1)
        raise


def extract_keywords(
    llm: BaseChatModel,
    query: str,
    retries: int = 3,
) -> Tuple[List[str], List[str]]:
    """
    Return (keywords, top_keywords) extracted from the query by the LLM.

    Extra post-processing:
    • ensure every search_unit is in keywords
    • for any multi-word term (space-separated) ensure each word is present
    """
    keywords, top_keywords, raw = _extract_keywords(llm, query, retries)
    return keywords, top_keywords
