import asyncio
import json
import logging
import os
import re
from datetime import datetime
from typing import Any, Dict, List

from langchain_core.callbacks import (
    AsyncCallbackManagerForRetrieverRun,
    CallbackManagerForRetrieverRun,
)
from langchain_core.documents import Document
from langchain_core.language_models import BaseChatModel
from langchain_core.messages import SystemMessage
from langchain_core.retrievers import BaseRetriever
from opensearchpy import AsyncOpenSearch
from opensearchpy.exceptions import OpenSearchException

from llm_api.llm.factory import (
    default_4_gpt_spec_data_json_enabled,
    get_model_from_spec,
)
from llm_api.specs.llm_spec import LLMSpec, LLMType

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


def build_query_dict(
    query: str, filter_by_date: bool, disabled_feed_ids: List[int]
) -> Dict[str, Any]:
    date_conditions = []
    words = [word.strip() for word in query.split(",")]
    remaining_words = []
    for word in words:
        if filter_by_date and re.match(r"^\d{4}-\d{2}-\d{2}:\d{4}-\d{2}-\d{2}$", word):
            try:
                start_date, end_date = word.split(":")
                datetime.strptime(start_date, "%Y-%m-%d")
                datetime.strptime(end_date, "%Y-%m-%d")
                date_conditions.append(
                    {"range": {"published_date": {"gte": start_date, "lte": end_date}}}
                )
            except Exception:
                remaining_words.append(word)
        elif filter_by_date and re.match(r"^\d{4}-\d{2}-\d{2}$", word):
            date_conditions.append({"term": {"published_date": word}})
        else:
            remaining_words.append(word)

    named_entity_boost = float(os.environ.get("NAMED_ENTITY_BOOST", "5"))
    match_phrases = []
    for word in remaining_words:
        if not word:
            continue
        if word.startswith("+"):
            match_phrases.append(
                {
                    "match_phrase": {
                        "content": {"query": word[1:], "boost": named_entity_boost}
                    }
                }
            )
        else:
            match_phrases.append({"match_phrase": {"content": word}})

    query_dict = {
        "query": {"bool": {"should": match_phrases}},
        "sort": [{"_score": {"order": "desc"}}],
        "size": OpenSearchRetriever.get_default_params()["size"],
    }

    if filter_by_date:
        query_dict["sort"].append({"published_date": {"order": "desc"}})

    if filter_by_date and date_conditions:
        query_dict["query"]["bool"]["filter"] = {
            "bool": {"should": date_conditions, "minimum_should_match": 1}
        }

    if disabled_feed_ids:
        query_dict["query"]["bool"]["must_not"] = [
            {"terms": {"feed_id": [str(fid) for fid in disabled_feed_ids]}}
        ]

    return query_dict


def should_fetch_more_and_rerank() -> bool:
    return (
        os.environ.get("RAG_OPEN_SEARCH_FETCH_MORE_DOCUMENTS", "false").lower()
        == "true"
    )


class OpenSearchRetriever(BaseRetriever):
    @staticmethod
    def get_default_params(
        index_name: str = os.environ.get(
            "LLM__OPENSEARCH__INDEXNAME", "general_knowledge_blogs_dev"
        ),
        filter_by_date: bool = True,
        disabled_feed_ids: List[int] | None = None,
    ) -> Dict[str, Any]:
        return {
            "host": os.environ.get("LLM__OPENSEARCH__HOST", "localhost"),
            "port": int(os.environ.get("LLM__OPENSEARCH__PORT", "9200")),
            "username": os.environ.get("LLM__OPENSEARCH__USERNAME"),
            "password": os.environ.get("LLM__OPENSEARCH__PASSWORD"),
            "index_name": index_name,
            "size": int(os.environ.get("LLM__OPENSEARCH__MAXQUERYRESULTS", "4")),
            "filter_by_date": filter_by_date,
            "disabled_feed_ids": disabled_feed_ids or [],
        }

    host: str
    port: int
    username: str
    password: str
    index_name: str
    filter_by_date: bool
    llm: BaseChatModel
    disabled_feed_ids: List[int]

    @classmethod
    def create(
        cls,
        host: str,
        port: int,
        username: str,
        password: str,
        index_name: str,
        filter_by_date: bool = True,
        **kwargs,
    ) -> "OpenSearchRetriever":
        return cls(
            host=host,
            port=port,
            username=username,
            password=password,
            index_name=index_name,
            filter_by_date=filter_by_date,
            llm=get_model_from_spec(
                LLMSpec(
                    type=LLMType.AzureChatOpenAI,
                    data=default_4_gpt_spec_data_json_enabled,
                )
            ),
            disabled_feed_ids=kwargs.get("disabled_feed_ids", []),
        )

    def _get_relevant_documents(
        self, query: str, *, run_manager: CallbackManagerForRetrieverRun
    ) -> List[Document]:
        raise NotImplementedError(
            "Synchronous retrieval not supported for OpenSearchRetriever"
        )

    def get_client(self) -> AsyncOpenSearch:
        return AsyncOpenSearch(
            hosts=[{"host": self.host, "port": self.port}],
            http_auth=(self.username, self.password),
            use_ssl=True,
            verify_certs="amazonaws" in self.host,
        )

    async def _query_opensearch(
        self,
        keywords: str,
        size: int,
        multiplier: int,
        filter_by_date_flag: bool,
        retries: int = 3,
    ) -> List[Document]:
        client = self.get_client()
        try:
            query_dict = build_query_dict(
                keywords,
                filter_by_date=filter_by_date_flag,
                disabled_feed_ids=self.disabled_feed_ids,
            )
            query_dict["size"] = size * multiplier
            res = await client.search(index=self.index_name, body=query_dict)
            docs = [
                Document(
                    page_content=hit["_source"]["content"],
                    metadata={
                        "source": hit["_source"]["url"],
                        "score": hit["_score"],
                        "published_date": hit["_source"].get("published_date"),
                        "retriever_source": "keywords",
                    },
                )
                for hit in res["hits"]["hits"]
            ]
            return docs
        except OpenSearchException as err:
            logger.error(
                "OpenSearch failed (retries left %s): %s",
                retries - 1,
                err,
            )
            if retries > 1:
                await asyncio.sleep(1)
                return await self._query_opensearch(
                    keywords,
                    size,
                    multiplier,
                    filter_by_date_flag,
                    retries - 1,
                )
            return []
        finally:
            await client.close()

    async def _aget_relevant_documents(
        self, query: str, *, run_manager: AsyncCallbackManagerForRetrieverRun
    ) -> List[Document]:
        multiplier = int(
            os.environ.get("OPENSEARCH_QUERY_DOCUMENT_COUNT_MULTIPLIER", "5")
        )
        default_size = OpenSearchRetriever.get_default_params()["size"]
        rerank_enabled = should_fetch_more_and_rerank()

        separator = "##@@##"
        if separator in query:
            question, keywords = query.split(separator, 1)
        else:
            question = keywords = query

        async def fetch_and_process(filter_by_date_flag: bool) -> List[Document]:
            docs = await self._query_opensearch(
                keywords,
                default_size,
                multiplier if rerank_enabled else 1,
                filter_by_date_flag,
            )
            if rerank_enabled:
                return await self._rerank_and_select(question, docs, default_size)
            return docs

        selected_docs = await fetch_and_process(self.filter_by_date)

        if self.filter_by_date and not selected_docs:
            selected_docs = await fetch_and_process(False)

        return selected_docs

    async def _rerank_and_select(
        self, question: str, docs: List[Document], default_size: int
    ) -> List[Document]:
        try:
            reranked_docs = await self.rerank_documents(question, docs)
            return self.select_top_documents(reranked_docs, default_size)
        except Exception as err:
            logger.warning("Rerank failed, returning all documents: %s", err)
            return docs

    async def rerank_documents(
        self, query: str, documents: List[Document]
    ) -> List[Document]:
        if not documents:
            return []

        prompt_docs = [
            {"id": idx, "content": d.page_content} for idx, d in enumerate(documents)
        ]
        prompt = (
            f"Given the user query: '{query}', rank the following documents by relevance.\n"
            "Documents that have information that answer the query should be rated higher.\n"
            "If a document fully answers the query, it should be rated 10.\n"
            "If a document does not answer the query at all, it should be rated 0.\n"
            'Return JSON {"scores":[{"id":int,"score":0-10}, ...]}\n'
            f"Documents: {json.dumps(prompt_docs)}"
        )

        response = await self.llm.ainvoke([SystemMessage(content=prompt)])
        scores_json = json.loads(response.content).get("scores", [])

        scores_map = {
            s.get("id"): s.get("score", 0) for s in scores_json if isinstance(s, dict)
        }
        for idx, doc in enumerate(documents):
            doc.metadata["rerank_score"] = scores_map.get(idx, 0)

        documents.sort(key=lambda d: d.metadata.get("rerank_score", 0), reverse=True)
        return documents

    def select_top_documents(
        self, documents: List[Document], default_size: int
    ) -> List[Document]:
        if len(documents) <= default_size:
            return documents

        boundary_score = (
            documents[default_size - 1].metadata.get("rerank_score", 0) or 1
        )
        top_documents = [
            d for d in documents if d.metadata.get("rerank_score", 0) >= boundary_score
        ]
        return top_documents
