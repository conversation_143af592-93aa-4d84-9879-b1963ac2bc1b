import asyncio
import datetime
import json
import logging
import os
import time
from typing import List, Optional, Tuple
from uuid import UUID

import pytz
from langchain_core.callbacks import (
    AsyncCallbackManagerForRetrieverRun,
    CallbackManagerForRetrieverRun,
)
from langchain_core.documents import Document
from langchain_core.messages import SystemMessage
from langchain_core.retrievers import BaseRetriever
from langchain_core.vectorstores import VectorStoreRetriever

from llm_api.blai_llm.utils import PlanLogger
from llm_api.llm.factory import (
    LLMSpec,
    LLMType,
    default_4_gpt_spec_data_json_enabled,
    get_model_from_spec,
)
from llm_api.retrievers import OpenSearchRetriever
from llm_api.retrievers.base import FusingStrategy
from llm_api.retrievers.date_extractor import compute_date_keywords
from llm_api.retrievers.keyword_extractor import extract_keywords as kw_extract
from llm_api.vectorstores.pgvector_integration import BlaiP<PERSON><PERSON>ectorFetcher
from llm_api.utils.page_metadata import get_page_title

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


class VectorStoreKeywordRetriever(BaseRetriever):
    vectorstore_retriever: VectorStoreRetriever
    keyword_retriever: OpenSearchRetriever
    fusing_strategy: FusingStrategy
    collection_name: str
    plan_id: UUID
    local_timezone: Optional[str] = None

    def _get_relevant_documents(
        self, query: str, *, run_manager: CallbackManagerForRetrieverRun
    ) -> List[Document]:
        raise NotImplementedError(
            f"You are calling a sync method on {self.__class__}. This is not what you want to do!"
        )

    def get_document_with_full_metadata_from_pgvector(self, doc: Document) -> Document:
        vectordb = BlaiPGVectorFetcher(self.collection_name)
        return vectordb.get_document_with_full_metadata(doc)

    def get_document_with_full_metadata(self, doc: Document) -> Document:
        return self.get_document_with_full_metadata_from_pgvector(doc)

    async def _aget_relevant_documents(
        self,
        query: str,
        *,
        run_manager: AsyncCallbackManagerForRetrieverRun,
    ) -> List[Document]:
        # Get fused result of the retrievers.
        question = query
        keywords = VectorStoreKeywordRetriever.extract_keywords(
            query=query,
            local_timezone=self.local_timezone,
        )
        log_plan = PlanLogger()
        log_text = f"Extracted keywords: {repr(keywords)}"
        log_plan.addToDevLog(log_text, self.plan_id)

        keywords = ",".join(keywords)
        logger.debug(
            f"Split query into question: '{question}' and keywords: '{keywords}'"
        )

        # choose parallel retrieval, by default
        retrieve_func = self.retrieve_documents_parallel
        retrieve_opt = os.environ.get("LLM__VECTORSTOREANDKEYWORDS__STRATEGY", "par")

        # can only do sequencial retrieval if we got some keywords
        if retrieve_opt.lower() == "seq" and keywords:
            retrieve_func = self.retrieve_documents_sequentially

        documents_from_retrievers = await retrieve_func(
            question=question, keywords=keywords, run_manager=run_manager
        )

        logger.debug(f"Docs from retrievers: {documents_from_retrievers}")

        # proceed with fusing only if
        # we got responses from retrievers
        if documents_from_retrievers:
            fused_documents = self.fusing_strategy.fuse_documents(
                doc_lists=documents_from_retrievers
            )

            logger.debug(f"Docs after fusing: {len(fused_documents)}")

            for doc in fused_documents:
                doc.metadata["query"] = query
                full_doc = self.get_document_with_full_metadata(doc)
                if full_doc:
                    for key, value in full_doc.metadata.items():
                        if key not in doc.metadata:
                            doc.metadata[key] = value
                else:
                    logger.debug(f"Could not get metadata for document: {doc}")
                if "filename" not in doc.metadata and "source" in doc.metadata:
                    doc.metadata["filename"] = os.path.basename(doc.metadata["source"])

                if (
                        "source" in doc.metadata
                        and doc.metadata["source"].startswith("http")
                        and not doc.metadata.get("page_title")
                ):
                    try:
                        page_title = await get_page_title(doc.metadata["source"])
                        if page_title:
                            doc.metadata["page_title"] = page_title
                    except Exception as e:
                        logger.warning(
                            f"Failed to fetch title for {doc.metadata['source']}: {e}"
                        )

                logger.debug(
                    f"{doc.metadata['retriever_source']} - {doc.metadata['source']}"
                )

        else:
            logger.warning(f"NO documents from retrievers!")
            return []

        return fused_documents

    async def retrieve_documents_parallel(
        self,
        question: str,
        keywords: str,
        run_manager: AsyncCallbackManagerForRetrieverRun,
    ):
        logger.info("Retrieving in parallel")
        # do the retrieval in parallel
        retrieve_queries = []

        retrieve_queries.append(
            self.vectorstore_retriever.ainvoke(
                question,
                callbacks=run_manager.get_child(tag=f"vectorstore_retriever"),
            )
        )
        if keywords:
            retrieve_queries.append(
                self.keyword_retriever.ainvoke(
                    f"{question}##@@##{keywords}",
                    callbacks=run_manager.get_child(tag=f"keyword_retriever"),
                )
            )

        retrieved_documents = await asyncio.gather(*retrieve_queries)

        return retrieved_documents

    async def retrieve_documents_sequentially(
        self,
        question: str,
        keywords: str,
        run_manager: AsyncCallbackManagerForRetrieverRun,
    ) -> List[Document]:
        logger.info("Retrieving sequentially")
        # send the query to the keywords retriever first
        # so we get a list of potential articles
        keyword_docs = await self.keyword_retriever.ainvoke(
            f"{question}##@@##{keywords}",
            callbacks=run_manager.get_child(tag=f"keywords_retriever"),
        )
        keyword_docs_sources = [doc.metadata["source"] for doc in keyword_docs]

        logger.debug(f"Docs from keywords: {keyword_docs}")

        # proceed with vectorstore search
        # only if we got some docs from keyword
        if keyword_docs:
            # use the urls from the keyword search to filter the vectorstore search
            if len(keyword_docs_sources) > 1:
                self.vectorstore_retriever.search_kwargs = {
                    "filter": {
                        "$or": [
                            {"source": {"$eq": keyword_source}}
                            for keyword_source in keyword_docs_sources
                        ]
                    },
                    "include_metadata": True,
                    "metadata_fields": ["*"],
                }
            else:
                self.vectorstore_retriever.search_kwargs = {
                    "filter": {
                        "source": {
                            "$eq": keyword_docs_sources[0],
                        }
                    },
                    "include_metadata": True,
                    "metadata_fields": ["*"],
                }
            vectorstore_docs = await self.vectorstore_retriever.ainvoke(
                question,
                callbacks=run_manager.get_child(tag=f"vectorstore_retriever"),
            )
            logger.debug(f"Docs from vectorstore: {vectorstore_docs}")

            return [vectorstore_docs, keyword_docs]

        # return an empty list in case nothing was found
        return []

    @staticmethod
    def extract_keywords(query: str, local_timezone: Optional[str]) -> List[str]:
        llm = get_model_from_spec(
            LLMSpec(
                type=LLMType.AzureChatOpenAI,
                data=default_4_gpt_spec_data_json_enabled,
            )
        )

        tz_name = local_timezone if local_timezone else "America/New_York"

        date_keywords = compute_date_keywords(
            llm=llm,
            query=query,
            local_tz=tz_name,
        )

        kw_list, top_keywords = kw_extract(
            llm=llm,
            query=query,
        )

        combined: List[str] = []
        for kw in date_keywords + kw_list + top_keywords:
            if kw in top_keywords:
                if f"+{kw}" not in combined:
                    combined.append(f"+{kw}")
            else:
                if kw not in combined:
                    combined.append(kw)

        return combined


class WeightedReciprocicalRankStrategy(FusingStrategy):
    c: int = 60

    @staticmethod
    def compute_weights_for_docs(doc_lists_length: int) -> List[float]:
        return [1 / doc_lists_length] * doc_lists_length

    def fuse_documents(self, doc_lists: List[List[Document]]) -> List[Document]:
        weights = WeightedReciprocicalRankStrategy.compute_weights_for_docs(
            len(doc_lists)
        )
        assert len(doc_lists) == len(
            weights
        ), f"len(doc_lists): {len(doc_lists)}; len(weights): {len(weights)}"

        # Create a union of all unique documents in the input doc_lists
        all_documents = set()
        for doc_list in doc_lists:
            for doc in doc_list:
                all_documents.add(doc.page_content)

        # Initialize the RRF score dictionary for each document
        rrf_score_dic = {doc: 0.0 for doc in all_documents}

        # Calculate RRF scores for each document
        for doc_list, weight in zip(doc_lists, weights):
            for rank, doc in enumerate(doc_list, start=1):
                rrf_score = weight * (1 / (rank + self.c))
                rrf_score_dic[doc.page_content] += rrf_score

        # Sort documents by their RRF scores in descending order
        sorted_documents = sorted(
            rrf_score_dic.keys(), key=lambda x: rrf_score_dic[x], reverse=True
        )

        # Map the sorted page_content back to the original document objects
        page_content_to_doc_map = {
            doc.page_content: doc for doc_list in doc_lists for doc in doc_list
        }

        sorted_docs = [
            page_content_to_doc_map[page_content] for page_content in sorted_documents
        ]

        return sorted_docs
