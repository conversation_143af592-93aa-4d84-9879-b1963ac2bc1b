import logging
import os
from datetime import datetime, timedelta

from llm_api.retrievers.vectorstore_keyword_retriever import VectorStoreKeywordRetriever

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


def get_date(days_ago):
    return (datetime.now() - timedelta(days=days_ago)).strftime("%Y-%m-%d")


def get_month_range(month):
    now = datetime.now()
    year = now.year if now.month > month else now.year - 1
    start = datetime(year, month, 1)
    if month == 12:
        end = datetime(year + 1, 1, 1) - timedelta(days=1)
    else:
        end = datetime(year, month + 1, 1) - timedelta(days=1)
    return f"{start.strftime('%Y-%m-%d')}:{end.strftime('%Y-%m-%d')}"


def get_keyword_tests():
    today = get_date(0)
    last_7_days = f"{get_date(7)}:{today}"
    last_14_days = f"{get_date(14)}:{today}"
    last_30_days = f"{get_date(30)}:{today}"
    last_90_days = f"{get_date(90)}:{today}"
    last_march = get_month_range(3)
    last_may = get_month_range(5)

    return [
        {
            "query": "The latest news about the stock market",
            "expected_dates": [last_7_days],
            "expected_keywords": ["stock market", "news"],
        },
        {
            "query": "What happened in cybersecurity in the last two weeks?",
            "expected_dates": [last_14_days],
            "expected_keywords": ["cybersecurity"],
        },
        {
            "query": "What are the news about Kubernetes?",
            "expected_dates": [last_30_days],
            "expected_keywords": ["Kubernetes", "news"],
        },
        {
            "query": "What happened in the cybersecurity space in the past three months?",
            "expected_dates": [last_90_days],
            "expected_keywords": ["cybersecurity", "news"],
        },
        {
            "query": "What vulnerabilities were disclosed in the in last march and may?",
            "expected_dates": [last_march, last_may],
            "expected_keywords": ["vulnerabilities", "disclosed"],
        },
        {
            "query": "What happened in cybersecurity today?",
            "expected_dates": [today],
            "expected_keywords": ["cybersecurity"],
        },
    ]


def test_extract_keywords():
    tests = get_keyword_tests()
    results = []
    for idx, test in enumerate(tests, 1):
        keywords = VectorStoreKeywordRetriever.extract_keywords(test["query"])
        extracted_dates = [
            k
            for k in keywords
            if ":" in k or len(k) == 10 and k[4] == "-" and k[7] == "-"
        ]
        extracted_non_dates = [k for k in keywords if k not in extracted_dates]

        expected_dates = test["expected_dates"]
        expected_keywords = test["expected_keywords"]

        date_match = all(date in extracted_dates for date in expected_dates)
        keyword_match = all(kw in extracted_non_dates for kw in expected_keywords)

        outcome = "✅" if date_match and keyword_match else "❌"

        results.append(
            {
                "case": idx,
                "query": test["query"],
                "expected_dates": expected_dates,
                "extracted_dates": extracted_dates,
                "expected_keywords": expected_keywords,
                "extracted_keywords": extracted_non_dates,
                "result": outcome,
            }
        )
    return {"results": results}
