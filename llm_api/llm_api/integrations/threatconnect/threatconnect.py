import base64
import hashlib
import hmac
import os
import time
from datetime import datetime, timezone

import requests
from pydantic import BaseModel

from llm_api.http_client import get_session
from llm_api.integrations.domain import FileMeta


class ThreatConnectAPIConfig(BaseModel):
    api_base_url: str
    api_access_id: str
    api_secret_key: str


def call_threatconnect(
    api_config: ThreatConnectAPIConfig, endpoint: str, binary: bool = False
):
    # Generate a nonce (Timestamp header)
    timestamp = str(int(time.time()))

    # Construct the API path and query string for the Authorization header
    api_path = endpoint + ""
    http_method = "GET"
    signature_data = "{}:{}:{}".format(api_path, http_method, timestamp)
    # Create the signature using HMAC-SHA256 with base64 encoding
    signature = hmac.new(
        api_config.api_secret_key.encode(), signature_data.encode(), hashlib.sha256
    ).digest()
    signature_b64 = base64.b64encode(signature).decode()
    # Construct the authentication headers
    content_type = "application/octet-stream" if binary else "application/json"
    auth_headers = {
        "Timestamp": timestamp,
        "Authorization": "TC {}:{}".format(api_config.api_access_id, signature_b64),
        "Accept": content_type,
    }

    try:
        endpoint_url = api_config.api_base_url + endpoint
        session = get_session()
        response = session.get(
            endpoint_url,
            headers=auth_headers,
            timeout=int(os.environ.get("PROCEDURES__WORKERS__REQUEST__TIMEOUT", "10")),
        )
        response.raise_for_status()  # Raise an exception for HTTP errors
        # print(response.headers)
        # print(response)
        if binary:
            return response.content
        else:
            return response.json()

    except requests.exceptions.RequestException as e:
        print("Error: {}".format(e))
        return "Error"


def get_file_metas(
    config: ThreatConnectAPIConfig,
    lastUpdateDate=None,
    limit=10,
) -> tuple[list[FileMeta], datetime]:
    page_size = limit
    # handle limit = -1 to grab all files
    if limit < 0:
        page_size = 25
    next_url = f"/api/v3/groups?fields=tags,attributes&resultLimit={page_size}&sorting=lastModified%20asc"
    if lastUpdateDate:
        next_url += f"&tql=lastModified%20GT%20%22{lastUpdateDate}%22"

    file_metas = []
    processed_count = 0
    new_last_update_date = None

    while next_url != None:
        response = call_threatconnect(config, next_url)

        for _ in range(0, 3):
            if response == "Error":
                time.sleep(1)
                response = call_threatconnect(config, next_url)

        if "next" in response:
            next_url = response["next"]
            if config.api_base_url in next_url:
                next_url = next_url.replace(config.api_base_url, "")
        else:
            next_url = None

        print(response)

        groups = response["data"]

        for group in groups:
            new_last_update_date: datetime = datetime.strptime(
                group["lastModified"],
                "%Y-%m-%dT%H:%M:%SZ",
            ).replace(tzinfo=timezone.utc)

            file_meta = None
            final_tags = ["all"]
            if "tags" in group and "data" in group["tags"]:
                tags = group["tags"]["data"]
                final_tags += [tag["name"] for tag in tags]

            publish_date = None
            if "attributes" in group:
                if "data" in group["attributes"]:
                    for attr in group["attributes"]["data"]:
                        if "type" in attr and attr["type"].lower() == "publish date":
                            publish_date = attr["value"]

            file_meta = FileMeta(
                id=str(group["id"]),
                name=group["name"],
                tags=final_tags,
                time=group.get("documentDateAdded"),
                modifiedAt=group["lastModified"],
                publishDate=publish_date,
            )

            lastUpdateDatetime = (
                datetime.strptime(lastUpdateDate, "%Y-%m-%dT%H:%M:%SZ").replace(
                    tzinfo=timezone.utc
                )
                if lastUpdateDate
                else None
            )

            if file_meta == None:
                continue

            if lastUpdateDate == None or file_meta.modifiedAt > lastUpdateDatetime:
                file_metas.append(file_meta)

        processed_count += len(groups)
        if limit > 0 and processed_count >= limit:
            break

    # print("@@file_metas: ", file_metas)

    return (file_metas, new_last_update_date)


def get_file_pdf(config: ThreatConnectAPIConfig, file_id: str) -> bytes:
    return call_threatconnect(config, f"/api/v3/groups/{file_id}/download", binary=True)
