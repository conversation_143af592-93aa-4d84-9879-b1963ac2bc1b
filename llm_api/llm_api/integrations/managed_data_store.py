import json
import logging
import os
from datetime import datetime, timedelta

import redis
import requests
from pydantic import BaseModel

from llm_api.blai_api.dtos import DataStoreDocument
from llm_api.blai_llm.constants import (
    LLM_API_KEY,
    PUBLIC_BACKEND_URL,
    REDIS_PORT,
    REDIS_URL,
)
from llm_api.blai_llm.data_stores import DataStoreManager
from llm_api.http_client import get_session
from llm_api.integrations.domain import FileMeta
from llm_api.integrations.threatconnect.threatconnect import (
    ThreatConnectAPIConfig,
    get_file_metas,
    get_file_pdf,
)
from llm_api.utils import monitor_long_request

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

# TODO: send datastore names

redis = redis.Redis(host=REDIS_URL, port=REDIS_PORT, db=0)


def process_tag(raw_tag):
    # logger.debug(f"@@@@ Processing tag: {raw_tag}")
    raw_tag = raw_tag.strip()
    tag_mapping = {
        "Research Target: Geopolitics": "Geopolitics",
        "Research Target: Emerging Technology": "Emerging Technology",
        "Research Target: Hacktivism": "Hacktivism",
        "Research Target: Espionage": "Espionage",
        "Research Target: Cybercrime": "Cybercrime",
        "Research Target: Disclosed Breach": "Disclosed Breach",
        "Research Target: Tactics Techniques Procedures (TTP)": "TTP",
        "Research Target: TTP": "TTP",
        "Research Target: Vulnerabilities": "Vulnerability",
        "Research Target: Vulnerability": "Vulnerability",
        "Research Target: Malware": "Malware",
        "Research Target: Threat Actor": "Threat Actor",
        "all": "all",
    }
    return tag_mapping.get(raw_tag)


def test_process_tag():
    test_cases = [
        # Mapped tags
        ("Research Target: Geopolitics", "Geopolitics"),
        ("Research Target: Emerging Technology", "Emerging Technology"),
        ("Research Target: Hacktivism", "Hacktivism"),
        ("Research Target: Espionage", "Espionage"),
        ("Research Target: Cybercrime", "Cybercrime"),
        ("Research Target: Disclosed Breach", "Disclosed Breach"),
        ("Research Target: Tactics Techniques Procedures (TTP)", "TTP"),
        ("Research Target: Vulnerabilities", "Vulnerability"),
        ("Research Target: Malware", "Malware"),
        ("Research Target: Threat Actor", "Threat Actor"),
        # Non-mapped tags
        ("Threat Actor: Scattered Spider", None),
        ("Origin Nation State: Russia", None),
        ("Target Nation State: USA", None),
        ("Target Region: Asia", None),
        ("Targeted Industry Sector: Finance", None),
        ("Vulnerability: CVE-2021-44228", None),
        ("Target System: Windows", None),
        ("Malware: WannaCry", None),
        ("Tool: Metasploit", None),
        ("T(123): Example Tactic", None),
        ("TTP: Phishing", None),
        ("Dataset Tag Families: Malware Samples", None),
        ("Geopolitics: Russia-Ukraine", None),
        ("Emerging Technology: AI", None),
        ("Hacktivism: Anonymous", None),
        ("Espionage: APT28", None),
        ("Cybercrime: Ransomware", None),
    ]

    logger.info("Running tests for process_tag")
    for raw_tag, expected in test_cases:
        result = process_tag(raw_tag)
        assert (
            result == expected
        ), f"Test failed for: '{raw_tag}'. Expected: {expected}, Got: {result}"
        logger.info(f"Test passed for: '{raw_tag}' -> '{result}'")


# test_process_tag()


def description_for_tag(processed_tag):
    tag_descriptions = {
        "Geopolitics": "Stores information related to political dynamics impacting cybersecurity.",
        "Emerging Technology": "Contains data on new technologies that may pose cybersecurity risks.",
        "Hacktivism": "Includes information on activism conducted through hacking.",
        "Espionage": "Holds details on spying activities, often related to nation-states.",
        "Cybercrime": "Stores information on criminal activities conducted online.",
        "Disclosed Breach": "Contains information about data breaches that have been publicly disclosed.",
        "TTP": "Contains details on Tactics, Techniques, and Procedures used by threat actors.",
        "Vulnerability": "Holds data on system or application weaknesses that can be exploited.",
        "Malware": "Contains information on malicious software used in cyber attacks.",
        "Threat Actor": "Stores information on individuals or groups conducting malicious activities.",
        "all": "Stores ThreatConnect files with any tag.",
    }
    return tag_descriptions.get(
        processed_tag, f"ThreatConnect files with the tag {processed_tag}"
    )


class ManagedDataStore(BaseModel):
    type: str
    config: dict

    def datastore_name_for_tag(self, tag: str):
        processed_tag = process_tag(tag)
        if processed_tag is None:
            return None
        return f"{self.type}: {processed_tag}".lower()

    def datastore_names_for(self, file: FileMeta, organizationId: str, config: dict):
        datastore_names = [self.datastore_name_for_tag(tag) for tag in file.tags]
        datastore_names = [
            datastore_name for datastore_name in datastore_names if datastore_name
        ]
        logger.debug("Datastore names for file %s: %s", file.name, datastore_names)
        return datastore_names

    def redis_key(self, organizationId):
        return f"rbc:{self.type}:_the_last_sync_update_time_for_organ_with_id:{organizationId}"

    async def managed_ds_to_be_synced(self, file: FileMeta, organizationId: str):
        if not LLM_API_KEY or not PUBLIC_BACKEND_URL:
            logger.error("LLM_API_KEY or PUBLIC_BACKEND_URL not defined")
            return False

        headers = {"LLMApiKey": LLM_API_KEY}
        try:
            datastore_names = self.datastore_names_for(
                file, organizationId, self.config
            )
            datastore_names = [
                datastore_name for datastore_name in datastore_names if datastore_name
            ]

            logger.debug("Sending datastores %s for computation sync", datastore_names)

            datastore_descriptions = {
                self.datastore_name_for_tag(tag): description_for_tag(tag)
                for tag in file.tags
                if self.datastore_name_for_tag(tag)
            }
            data = {
                "organizationId": organizationId,
                "managedDsNames": datastore_names,
                "dsDescriptions": datastore_descriptions,
                "providerDocId": file.id,
                "provider": self.type,
                "lastModifiedAt": str(file.modifiedAt),
            }
            session = get_session()
            response = session.post(
                f"{PUBLIC_BACKEND_URL}/internal/api/llm/managedDatastores/computeDsForSync".replace(
                    "/api/v1", ""
                ),
                json=data,
                headers=headers,
                timeout=int(
                    os.environ.get("PROCEDURES__WORKERS__REQUEST__TIMEOUT", "10")
                ),
            )
            logger.debug("Made request to backend api.")
            # logger.debug(response)
            logger.debug(response.text)
            response.raise_for_status()
            return response.json()["managedDsToBeSynced"]
        except requests.exceptions.Timeout:
            logger.error("Request to backend api timed out.")
        except requests.exceptions.RequestException as e:
            logger.error(f"Request to backend api raised an error {e}")
        return []

    async def get_file_bytes(self, file: FileMeta) -> bytes:
        if self.type == "ThreatConnect":
            return get_file_pdf(ThreatConnectAPIConfig(**self.config), file.id)
        return b""

    async def get_file_metas(self, organizationId) -> tuple[list[FileMeta], datetime]:
        if self.type == "ThreatConnect":
            last_update_date = redis.get(self.redis_key(organizationId))
            if last_update_date:
                last_update_date = last_update_date.decode("utf-8")
            # print("last_update_date: ", last_update_date)
            if last_update_date:
                return get_file_metas(
                    ThreatConnectAPIConfig(**self.config), last_update_date
                )
            return get_file_metas(ThreatConnectAPIConfig(**self.config))
        return ([], None)

    async def get_all_file_metas(self, organizationId) -> list[FileMeta]:
        if self.type == "ThreatConnect":
            file_metas, new_last_update_date = get_file_metas(
                ThreatConnectAPIConfig(**self.config), None, -1
            )
            return file_metas
        return []

    async def sync_file_bytes(
        self,
        file: FileMeta,
        file_bytes: bytes,
        organizationId: str,
        managedDatastores: list[dict],
    ):
        headers = {
            "LLMApiKey": LLM_API_KEY,
            "Content-Type": "application/octet-stream",
            "filename": file.name,
            "organizationid": organizationId,
            "manageddatastores": json.dumps(json.dumps(managedDatastores)),
            "providerdocid": file.id,
            "provider": self.type,
            "lastModifiedAt": str(file.modifiedAt),
        }
        data = []
        try:
            session = get_session()
            response = session.post(
                f"{PUBLIC_BACKEND_URL}/internal/api/llm/managedDatastores/uploadOrReupload".replace(
                    "/api/v1", ""
                ),
                data=file_bytes,
                headers={
                    k: v.encode("utf-8") if isinstance(v, str) else v
                    for k, v in headers.items()
                },
                timeout=int(
                    os.environ.get("PROCEDURES__WORKERS__REQUEST__TIMEOUT", "10")
                ),
            )
            data = response.json()
            logger.info("Made sync request to backend api.")
            logger.info(response.text)
            response.raise_for_status()
        except requests.exceptions.Timeout:
            logger.error("Request to backend api timed out.")
        except requests.exceptions.RequestException as e:
            logger.error(f"Request to backend api raised an error {e}")

        for file_info in data:
            logger.info(f"Uploading document to datastore: {file_info}")
            if (
                not isinstance(file_info, dict)
                or "fileS3Key" not in file_info
                or "datastoreDynamoId" not in file_info
            ):
                continue
            key = file_info["fileS3Key"]
            dataStoreId = file_info["datastoreDynamoId"]
            body = DataStoreDocument(
                organizationId=organizationId,
                dataStoreId=dataStoreId,
                documents=[key],
                documentType="pdf",
                userId="",
                publishDate=file.publishDate,
            )

            try:
                DataStoreManager().upload_document(body=body)
            except Exception as e:
                logger.error(f"Error uploading document {file_info} to datastore: {e}")

    @monitor_long_request(max_time_seconds=300)
    async def sync(self, organizationId: str):
        file_metas, new_last_update_date = await self.get_file_metas(organizationId)
        for file in file_metas:
            logger.info("Processing file %s with ID %s", file.name, file.id)
            managedDatastores = await self.managed_ds_to_be_synced(file, organizationId)
            if managedDatastores:
                logger.debug(
                    "!@!@!@! Syncing file %s with ID %s to datastores %s",
                    file.name,
                    file.id,
                    managedDatastores,
                )
                import traceback

                try:
                    file_bytes = await self.get_file_bytes(file)
                    logger.debug("PDF was downloaded.")
                    await self.sync_file_bytes(
                        file, file_bytes, organizationId, managedDatastores
                    )
                    logger.debug("File was synced.")
                except Exception as e:
                    logger.debug(f"An error occurred: {e}")
                    logger.debug(traceback.format_exc())

        if new_last_update_date:
            new_last_update_date = new_last_update_date + timedelta(seconds=1)
            modified_at_format = new_last_update_date.strftime("%Y-%m-%dT%H:%M:%SZ")
            redis.set(self.redis_key(organizationId), modified_at_format)
            # print(f"new_last_update_date: {modified_at_format}"

    async def reset_sync(self, organizationId: str):
        redis.delete(self.redis_key(organizationId))
        return {"message": "Sync reset for organization"}

    @monitor_long_request(max_time_seconds=600)
    async def handle_delete(self, organizationId: str):
        file_metas = await self.get_all_file_metas(organizationId)
        snapshot = []
        for file in file_metas:
            datastore_names = self.datastore_names_for(
                file, organizationId, self.config
            )
            datastore_names = [
                datastore_name for datastore_name in datastore_names if datastore_name
            ]
            data = {
                "managedDsNames": datastore_names,
                "providerDocId": file.id,
            }
            snapshot.append(data)

        headers = {"LLMApiKey": LLM_API_KEY}

        session = get_session()
        response = session.post(
            f"{PUBLIC_BACKEND_URL}/internal/api/llm/managedDatastores/analyzeSnapshot".replace(
                "/api/v1", ""
            ),
            json={
                "organizationId": organizationId,
                "provider": self.type,
                "snapshot": snapshot,
            },
            headers=headers,
            timeout=int(os.environ.get("PROCEDURES__WORKERS__REQUEST__TIMEOUT", "10")),
        )

        print("response: ", response.json())

        data = response.json()
        to_delete = data.get("toDelete", [])

        for file_info in to_delete:
            organizationId = file_info["organizationId"]
            dataStoreId = file_info["dataStoreId"]
            fileS3Key = file_info["fileS3Key"]

            document = DataStoreDocument(
                organizationId=organizationId,
                dataStoreId=dataStoreId,
                documents=[fileS3Key],
                documentType="pdf",
                userId="",
            )

            try:
                DataStoreManager().delete_document(document)
            except Exception as e:
                logger.debug(f"An error occurred: {e}")
                logger.debug(traceback.format_exc())
