from fastapi import APIRouter
from fastapi.templating import <PERSON><PERSON><PERSON><PERSON>emplates
import os
from llm_api.integrations.managed_data_store import ManagedDataStore

router = APIRouter()
templates = Jinja2Templates(directory="llm_api/eval/templates")

@router.get("/integrations/sync/rbc")
async def get_integrations():
  organizationId = os.getenv("RBC_TC_SYNC_ORG_ID")
  managed_data_store = ManagedDataStore(type="ThreatConnect", config={
      "api_base_url": os.getenv("RBC_TC_SYNC_API_BASE_URL"),
      "api_access_id": os.getenv("RBC_TC_SYNC_API_ACCESS_ID"),
      "api_secret_key": os.getenv("RBC_TC_SYNC_API_SECRET_KEY")
  })
  return await managed_data_store.sync(organizationId=organizationId)


@router.get("/integrations/handleDelete/rbc")
async def get_integrations():
  organizationId = os.getenv("RBC_TC_SYNC_ORG_ID")
  managed_data_store = ManagedDataStore(type="ThreatConnect", config={
      "api_base_url": os.getenv("RBC_TC_SYNC_API_BASE_URL"),
      "api_access_id": os.getenv("RBC_TC_SYNC_API_ACCESS_ID"),
      "api_secret_key": os.getenv("RBC_TC_SYNC_API_SECRET_KEY")
  })
  return await managed_data_store.handle_delete(organizationId=organizationId)

@router.get("/integrations/resetSync/rbc")
async def get_integrations():
  organizationId = os.getenv("RBC_TC_SYNC_ORG_ID")
  managed_data_store = ManagedDataStore(type="ThreatConnect", config={
      "api_base_url": os.getenv("RBC_TC_SYNC_API_BASE_URL"),
      "api_access_id": os.getenv("RBC_TC_SYNC_API_ACCESS_ID"),
      "api_secret_key": os.getenv("RBC_TC_SYNC_API_SECRET_KEY")
  })
  return await managed_data_store.reset_sync(organizationId=organizationId)