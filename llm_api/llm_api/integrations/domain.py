from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel


class FileMeta(BaseModel):
    id: str
    name: str
    tags: List[str]
    modifiedAt: datetime
    time: Optional[datetime] = None
    publishDate: Optional[str] = None

    def dict(self):
        return {
            "id": self.id,
            "name": self.name,
            "tags": self.tags,
            "time": str(self.time) if self.time else "",
            "modifiedAt": str(self.modifiedAt),
            "publishDate": self.publishDate,
        }
