import datetime
import logging
import os
from typing import Any, Dict, List, Tuple
from uuid import UUID

import pytz
from langchain.agents import <PERSON><PERSON><PERSON><PERSON><PERSON>, create_openai_functions_agent
from langchain.chains import Convers<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>
from langchain_core.agents import <PERSON><PERSON><PERSON>, AgentFinish
from langchain_core.callbacks import BaseCallbackHand<PERSON>
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_core.prompts import (
    ChatPromptTemplate,
    HumanMessagePromptTemplate,
    MessagesPlaceholder,
    PromptTemplate,
)
from langchain_core.tools import BaseTool
from pydantic import Extra, root_validator

from llm_api.blai_llm.blai_agents import BlaiConversationalChatAgent
from llm_api.blai_llm.blai_memory import get_memory
from llm_api.blai_llm.utils import PlanLogger, sanitize_tool_name, sanitize_tool_names
from llm_api.callbacks import Logging<PERSON>allback<PERSON>andler
from llm_api.exceptions import ContentFilteringError
from llm_api.llm.constants import AzureOpenAIErrors
from llm_api.llm.factory import get_model_from_spec
from llm_api.mixins import LLMPromptTruncationMixin
from llm_api.parsers.bl_parser import BLOutputParser
from llm_api.specs.coordinator_spec import CoordinatorSpec
from llm_api.utils import get_feature_flag_value

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

USE_OPENAI_FUNCTION_CALLING = (
    os.environ.get("USE_OPENAI_FUNCTION_CALLING", "False") == "True"
)


class Coordinator(LLMPromptTruncationMixin):
    def __init__(
        self,
        spec: CoordinatorSpec,
        tools: List[BaseTool],
        conversation_id: str,
        plan_id: UUID,
        logging_cb: LoggingCallbackHandler,
        streaming_cb: BaseCallbackHandler = None,
        has_redis_memory: bool = False,
        local_timezone: str = None,
    ):
        self.spec = spec
        self.spec.name = sanitize_tool_name(self.spec.name)
        self.plan_id = plan_id
        self.logging_cb = logging_cb

        self.llm = get_model_from_spec(self.spec.llm, streaming_cb=streaming_cb)

        custom_parser = BLOutputParser()

        self.tools = sanitize_tool_names(tools)

        if has_redis_memory:
            self.conversation_memory = get_memory(conversation_id)
        else:
            self.conversation_memory = get_memory(None)

        if USE_OPENAI_FUNCTION_CALLING:
            timezone = local_timezone if local_timezone else "America/New_York"
            tz = pytz.timezone(timezone)
            today = datetime.datetime.now(tz)
            today_str = today.strftime("%Y-%m-%d %A")
            agent_obj = create_openai_functions_agent(
                llm=self.llm,
                tools=self.tools or [],
                prompt=ChatPromptTemplate(
                    [
                        SystemMessage(content=self.spec.prompt),
                        SystemMessage(
                            content=(f"For context the current date is {today_str}.\n")
                        ),
                        SystemMessage(
                            content=(
                                "YOU ALWAYS USE TOOLS to gather the necessary information and perform the necessary actions before your answer the user's question. You should only answer the user using information you retrieved using tools. Don't use your own knowledge to answer, you should rely exclusively on tools.\n"
                                "Don't reference any files retrieved by the tools. Just use the information you retrieved to answer the user.\n"
                                "CRITICAL RULE: As a rule of thumb, if you have tools, you must use them before you answer and if you don't have enough information to answer the user, you should use more tools to retrieve more information.\n"
                                "# Tool Usage Instructions\n"
                                "For every question you need to use at least one tool. You are not allowed to give a final answer before using at least one tool.\n"
                                "You MUST use the available tools to answer the question.\n"
                                "When using tools you should pass the question from the user exactly as it is to the tool when the tool allows you to do that - you should not miss any of the details.\n"
                                "Always use the relevant tools to retrieve information or perform actions before giving a final answer.\n"
                                "IMPORTANT: Provide a final answer ONLY after utilizing at least one tool.\n"
                                "If multiple tools are applicable, USE THEM ALL before concluding.\n"
                                "For information retrieval tools, ensure all are used prior to answering.\n"
                            )
                        ),
                        SystemMessage(
                            content=(
                                "CSV data wrapped in [CSV] should be present in the conversation as they are the system know how to interpret them.\n"
                                "Pass CSV tags from tools to the user in the final answer as they are.\n"
                                "For example if a tool returns a CSV file like this:\n"
                                '[CSV s3_key="foo"]\nBar\n123\n[/CSV]\n'
                                "You should pass it to the user as is, without any modifications, like this.\n"
                                '[CSV s3_key="foo"]\nBar\n123\n[/CSV]\n'
                                "You should not modify the CSV tags in any way.\n"
                                "You should not reference the CSV tags in your answer.\n"
                                "You should not make extra references to the s3_key in your answer.\n"
                                "Don't give the user a link to the CSV file. The link is not accessible to the user.\n"
                            )
                        ),
                        MessagesPlaceholder("chat_history", optional=True),
                        HumanMessagePromptTemplate.from_template("{input}"),
                        MessagesPlaceholder("agent_scratchpad"),
                    ]
                ),
            )
        else:
            agent_obj = BlaiConversationalChatAgent.from_llm_and_tools(
                tools=self.tools,
                llm=self.llm,
                output_parser=custom_parser,
            )

        self.agent_executor = BlaiCustomAgentExecutor.from_agent_and_tools(
            agent=agent_obj,
            tools=self.tools,
            max_iterations=int(os.getenv("MAX_AGENT_ITERATIONS", "20")),
            tool_choice="any",
            early_stopping_method=os.getenv("AGENT_EARLY_STOPPING_METHOD", "force"),
            memory=self.conversation_memory,
            return_intermediate_steps=True,
            return_source_documents=True,
            handle_parsing_errors=True,
            verbose=True,
        )

        if not USE_OPENAI_FUNCTION_CALLING:
            # tell the agent to use our tools
            tools_prompt = self.agent_executor.agent.create_prompt(
                system_message=self.spec.prompt, tools=self.tools
            )
            self.agent_executor.agent.llm_chain.prompt = tools_prompt

    async def acall(
        self, input: dict, callbacks: List[BaseCallbackHandler] | None = None
    ):
        truncated_input: str = self.truncate_context(input["input"])
        if len(truncated_input) < len(input):
            log = PlanLogger()
            log.addToLog(f"Input for {self.spec.name} has been truncated", self.plan_id)
            logger.warning(
                "Input for %s has been truncated. New input: %s",
                self.spec.name,
                truncated_input,
            )
            input["input"] = truncated_input

        try:
            if self.tools:
                logger.debug(f"Coordinator.acall Using tools: {self.tools}")
                return await self.agent_executor.ainvoke(input, callbacks=callbacks)
            else:
                logger.debug(f"Coordinator.acall NOT using tools: {self.tools}")
                today_str = datetime.datetime.now().strftime("%Y-%m-%d")
                chain_params = {
                    "llm": self.llm,
                    "prompt": PromptTemplate(
                        input_variables=["input"],
                        template=(
                            f"{self.spec.prompt}.\n"
                            f"For context the current date is {today_str}. You can use this information when processing user queries.\n"
                            "Here is the question: \n{input}"
                        ),
                        validate_template=False,
                    ),
                    "callbacks": [self.logging_cb],
                    "output_key": "output",
                }
                chain_cls = LLMChain

                if self.conversation_memory:
                    chain_params["memory"] = self.conversation_memory
                    chain_params["prompt"] = PromptTemplate(
                        input_variables=["chat_history", "input"],
                        template=self.spec.prompt
                        + f"For context the current date is {today_str}. You can use this information when processing user queries.\n"
                        + "\nPrevious conversation:\n {chat_history}.\nHere is the question: \n{input}",
                    )
                    chain_cls = ConversationChain

                chain = chain_cls(**chain_params)
                return await chain.acall(
                    input,
                    callbacks=[self.logging_cb],
                )
        except ValueError as err:
            match str(err):
                case AzureOpenAIErrors.AZURE_CONTENT_FILTER_TRIGGERED:
                    if get_feature_flag_value("RAISE_SPECIFIC_CONTENT_FILTERING_ERROR"):
                        raise ContentFilteringError(
                            AzureOpenAIErrors.AZURE_CONTENT_FILTER_TRIGGERED.name
                        )
                    raise err
                case _:
                    raise err


class BlaiCustomAgentExecutor(AgentExecutor):
    def return_stopped_response(
        self,
        early_stopping_method: str,
        intermediate_steps: List[Tuple[AgentAction, str]],
        **kwargs: Any,
    ) -> AgentFinish:
        """Return response when agent has been stopped due to max iterations."""
        if early_stopping_method == "force":
            # `force` just returns a constant string
            return AgentFinish(
                {"output": "Agent stopped due to iteration limit or time limit."}, ""
            )
        elif early_stopping_method == "generate":
            # Generate does one final forward pass
            thoughts = ""
            for action, observation in intermediate_steps:
                thoughts += action.log
                thoughts += (
                    f"\n{self.observation_prefix}{observation}\n{self.llm_prefix}"
                )
            # Adding to the previous steps, we now tell the LLM to make a final prediction
            thoughts += (
                "\n\nI now need to return a final answer based on the previous steps:"
            )
            # thoughts should not be a list of strings
            thoughts = [HumanMessage(content=thoughts)]
            new_inputs = {"agent_scratchpad": thoughts, "stop": self._stop}
            full_inputs = {**kwargs, **new_inputs}
            full_output = self.llm_chain.predict(**full_inputs)
            # We try to extract a final answer
            try:
                parsed_output = self.output_parser.parse(full_output)
            except OutputParserException:
                return AgentFinish({"output": full_output}, full_output)
            if isinstance(parsed_output, AgentFinish):
                # If we can extract, we send the correct stuff
                return parsed_output
            else:
                # If we cannot extract a final answer, return the full output
                return AgentFinish({"output": full_output}, full_output)
        else:
            raise ValueError(
                "early_stopping_method should be one of `force` or `generate`, "
                f"got {early_stopping_method}"
            )


class CoordinatorTool(BaseTool):
    coordinator: Coordinator

    @root_validator(pre=True)
    def parse_params(cls, values: Dict[str, Any]) -> Dict[str, Any]:
        values["coordinator"] = Coordinator(**values)

        values["name"] = sanitize_tool_name(values["spec"].name)
        values["description"] = values["spec"].description

        return values

    class Config:
        extra = Extra.allow

    def _run(self, question: str):
        return NotImplementedError(
            f"You are calling a sync method on {self.__class__}. Please don't!"
        )

    async def _arun(self, question: str):
        log = PlanLogger()
        log_text = f"Used '{self.name}' with the input '{question}'"
        log.addToLog(log_text, self.plan_id)

        logger.debug(f"CoordinatorTool.arun question: {question}")
        output = await self.coordinator.acall(
            {"input": question}, callbacks=[self.coordinator.logging_cb]
        )

        all_sources = []
        all_apis = []
        all_evidence = []
        intermediate_steps = output.get("intermediate_steps")
        if intermediate_steps:
            try:
                for interm_step in intermediate_steps:
                    if "source_documents" in interm_step[1].keys():
                        source_docs = interm_step[1]["source_documents"]
                        all_sources.extend(source_docs)
                    if "source_api" in interm_step[1].keys():
                        source_api = interm_step[1]["source_api"]
                        all_apis.extend(source_api)
                    if "evidence" in interm_step[1].keys():
                        evidence = interm_step[1]["evidence"]
                        all_evidence.extend(evidence)
            except Exception as error:
                logger.error(f"aresearch {error}")
                pass

        reply = {
            "question": question,
            "answer": output["output"] if isinstance(output, dict) else output,
        }
        reply["source_documents"] = all_sources
        reply["source_api"] = all_apis
        reply["evidence"] = all_evidence

        log_text = f"'{self.name}' is done"
        log.addToLog(log_text, self.plan_id)

        return reply
