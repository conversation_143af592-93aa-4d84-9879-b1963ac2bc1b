import logging
import os
import tempfile
from typing import List

from langchain_community.document_loaders import (PyPDFLoader,
                                                  S3DirectoryLoader,
                                                  S3FileLoader, TextLoader                                                  )
from langchain_unstructured import UnstructuredLoader
from langchain_core.document_loaders import Blob
from langchain_core.documents import Document
from unstructured.cleaners.core import clean_extra_whitespace

import llm_api.eval.metrics
from llm_api.blai_api.dtos import FileBytes
from llm_api.blai_llm.blai_parsers import CustomPyPDFParser

# prevent log poisoning from PDF parsing
logging.getLogger("langchain_community.document_loaders").setLevel(logging.CRITICAL)
logging.getLogger("pikepdf").setLevel(logging.CRITICAL)
logging.getLogger("unstructured").setLevel(logging.CRITICAL)


class CustomPyPDFLoader(PyPDFLoader):
    def __init__(self, file_path: str) -> None:
        """Initialize with file path."""
        try:
            import pypdf  # noqa:F401
        except ImportError:
            raise ImportError(
                "pypdf package not found, please install it with " "`pip install pypdf`"
            )
        super().__init__(file_path)
        # need to use the custom PyPDFParser to add the filename hash to metadata
        # which will be used when querying for which documents to delete from the vector database
        self.parser = CustomPyPDFParser()


class S3UnstructuredDocumentLoader(S3FileLoader):
    def load(self) -> List[Document]:
        """Load documents."""
        try:
            import boto3
        except ImportError:
            raise ImportError(
                "Could not import `boto3` python package. "
                "Please install it with `pip install boto3`."
            )
        s3 = boto3.client("s3")
        with tempfile.TemporaryDirectory() as temp_dir:
            file_path = f"{temp_dir}/{self.key}"
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            s3.download_file(self.bucket, self.key, file_path)

            loader = UnstructuredLoader(
                file_path=file_path,
                post_processors=[clean_extra_whitespace],
            )

            # prevent log poisoning from PDF parsing
            logger = logging.getLogger()
            previous_level = logger.getEffectiveLevel()
            logger.setLevel(logging.CRITICAL)
            try:
                documents = loader.load()
            finally:
                # Restore the original logging level
                logger.setLevel(previous_level)

            return documents


class BinaryFileLoader:
    def __init__(self, file: FileBytes) -> None:
        self.file = file

    def load(self) -> List[Document]:
        with tempfile.TemporaryDirectory() as temp_dir:
            file_path = f"{temp_dir}/{self.file.file_name}"
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            with open(file_path, "wb") as f:
                f.write(self.file.file_bytes)

            loader = UnstructuredFileLoader(
                file_path,
                post_processors=[clean_extra_whitespace],
            )

            # prevent log poisoning from PDF parsing
            logger = logging.getLogger()
            previous_level = logger.getEffectiveLevel()
            logger.setLevel(logging.CRITICAL)
            try:
                documents = loader.load()
            finally:
                # Restore the original logging level
                logger.setLevel(previous_level)

            return documents


class S3PdfDirectoryLoader(S3DirectoryLoader):
    def load(self) -> List[Document]:
        """Load documents."""
        try:
            import boto3
        except ImportError:
            raise ImportError(
                "Could not import boto3 python package. "
                "Please install it with `pip install boto3`."
            )
        s3 = boto3.resource("s3")
        bucket = s3.Bucket(self.bucket)
        docs = []
        for obj in bucket.objects.filter(Prefix=self.prefix):
            loader = S3UnstructuredDocumentLoader(self.bucket, obj.key)
            docs.extend(loader.load())
        return docs


class S3FileTextLoader(S3FileLoader):
    def load(self) -> List[Document]:
        """Load documents."""
        try:
            import boto3
        except ImportError:
            raise ImportError(
                "Could not import `boto3` python package. "
                "Please install it with `pip install boto3`."
            )
        s3 = boto3.client("s3")
        with tempfile.TemporaryDirectory() as temp_dir:
            file_path = f"{temp_dir}/{self.key}"
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            s3.download_file(self.bucket, self.key, file_path)
            loader = TextLoader(file_path)
            return loader.load()


class S3DirectoryTextLoader(S3DirectoryLoader):
    def load(self) -> List[Document]:
        """Load documents."""
        try:
            import boto3
        except ImportError:
            raise ImportError(
                "Could not import boto3 python package. "
                "Please install it with `pip install boto3`."
            )
        s3 = boto3.resource("s3")
        bucket = s3.Bucket(self.bucket)
        docs = []
        for obj in bucket.objects.filter(Prefix=self.prefix):
            loader = S3FileTextLoader(self.bucket, obj.key)
            docs.extend(loader.load())
        return docs
