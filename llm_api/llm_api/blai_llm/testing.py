import json

from langchain_core.messages import SystemMessage

from llm_api.blai_api.dtos import TestAssertion
from llm_api.llm.factory import default_4_gpt_spec_data, get_model_from_spec
from llm_api.specs.llm_spec import LLMSpec, LLMType


def test_assertion(test: TestAssertion, retry: bool = True):
    spec = LLMSpec(
        type=LLMType.AzureChatOpenAI,
        data=default_4_gpt_spec_data,
        json_mode_enabled=True,
    )
    llm = get_model_from_spec(spec)
    messages = [
        SystemMessage(
            content=f"""
    # Goal

    You goal is to test the following assertion:

    {test.assertion}

    # Context
         
    Based on the following context:

    {test.context}

    # Output format 

    You should output a valid JSON object with the following format:

    {{
        "reasoning": string, // The step by step reasoning behind the assertion
        "result": boolean, // The result of the assertion
    }}
"""
        )
    ]

    response = llm.invoke(messages)

    try:
        data = json.loads(response.content)
        return {
            "success": True,
            "result": data["result"],
            "reasoning": data["reasoning"],
        }
    except Exception as e:
        if retry:
            return test_assertion(test, retry=False)
        return {
            "error": "Failed to parse the response",
            "result": False,
        }
