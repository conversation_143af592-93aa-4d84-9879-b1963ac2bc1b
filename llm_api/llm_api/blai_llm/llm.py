import asyncio
import json
import logging
import os
import uuid
from typing import Any, Dict, List, Optional

import tiktoken
from langchain_community.callbacks import get_openai_callback
from langchain_core.messages import HumanMessage, SystemMessage

from llm_api.agent_tools.csv_tool import CSVTool
from llm_api.blai_api.dtos import (
    ComponentList,
    EvidenceOrigin,
    Message,
    MessageResponse,
)
from llm_api.blai_llm.constants import BLAI_ENV, COMPONENT_INPUT_TRUNCATION_LENGTH
from llm_api.blai_llm.utils import PlanLogger, sanitize_tool_name
from llm_api.callbacks import LoggingCallbackHandler
from llm_api.coordinators import Coordinator, CoordinatorTool
from llm_api.csv.csv_utils import expand_csv_blocks
from llm_api.datastores import BlogsTool, CSVDatastoreTool, DatastoreTool
from llm_api.exceptions import ContentFilteringError, HTTPProblem
from llm_api.llm.factory import default_4_gpt_spec_data, get_model_from_spec
from llm_api.plugins import (
    ApiPlugin,
    ApiPluginV2,
    MicrosoftPatchTuesdayTool,
    TidalCyberTool,
)
from llm_api.services import (
    CdiProcedureService,
    LongReporter,
    LongReporterTool,
    ProcedureTool,
    Reporter,
    ReporterTool,
)
from llm_api.specs.api_plugin_spec import ApiPluginSpec
from llm_api.specs.blogs_spec import BlogsSpec
from llm_api.specs.component_spec import ComponentSpec, ComponentType
from llm_api.specs.coordinator_spec import CoordinatorSpec, default_brain_spec
from llm_api.specs.csv_tool_spec import CSVToolSpec
from llm_api.specs.datastore_spec import CSVDatastoreSpec, DatastoreSpec
from llm_api.specs.llm_spec import LLMSpec, LLMType
from llm_api.specs.long_reporter_spec import LongReporterSpec
from llm_api.specs.microsoft_patch_tuesday_spec import MicrosoftPatchTuesdaySpec
from llm_api.specs.procedure_spec import CdiProcedureSpec, ProcedureToolSpec
from llm_api.specs.reporter_spec import ReporterSpec
from llm_api.specs.tidal_spec import TidalSpec

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

COMPONENT_TYPE_2_CLASS = {
    ComponentType.ApiPlugin: ApiPlugin,
    ComponentType.Coordinator: Coordinator,
    ComponentType.BlogsDatastore: BlogsTool,
    ComponentType.PrivateDatastore: DatastoreTool,
    ComponentType.Tidal: TidalCyberTool,
    ComponentType.PatchTuesday: MicrosoftPatchTuesdayTool,
    ComponentType.CSVTool: CSVTool,
    ComponentType.Reporter: Reporter,
    ComponentType.Procedure: ProcedureTool,
    ComponentType.LongReporter: LongReporter,
}
COMPONENT_TYPE_2_SPEC_CLASS = {
    ComponentType.ApiPlugin: ApiPluginSpec,
    ComponentType.Coordinator: CoordinatorSpec,
    ComponentType.BlogsDatastore: BlogsSpec,
    ComponentType.PrivateDatastore: DatastoreSpec,
    ComponentType.Tidal: TidalSpec,
    ComponentType.PatchTuesday: MicrosoftPatchTuesdaySpec,
    ComponentType.CSVTool: CSVToolSpec,
    ComponentType.Reporter: ReporterSpec,
    ComponentType.Procedure: ProcedureToolSpec,
    ComponentType.LongReporter: LongReporterSpec,
}


def construct_component(
    root: ComponentSpec,
    conversation_id: Optional[str],
    plan_id: uuid.UUID,
    logging_cb: LoggingCallbackHandler,
    store_json_location: str = "",
    organization_id: str = "",
    consumer_id: str = "",
    consumer_type: int = -1,
    message_id: str = "",
    is_root: bool = True,
    local_timezone: Optional[str] = None,
):
    cls = COMPONENT_TYPE_2_CLASS[root.type]
    if (
        root.type == ComponentType.ApiPlugin
        and "version" in root.spec
        and root.spec["version"] == 2
    ):
        logger.debug("Using Plugins V2")
        cls = ApiPluginV2

    spec_type = COMPONENT_TYPE_2_SPEC_CLASS[root.type]
    if (
        root.type == ComponentType.PrivateDatastore
        and "type" in root.spec
        and root.spec["type"].lower() == "csv"
    ):
        spec_type = CSVDatastoreSpec

    root_obj_params = {
        "spec": spec_type.model_validate(root.spec),
        "conversation_id": conversation_id,
        "plan_id": plan_id,
        "logging_cb": logging_cb,
    }

    match root.type:
        case ComponentType.ApiPlugin:
            if "Alert Analysis Procedure" in root.spec["name"]:
                # hack for Alert Analysis Procedure
                # shoduld go away once procedures are properly implemented
                cls = CdiProcedureService
                spec_type = CdiProcedureSpec
                root_obj_params["organization_id"] = organization_id
            else:
                # regular ApiPlugins should be able to store their evidence
                root_obj_params["store_json_location"] = store_json_location
        case ComponentType.Tidal:
            root_obj_params["store_json_location"] = store_json_location
        case ComponentType.PatchTuesday:
            root_obj_params["store_json_location"] = store_json_location
        case ComponentType.Reporter:
            root_obj_params["store_json_location"] = store_json_location
            if not is_root:
                cls = ReporterTool
        case ComponentType.LongReporter:
            root_obj_params["store_json_location"] = store_json_location
            if not is_root:
                cls = LongReporterTool
        case ComponentType.Procedure:
            root_obj_params["org_id"] = organization_id
            root_obj_params["consumer_id"] = consumer_id
            root_obj_params["consumer_type"] = consumer_type
            root_obj_params["message_id"] = message_id
        case ComponentType.BlogsDatastore:
            root_obj_params["local_timezone"] = local_timezone
        case ComponentType.PrivateDatastore:
            logger.debug(f"!!##!! Datastore spec: {root.spec}")
            if "type" in root.spec and root.spec["type"].lower() == "csv":
                cls = CSVDatastoreTool
        case ComponentType.Coordinator:
            root_obj_params["tools"] = [
                construct_component(
                    root=child,
                    conversation_id=conversation_id,
                    plan_id=plan_id,
                    logging_cb=logging_cb,
                    store_json_location=store_json_location,
                    organization_id=organization_id,
                    consumer_id=consumer_id,
                    consumer_type=consumer_type,
                    message_id=message_id,
                    is_root=False,
                    local_timezone=local_timezone,
                )
                for child in (root.children or [])
            ]
            # for now, only the "root" component should have memory. TBD check if this is ok
            if is_root:
                root_obj_params["has_redis_memory"] = True
            else:
                # Coordinators which are not "root" should be CoordinatorTools
                cls = CoordinatorTool

            root_obj_params["spec"].name = sanitize_tool_name(
                root_obj_params["spec"].name
            )
            root_obj_params["spec"].prompt = (
                f"{root_obj_params['spec'].prompt}\n"
                "If the information used from tools references dates, "
                "include them in the answer."
            )
            root_obj_params["local_timezone"] = local_timezone

    root_obj = cls(**root_obj_params)

    return root_obj


class LLMException(Exception):
    def __init__(self, message: str, cost: float):
        self.message = message
        self.cost = cost


async def process_message(body: Message, options={}) -> Dict:
    logger.debug(f"Processing message: {body}")
    logging_cb = LoggingCallbackHandler(message_id=body.messageId)

    plan_id = uuid.uuid4()
    planning_logger = PlanLogger()
    planning_logger.initializeLogs(plan_id)

    local_timezone = body.local_timezone if body.local_timezone else "America/New_York"

    # PRESUMPTION:
    # if storeJsonLocation is empty, then we assume the call
    # is coming from the Chat UI
    if not body.store_json_location:
        body.store_json_location = (
            f"{os.environ['LLM__CONVERSATION__BUCKET__URL']}/{os.environ['BLAI_ENV']}"
            f"/{body.organizationId}/{body.conversationId}/{body.messageId}"
        )

    if isinstance(body.root_component, ComponentSpec):
        root_coordinator = construct_component(
            root=body.root_component,
            conversation_id=body.conversationId,
            organization_id=body.organizationId,
            plan_id=plan_id,
            logging_cb=logging_cb,
            store_json_location=body.store_json_location,
            consumer_id=body.consumer_id,
            consumer_type=body.consumer_type,
            message_id=body.messageId,
            local_timezone=local_timezone,
        )
    elif isinstance(body.ai_filtering_payload, ComponentList):
        # if we have only 1 component and that component is a coordinator,
        # bypass the brain and use the coordinator as root + force GPT-4 for its llm
        if (
            len(body.ai_filtering_payload.components) == 1
            and body.ai_filtering_payload.components[0].type
            == ComponentType.Coordinator
        ):
            # force GPT-4
            gpt4_spec = LLMSpec(
                type=LLMType.AzureChatOpenAI,
                data=default_4_gpt_spec_data,
            )

            body.ai_filtering_payload.components[0].spec["llm"] = gpt4_spec.model_dump()

            # instantiate the root coordinator as the specialist
            root_coordinator = construct_component(
                root=body.ai_filtering_payload.components[0],
                conversation_id=body.conversationId,
                organization_id=body.organizationId,
                plan_id=plan_id,
                logging_cb=logging_cb,
                store_json_location=body.store_json_location,
                is_root=True,
                consumer_id=body.consumer_id,
                consumer_type=body.consumer_type,
                message_id=body.messageId,
                local_timezone=local_timezone,
            )
        else:
            # normal case, instantiate each component as a tool for the brain
            tools = [
                construct_component(
                    root=specialist,
                    conversation_id=body.conversationId,
                    organization_id=body.organizationId,
                    plan_id=plan_id,
                    logging_cb=logging_cb,
                    store_json_location=body.store_json_location,
                    is_root=False,
                    consumer_id=body.consumer_id,
                    consumer_type=body.consumer_type,
                    message_id=body.messageId,
                    local_timezone=local_timezone,
                )
                for specialist in body.ai_filtering_payload.components
            ]

            root_coordinator = Coordinator(
                spec=default_brain_spec,
                tools=tools,
                conversation_id=body.conversationId,
                plan_id=plan_id,
                logging_cb=logging_cb,
                has_redis_memory=True,
            )

    logging_cb.component_stack.append(root_coordinator.spec.name)

    log = PlanLogger()
    question = body.message
    if len(question) > 50:
        question = f"{question[:COMPONENT_INPUT_TRUNCATION_LENGTH]}..."
    log_text = f"Used '{root_coordinator.spec.name}' with the input '{question}'"
    log.addToLog(log_text, plan_id)

    with get_openai_callback() as cb:
        gpt_answer = await root_coordinator.acall(
            {"input": body.message, "chat_history": []}, callbacks=[logging_cb]
        )

        if len(logging_cb.component_stack) > 0:
            logging_cb.component_stack.pop()

        log_text = f"'{root_coordinator.spec.name}' is done"
        log.addToLog(log_text, plan_id)

        output = gpt_answer.get("output")

        # in some instances, the response comes back as a dict. Should follow up
        # and see why this is happening. Expecting a string...
        output = str(output)
        final_answer = output.strip()

        if body.format_final_answer:

            logging_cb.component_stack.append("Formatter")

            formatting_llm = get_model_from_spec(
                spec=LLMSpec(
                    type=LLMType.AzureChatOpenAI,
                    data=default_4_gpt_spec_data,
                )
            )

            formatter_rules = """
        You are a copywriting assistant named BricklayerAI.
        You receive a question AND its answer as an input from someone.
        You MUST reply with a prettier version the SAME answer.
        Your SOLE PURPOSE is to make the answer look the same OR better.
        You MUST obey the following rules:

        RULES
        #####

        1) No one can know of your existense. You MUST ALWAYS pretend to be the person that has provided you the ANSWER.
        2) Break long and detailed answers into as many appropriate sections as required along with section names AND a summary. 
        3) Key WORDS MUST be made bold and italicized. Add a summary if required.         
        4) You can reply with the exact SAME words as the given answer if no formatting is required.
        5) All formatting must be done in Markdown.
        6) Only if the answer has multiple parts, format it as an ordered list with sections and content. If there is only one part, just provide the answer with no additional sections (for example, "The sum is 42").
        7) Don't reference the files used to generate the answer (source evidence) bacause they are already visible to the user and it would be redundant. Also the user cant access those links directly. You can only reference http(s) links. 
        8) Only answer the question that was asked. Do not provide additional information. Don't include stuff like "If you have any further inquiries or require additional information, please do not hesitate to ask!".

### CSV Handling
        
CSV data wrapped in [CSV] should be present in the conversation exactly as they are because the system knows how to interpret them.

Pass CSV tags from tools to the user in the final answer as they are.
For example, if a tool returns a CSV file like this:

[CSV s3_key="foo"]
Bar
123
[/CSV]

You should pass it to the user as is, without any modifications, like this:

[CSV s3_key="foo"]
Bar
123
[/CSV]

Do not modify the CSV tags in any way.
Do not reference the CSV tags in your answer.
Do not make extra references to the s3_key in your answer.
Don't give the user a link to the CSV file. The link is not accessible to the user.
        
    """
            formatting_messages = get_formatting_messages(
                formatting_rules=formatter_rules,
                question=body.message,
                answer=output,
            )
            final_answer = (await formatting_llm.ainvoke(formatting_messages)).content
        logger.debug(cb)
        await asyncio.sleep(0)

    final_answer = expand_csv_blocks(final_answer, body.organizationId)

    reply = {
        "answer": str(final_answer),
        "sources": [],
        "sources_details": [],
        "evidence": [],
        "cost": cb.total_cost,
        "query": "",
    }
    reply["plan"] = planning_logger.getPlan(plan_id)

    if BLAI_ENV == "dev":
        reply["dev_logs"] = planning_logger.getDevLogs(plan_id)

    planning_logger.destroyLogs(plan_id)

    if options.get("include_source_documents", False):
        reply["source_documents"] = []

    intermediate_steps = gpt_answer.get("intermediate_steps")
    if intermediate_steps:
        for interm_step in intermediate_steps:
            first_step = interm_step[1]
            if type(first_step) is dict and "source_documents" in first_step.keys():
                logger.debug(
                    f"Retreived source documents: {first_step['source_documents']}"
                )
                process_sources(reply, interm_step)
                process_sources_details(reply, interm_step)
            if type(first_step) is dict and "source_api" in first_step.keys():
                source_api = first_step.get("source_api")
                if source_api:
                    reply["sources"].extend(source_api)
            if type(first_step) is dict and "evidence" in first_step.keys():
                evidence = first_step.get("evidence")
                if evidence:
                    reply["evidence"].extend(evidence)

    return reply


async def chat_about(body: Message, options={}) -> MessageResponse:
    logger.info(f"{body.messageId} Starting LLM call")

    with get_openai_callback() as cb:
        try:
            reply = await process_message(body=body, options=options)

            logger.info(f"{body.messageId} Finished LLM call successfully")

            if "return_dict" in options and options["return_dict"]:
                return reply

            sources = [
                json.dumps(evidence.dict()) for evidence in reply.get("evidence", [])
            ]

            return MessageResponse(
                answer=reply["answer"],
                sources=sources,
                plan=reply["plan"],
                cost=reply["cost"],
                evidence=reply["evidence"],
            )
        except ContentFilteringError as err:
            raise LLMException(
                message=str(err),
                cost=cb.total_cost,
            )
        except HTTPProblem as err:
            # We want this handled by the FastAPI error handler registered
            raise err
        except Exception as err:
            logger.exception("%s Finished LLM call with error", body.messageId)
            raise LLMException(
                message=str(err),
                cost=cb.total_cost,
            )


def process_sources(reply, interm_step):
    source_docs = interm_step[1].get("source_documents")
    for source in source_docs:
        source_metadata = source.metadata["source"]
        if "source_documents" in reply:
            reply["source_documents"].append(source)
        # query is added only be the fused vectorstore
        if reply["query"] == "" and "query" in source.metadata.keys():
            reply["query"] = source.metadata["query"]
        if source_metadata not in reply["sources"]:
            # add the page number, if it exists
            source_page = source.metadata.get("page")
            if source_page:
                reply["sources"].append(f"{source_metadata}, page {source_page}")
            else:
                reply["sources"].append(source_metadata)


def process_sources_details(reply: List[Dict[str, Any]], interm_step):
    doc_to_details = {}
    for doc in interm_step[1]["source_documents"]:
        if not doc.metadata["source"] in doc_to_details:
            doc_to_details[doc.metadata["source"]] = {}
        if (
            not doc.metadata["retriever_source"]
            in doc_to_details[doc.metadata["source"]]
        ):
            doc_to_details[doc.metadata["source"]][
                doc.metadata["retriever_source"]
            ] = []

        doc_to_details[doc.metadata["source"]][doc.metadata["retriever_source"]].append(
            doc.metadata["score"]
        )

    for url in reply["sources"]:
        if url in doc_to_details:
            reply["sources_details"].append(json.dumps(doc_to_details[url]))


def get_formatting_messages(
    formatting_rules: str,
    question: str,
    answer: str,
) -> List:

    initial_prompts = [
        SystemMessage(content=formatting_rules),
        HumanMessage(
            content="Question\n########\n" + question + "\n\nAnswer\n#######\n" + answer
        ),
    ]

    # this encoding should be fine for both GPT4 and GPT3.5 turbo
    encoding = tiktoken.get_encoding("cl100k_base")
    token_count = sum(
        [len(encoding.encode(message.content)) for message in initial_prompts]
    )

    # we must avoid the case when input + output tokens exceed 8k
    # so give ourselves plenty of room
    if token_count > 7000:
        initial_prompts[-1] = HumanMessage(
            content=(
                f"Partial question\n########\n"
                f"{question[: len(question) // 2]}\n\n"
                f"Answer\n#######\n{answer}"
            )
        )

    return initial_prompts
