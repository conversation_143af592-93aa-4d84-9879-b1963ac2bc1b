import hashlib
import logging
import os
import time
from functools import wraps
from typing import Callable, Dict, List, Tuple, Type, Union

from langchain_core.document_loaders import BaseLoader
from langchain_core.documents import Document
from langchain_text_splitters import TextSplitter
from pydantic import BaseModel

logger = logging.getLogger(__name__)


def retry(
    exception: Union[Type[Exception], Tuple[Type[Exception]]],
    tries: int = 3,
    delay: float = 1,
    backoff: float = 2,
):
    """Retry calling the decorated function using an exponential backoff.

    Args:
        exception: The exception or tuple of exceptions to catch.
        tries: Number of times to try (not retry) before giving up.
        delay: Initial delay between retries in seconds.
        backoff: Backoff multiplier (e.g., value of 2 will double the delay each retry).
    """

    def deco_retry(f):
        @wraps(f)
        def f_retry(*args, **kwargs):
            mtries, mdelay = tries, delay
            while mtries > 1:
                try:
                    return f(*args, **kwargs)
                except exception as e:
                    logger.error(f"Retry {tries - mtries + 1}, exception: {e}")
                    time.sleep(mdelay)
                    mtries -= 1
                    mdelay *= backoff
            return f(*args, **kwargs)

        return f_retry

    return deco_retry


ChunkIndex = int
ChunkMetaDataFunction = Callable[[ChunkIndex, Document], Dict[str, str]]
ChunkIDFunction = Callable[[ChunkIndex, Document], str]


class ChunkDocNameIDFunction(BaseModel):
    doc_name: str

    def __call__(self, chunk_index: ChunkIndex, document: Document) -> str:
        doc_name_hash = hashlib.sha256(self.doc_name.encode("utf-8")).hexdigest()
        return f"{doc_name_hash}_{chunk_index}"


class ChunkIndexHashMetaDataFunction(BaseModel):
    doc_name: str

    def __call__(self, chunk_index: ChunkIndex, document: Document) -> Dict[str, str]:
        # Make backwards compatible
        _id = ChunkDocNameIDFunction(doc_name=self.doc_name)(chunk_index, document)

        return {
            "id": _id,
            "s3_path": self.doc_name,
            "source": self.doc_name,
            "filename": os.path.basename(self.doc_name),
        }


def get_split_chunks_with_metadata(
    text_splitter: TextSplitter,
    loader: BaseLoader,
    id_function: ChunkIDFunction,
    metadata_functions: List[ChunkMetaDataFunction],
) -> List[Document]:
    chunks = loader.load_and_split(text_splitter)
    # Modify Metadata
    for idx, chunk in enumerate(chunks):
        for mf in metadata_functions:
            chunk.id = id_function(idx, chunk)
            chunk.metadata |= mf(idx, chunk)
    return chunks
