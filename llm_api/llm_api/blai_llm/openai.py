import os

from langchain_openai.chat_models import AzureChatOpenAI
from langchain_openai import AzureOpenAIEmbeddings
from langchain_openai.llms import AzureOpenAI

### Azure OpenAI Config ###

azure_openai_llm = AzureOpenAI(
    temperature=0,
    azure_deployment=os.environ.get("AZURE_OPENAI_DEPLOYMENT", ""),
    api_version="2023-05-15",
    openai_api_type="azure",
    azure_endpoint=os.environ.get("AZURE_OPENAI_API_BASE", ""),
    api_key=os.environ.get("AZURE_OPENAI_API_KEY", ""),
    # request_timeout=40,
    # max_retries=2
)

azure_openai_embeddings = AzureOpenAIEmbeddings(
    openai_api_type="azure",
    azure_deployment=os.environ.get("AZURE_OPENAI_EMBEDDING_DEPLOYMENT", ""),
    azure_endpoint=os.environ.get("AZURE_OPENAI_EMBEDDING_API_BASE", ""),
    api_key=os.environ.get("AZURE_OPENAI_EMBEDDING_API_KEY", ""),
    api_version="2023-05-15",
    chunk_size=16,
)

azure_openai_chat_llm = AzureChatOpenAI(
    azure_endpoint=os.environ.get("AZURE_OPENAI_API_BASE", ""),
    api_key=os.environ.get("AZURE_OPENAI_API_KEY", ""),
    azure_deployment=os.environ.get("AZURE_OPENAI_DEPLOYMENT", ""),
    api_version="2023-05-15",
    temperature=0,
    # request_timeout=40,
    # max_retries=2
)


azure_openai_streaming_chat_llm = AzureChatOpenAI(
    azure_endpoint=os.environ.get("AZURE_OPENAI_API_BASE", ""),
    api_key=os.environ.get("AZURE_OPENAI_API_KEY", ""),
    azure_deployment=os.environ.get("AZURE_OPENAI_DEPLOYMENT", ""),
    api_version="2023-05-15",
    streaming=True,
    # callbacks=[custom_async_callback],
    temperature=0,
    # request_timeout=40,
    # max_retries=2
)


azure_openai_streaming_chat_gpt4 = AzureChatOpenAI(
    azure_endpoint=os.environ.get("AZURE_OPENAI_API_BASE_GPT_4", ""),
    api_key=os.environ.get("AZURE_OPENAI_API_KEY_GPT_4", ""),
    azure_deployment=os.environ.get("AZURE_OPENAI_DEPLOYMENT_GPT4", ""),
    api_version="2023-05-15",
    streaming=True,
    # callbacks=[custom_async_callback],
    temperature=0,
    # request_timeout=40,
    # max_retries=2
)


azure_openai_chat_llm_gpt4 = AzureChatOpenAI(
    azure_endpoint=os.environ.get("AZURE_OPENAI_API_BASE_GPT_4", ""),
    api_key=os.environ.get("AZURE_OPENAI_API_KEY_GPT_4", ""),
    azure_deployment=os.environ.get("AZURE_OPENAI_DEPLOYMENT_GPT4", ""),
    api_version="2023-05-15",
    temperature=0,
    # request_timeout=40,
    # max_retries=2
)
