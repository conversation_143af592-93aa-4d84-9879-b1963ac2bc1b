import os



S3_BUCKET = os.environ.get("S3_BUCKET", "bricklayerai-dev")

BLAI_ENV = os.environ.get("BLAI_ENV", "dev")  # dev/prod
REDIS_URL = os.environ.get("REDIS_URL", "localhost")
REDIS_PORT = os.environ.get("REDIS_PORT", "6379")
LLM_CHAT_MEMORY = os.environ.get("LLM_CHAT_MEMORY", "True").lower() == "true"
ALIENVAULT_API_KEY = os.environ.get("ALIENVAULT_API_KEY", "")
LLM_API_KEY = os.environ.get("LLM_API_KEY")
PUBLIC_BACKEND_URL = os.environ.get("PUBLIC_BACKEND_URL", "http://127.0.0.1:8000")

COMPONENT_INPUT_TRUNCATION_LENGTH = 50

