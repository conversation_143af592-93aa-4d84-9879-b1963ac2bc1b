import logging
import re

from llm_api.blai_llm.constants import BLAI_ENV

logger = logging.getLogger(__name__)


def sanitize_tool_name(name):
    return re.sub(r"[^a-zA-Z0-9_-]", "_", name)


def sanitize_tool_names(tools):
    sanitized_tools = []
    for tool in tools:
        # Ensure tool has a 'name' field
        if "name" in tool:
            # Sanitize the name to meet the required pattern
            sanitized_name = sanitize_tool_name(tool["name"])
            tool["name"] = sanitized_name
        sanitized_tools.append(tool)
    return sanitized_tools


def get_datastore_collection_name(organization_id: str, datastore_id: str) -> str:
    return f"org_ds_{organization_id}_{datastore_id[:13]}"


class PlanLogger:
    logs = {}
    devLogs = {}
    intermediateFiles = None

    @staticmethod
    def initializeLogs(plan_id):
        PlanLogger.logs.update({plan_id: []})
        PlanLogger.devLogs.update({plan_id: []})

    @staticmethod
    def destroyLogs(plan_id):
        PlanLogger.logs.pop(plan_id)

    @staticmethod
    def addToLog(msg, plan_id):
        PlanLogger.logs.get(plan_id, []).append(msg)

    @staticmethod
    def addToDevLog(msg, plan_id):
        PlanLogger.devLogs.get(plan_id).append(msg)
        if BLAI_ENV == "dev":
            PlanLogger.addToLog(f"[debug] {msg}", plan_id)

    @staticmethod
    def getDevLogs(plan_id):
        return PlanLogger.devLogs.get(plan_id)

    @staticmethod
    def getPlan(plan_id):
        logs = PlanLogger.logs.get(plan_id)
        if not logs:
            return []
        plan = [f"Step {i+1}) {logs[i]}." for i in range(len(logs))]
        return plan
