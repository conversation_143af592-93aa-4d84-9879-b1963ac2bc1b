import hashlib
import logging
import os
import re
import tempfile
from datetime import datetime
from pathlib import Path
from time import sleep
from typing import List

import boto3
import requests
from fastapi import HTTPException
from fastapi.responses import JSONResponse
from langchain_community.callbacks import get_openai_callback
from langchain_core.documents import Document
from langchain_experimental.text_splitter import SemanticChunker
from langchain_text_splitters import RecursiveCharacterTextSplitter
from opensearchpy import OpenSearch
from opensearchpy.exceptions import NotFoundError

from llm_api.blai_api.dtos import (
    BlogsDeleteFeedRequest,
    DataStoreDocument,
    DeleteOrganizationRequest,
)
from llm_api.blai_llm.blai_loaders import S3UnstructuredDocumentLoader
from llm_api.blai_llm.constants import LLM_API_KEY, PUBLIC_BACKEND_URL, S3_BUCKET
from llm_api.blai_llm.openai import azure_openai_embeddings
from llm_api.blai_llm.utils import get_datastore_collection_name
from llm_api.csv.datastore_utils import generate_data_description_for_csv
from llm_api.eval.rag_flags import RAGFlags
from llm_api.http_client import get_session
from llm_api.vectorstores.pgvector_integration import get_pgvector_for

rag_flags = RAGFlags.get_instance()

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)
s3 = boto3.client("s3")

# Monkey patching nltk.data.find to automatically download resources
import nltk
from nltk import download

# Preserve the original nltk.data.find method
_original_find = nltk.data.find


def find_with_auto_download(resource_path, *args, **kwargs):
    """
    Patched version of nltk.data.find that automatically downloads
    the required NLTK resource if it's not found.

    This generic approach extracts the top-level resource category
    and attempts to download it without hardcoding specific model names.

    :param resource_path: The NLTK resource path (e.g., 'tokenizers/punkt')
    :return: The found resource
    :raises LookupError: If the resource cannot be found even after attempting to download
    """
    try:
        # Attempt to find the resource using the original method
        return _original_find(resource_path, *args, **kwargs)
    except LookupError:
        # Extract the top-level resource category (e.g., 'tokenizers' from 'tokenizers/punkt')
        resource_parts = resource_path.split("/")
        if not resource_parts:
            raise LookupError(f"Invalid resource path: '{resource_path}'")

        try:
            logger.info(f"Attempting to download resource: {resource_parts}")
            for part in reversed(resource_parts):
                if len(part) == 0:
                    continue
                if part == "english":
                    # Skip downloading the 'english' resource
                    continue
                try:
                    # Attempt to download the required resource category
                    download(part)
                    break
                except LookupError:
                    # Skip if the resource category cannot be found
                    pass

            # if _eng suffix try to download without it
            if resource_parts[-1].endswith("_eng"):
                try:
                    download(resource_parts[-1][:-4])
                except LookupError:
                    pass

            # Retry finding the resource after download
            return _original_find(resource_path, *args, **kwargs)
        except LookupError:
            # Raise an error if the resource cannot be found even after downloading
            raise LookupError(
                f"Resource '{resource_path}' could not be found even after attempting to download."
            )


# Apply the monkey-patch to nltk.data.find
nltk.data.find = find_with_auto_download


#####################################################################


class DataStoreManager:
    def update_index_and_cost(self, req_data, llm_cost):
        if not LLM_API_KEY or not PUBLIC_BACKEND_URL:
            logger.error("LLM_API_KEY or PUBLIC_BACKEND_URL not defined")
            return
        timestamp = (
            int(datetime.timestamp(datetime.now())) * 1000
        )  # Javascript needs milliseconds
        data = {
            "dataStoreId": req_data.dataStoreId,
            "organizationId": req_data.organizationId,
            "indexDate": timestamp,
            "userId": req_data.userId,
            "dollarsConsumed": llm_cost,
        }
        headers = {"LLMApiKey": LLM_API_KEY}
        try:
            session = get_session()
            session.patch(
                f"{PUBLIC_BACKEND_URL}/update-index-date",
                data=data,
                headers=headers,
                timeout=int(
                    os.environ.get("PROCEDURES__WORKERS__REQUEST__TIMEOUT", "10")
                ),
            )
        except requests.exceptions.Timeout:
            logger.error("Index update to backend api timed out.")
        except requests.exceptions.RequestException as e:
            logger.error(f"Index update to backend api raised an error {e}")

    def upload_csv_document(self, body: DataStoreDocument):
        logger.debug(f"[upload_csv_document] body: {body}")

        if body.shouldGenerateDataDescription or True:
            logger.debug("[upload_csv_document] generating data description")
            doc_name = body.documents[0]
            with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                s3.download_fileobj(S3_BUCKET, doc_name, temp_file)
                temp_file.close()

                csv_content = ""
                with open(temp_file.name, "r") as file:
                    csv_content = file.read()

                data_description = generate_data_description_for_csv(csv_content)

                return {"dataDescription": data_description}

    def delete_csv_document(self, body: DataStoreDocument):
        logger.debug(f"[delete_csv_document] body: {body}")

    def upload_document(self, body: DataStoreDocument):
        if body.documentType and body.documentType.lower() == "csv":
            return self.upload_csv_document(body)

        nltk.download("averaged_perceptron_tagger")

        logger.info(f"[upload document] body: {body}")
        if not body.documents:
            raise HTTPException(status_code=400, detail="Mising data.")
        collection_name = get_datastore_collection_name(
            body.organizationId, body.dataStoreId
        )
        llm_cost = 0

        self.delete_document(body)

        if body.documentType.lower() in ["pdf", "document"]:
            for doc_name in body.documents:
                logger.info(f"[upload_document] adding document to index: {doc_name}")
                loader = S3UnstructuredDocumentLoader(
                    S3_BUCKET, doc_name
                )  # for now we only support one upload at a time

                documents = loader.load()
                all_texts = [doc.page_content for doc in documents]
                metadata = documents[0].metadata
                if "languages" in metadata:
                    del metadata["languages"]
                if body.publishDate:
                    metadata["publish_date"] = body.publishDate
                metadata["source"] = doc_name
                document = Document(page_content=" ".join(all_texts), metadata=metadata)
                llm_cost += self.chunk_document_and_upload_to_indexes(
                    document, collection_name, doc_name, body
                )

        self.update_index_and_cost(body, llm_cost)

    def chunk_document_and_upload_to_indexes(
        self,
        document,
        collection_name,
        doc_name,
        body: DataStoreDocument,
        retryOnFailure=True,
    ):
        llm_cost = 0

        logger.debug(
            f"[chunk_document_and_upload_to_indexes] chunking document:\n{document.page_content}"
        )

        try:
            chunk_size = 500
            if rag_flags.CUSTOM_CHUNK_SIZE:
                chunk_size = int(rag_flags.CUSTOM_CHUNK_SIZE)

            cleaned_text = re.sub(r" {2,}", " ", document.page_content)

            if rag_flags.USE_SEMANTIC_CHUNKING:
                logger.debug("Using semantic chunking")
                text_splitter = SemanticChunker(
                    azure_openai_embeddings, breakpoint_threshold_type="percentile"
                )
            else:
                logger.debug("Using RecursiveCharacterTextSplitter")
                text_splitter = RecursiveCharacterTextSplitter(
                    chunk_size=chunk_size, chunk_overlap=20
                )
            texts = text_splitter.create_documents([cleaned_text])
            texts = [text.page_content for text in texts]
            doc_name_hash = hashlib.sha256(doc_name.encode("utf-8")).hexdigest()
            ids = [f"{doc_name_hash}_{idx}" for idx in range(len(texts))]

            previous_logger_level = logger.level
            logger.setLevel(logging.DEBUG)
            logger.debug(f"chunked text {texts}")
            for text in texts:
                logger.debug(f"<chunk>\n{text}\n</chunk>\n")
            logger.setLevel(previous_logger_level)

            logger.info(f"split text into {len(texts)} chunks")
            if len(texts) > 0:
                llm_cost = self.upload_to_vectorstore(
                    collection_name=collection_name,
                    doc_name=doc_name,
                    document=document,
                    texts=texts,
                    ids=ids,
                )
                self.upload_to_opensearch(
                    doc_name=doc_name,
                    collection_name=collection_name,
                    texts=texts,
                    ids=ids,
                )
        except Exception as e:
            logger.error("Error uploading document")
            logger.error(document)
            logger.error(e)
            logger.error("[upload_document] document already indexed")
            if retryOnFailure:
                logger.error("[upload_document] retrying...")
                self.delete_document(body)
                self.chunk_document_and_upload_to_indexes(
                    document, collection_name, doc_name, body, False
                )
            else:
                raise HTTPException(status_code=409, detail="Document already indexed.")
        logger.info(f"[upload_document] document {doc_name} added to index")
        return llm_cost

    def upload_to_vectorstore(
        self,
        collection_name: str,
        doc_name: str,
        document: Document,
        texts: List[str],
        ids: List[str],
    ) -> float:
        return self.upload_to_pgvector(collection_name, doc_name, document, texts, ids)

    def upload_to_pgvector(
        self,
        collection_name: str,
        doc_name: str,
        document: Document,
        texts: List[str],
        ids: List[str],
    ) -> float:
        logger.info(
            f"uploading to vectorstore: collection: {collection_name} {doc_name}"
        )
        vectorstore = get_pgvector_for(collection_name)
        with get_openai_callback() as cb:
            vectorstore.add_documents([document], ids=ids)

            metadatas = [document.metadata for _ in range(len(texts))]
            documents = []
            for text, id, metadata in zip(texts, ids, metadatas):
                documents.append(
                    Document(
                        page_content=text,
                        metadata={
                            "id": id,
                            "s3_path": doc_name,
                        }
                        | metadata,
                    )
                )
            vectorstore.add_documents(documents, ids=ids)

            total_cost = cb.total_cost

        return total_cost

    def upload_to_opensearch(
        self,
        doc_name: str,
        collection_name: str,
        texts: List[str],
        ids: List[str],
    ):
        logger.info(
            f"uploading to opensearch: collection: {collection_name} {doc_name} texts: {len(texts)} ids: {len(ids)}"
        )
        opensearch_client = self.get_opensearch_client()
        self.ensure_index(client=opensearch_client, collection_name=collection_name)

        opensearch_requests = []
        for idx, text_chunk in enumerate(texts):
            index = {
                "index": {
                    "_index": collection_name,
                    "_id": ids[idx],
                }
            }
            data = {
                "content": text_chunk or "",
                "url": doc_name,
            }

            opensearch_requests.append(index)
            opensearch_requests.append(data)

        response = opensearch_client.bulk(body=opensearch_requests)
        if response["errors"]:
            error_messages = []
            for item in response["items"]:
                if "error" in item["index"]:
                    status = item["index"]["status"]
                    error_type = item["index"]["error"]["type"]
                    error_messages.append(f"{status}: {error_type}")
            consolidated_message = "There were errors:\n" + "\n".join(error_messages)
            logger.error(consolidated_message)
        else:
            logger.info(
                f"Bulk-inserted {len(response['items'])} items into OpenSearch."
            )
        opensearch_client.indices.refresh(index=collection_name)

    def ensure_index(
        self, client: OpenSearch, collection_name: str, retries=3, delay=2
    ):
        attempt = 0
        while attempt < retries:
            try:
                logger.debug(f"Attempting to get index: {collection_name}")
                index = client.indices.get(index=collection_name)
                logger.info(f"Index {collection_name} already exists.")
                return index
            except NotFoundError:
                logger.warning(
                    f"Index {collection_name} not found. Attempting to create it."
                )
                try:
                    index = client.indices.create(
                        index=collection_name,
                        body={
                            "settings": {
                                "analysis": {
                                    "analyzer": {"default": {"type": "standard"}}
                                },
                                "similarity": {
                                    "custom_bm25": {
                                        "type": "BM25",
                                        "k1": 2.0,
                                        "b": 0.75,
                                    }
                                },
                            },
                            "mappings": {
                                "properties": {
                                    "id": {"type": "keyword"},
                                    "url": {"type": "text"},
                                    "content": {
                                        "type": "text",
                                        "similarity": "custom_bm25",
                                    },
                                }
                            },
                        },
                    )
                    logger.info(f"Successfully created index: {collection_name}")
                    return index
                except Exception as create_error:
                    logger.error(
                        f"Failed to create index {collection_name}: {create_error}"
                    )
                    attempt += 1
                    sleep(delay)
        logger.critical(
            f"Could not ensure index {collection_name} after {retries} attempts."
        )
        raise NotFoundError(
            404,
            "index_not_found_exception",
            f"Could not create or find index {collection_name}",
        )

    def get_opensearch_client(self) -> OpenSearch:
        opensearch_host = os.environ["LLM__OPENSEARCH__HOST"]
        opensearch_port = os.environ["LLM__OPENSEARCH__PORT"]
        opensearch_user = os.environ["LLM__OPENSEARCH__USERNAME"]
        opensearch_pass = os.environ["LLM__OPENSEARCH__PASSWORD"]
        is_local = not "amazonaws" in str(opensearch_host)

        if is_local:
            # local client
            hosts = [{"host": str(opensearch_host), "port": int(opensearch_port)}]
            auth = (str(opensearch_user), str(opensearch_pass))

            client = OpenSearch(
                hosts=hosts,
                http_auth=auth,
                use_ssl=True,
                verify_certs=not is_local,
            )
        else:
            # AWS OpenSearch client
            hosts = [{"host": str(opensearch_host), "port": 443}]
            auth = (str(opensearch_user), str(opensearch_pass))

            client = OpenSearch(
                hosts=hosts,
                http_auth=auth,
                use_ssl=True,
                verify_certs=True,
                # connection_class=RequestsHttpConnection,
                pool_maxsize=20,
            )

            logger.info(client.info())

        return client

    def delete_document(self, body: DataStoreDocument):
        logger.info(f"[delete_document] body: {body}")
        if body.documentType and body.documentType.lower() == "csv":
            self.delete_csv_document(body)
            return

        collection_name = get_datastore_collection_name(
            body.organizationId, body.dataStoreId
        )

        try:
            self.delete_docs_from_vectorstore(
                collection_name=collection_name,
                documents=body.documents,
            )
        except Exception as e:
            logger.error(f"Error deleting document(s) from vectorstore: {e}.")

        try:
            self.delete_docs_from_opensearch(
                collection_name=collection_name,
                documents=body.documents,
            )
        except Exception as e:
            logger.error(f"Error deleting document(s) from OpenSearch: {e}.")

        return JSONResponse(
            content={"detail": "Document(s) deleted from index."}, status_code=200
        )

    def delete_docs_from_vectorstore(
        self,
        collection_name: str,
        documents: List[str],
    ):
        self.delete_docs_from_pgvector(collection_name, documents)

    def delete_docs_from_pgvector(
        self,
        collection_name: str,
        documents: List[str],
    ):
        vectorstore = get_pgvector_for(collection_name)
        vectorstore.search_kwargs = {
            "filter": {
                "source": {
                    "$in": documents,
                }
            },
        }
        while True:
            documents = vectorstore.similarity_search(query="", k=1000)
            if not documents or len(documents) == 0:
                break
            vectorstore.delete(ids=[doc.id for doc in documents])

    def delete_docs_from_opensearch(self, collection_name: str, documents: List[str]):
        opensearch_client = self.get_opensearch_client()

        for doc in documents:
            filename = str(doc).strip()

            opensearch_client.delete_by_query(
                index=collection_name,
                body={"query": {"match_phrase": {"url": filename}}},
            )
            logger.warn(
                f"[delete_document] document {doc} removed from OpenSearch index: {collection_name}"
            )

        opensearch_client.indices.refresh(index=collection_name)

    def delete_from_pgvector(self, feed_id: str, collection_name: str):
        try:
            vectorstore = get_pgvector_for(collection_name)
            vectorstore.search_kwargs = {"filter": {"feed_id": feed_id}}
            while True:
                docs = vectorstore.similarity_search(query="", k=1000)
                if not docs:
                    break
                vectorstore.delete(ids=[doc.id for doc in docs])
            logger.debug(
                f"[delete_from_pgvector] blog feed {feed_id} removed from PGVECTOR collection: {collection_name}"
            )
        except Exception as e:
            logger.error(f"Error deleting feed from PGVECTOR: {e}")

    def delete_from_opensearch(self, feed_id: str, collection_name: str):
        try:
            opensearch_client = self.get_opensearch_client()
            query = {"query": {"term": {"feed_id": feed_id}}}
            opensearch_client.delete_by_query(index=collection_name, body=query)
            opensearch_client.indices.refresh(index=collection_name)
            logger.debug(
                f"[delete_from_opensearch] blog feed {feed_id} removed from OpenSearch index: {collection_name}"
            )
        except Exception as e:
            logger.error(f"Error deleting feed from OpenSearch: {e}")

    def delete_blogs_feed_from_indexes(self, request: BlogsDeleteFeedRequest):
        collection_name = get_datastore_collection_name(
            request.organizationId, request.secondaryIndexId
        )
        self.delete_from_pgvector(request.feedId, collection_name)
        self.delete_from_opensearch(request.feedId, collection_name)

    def delete_organization(self, request: DeleteOrganizationRequest):
        secondary_index_id = get_datastore_collection_name(
            request.organizationId, request.secondaryIndexId
        )
        datastore_ids = [
            get_datastore_collection_name(request.organizationId, datastore_id)
            for datastore_id in request.dataStoreIds
        ]
        errors = []
        for datastore_id in datastore_ids + [secondary_index_id]:
            try:
                self.delete_index_from_opensearch(datastore_id)
            except Exception as e:
                errors.append(
                    f"Error deleting index from OpenSearch: {datastore_id}: {e}"
                )
            try:
                self.delete_collection_from_vectorstore(datastore_id)
            except Exception as e:
                errors.append(
                    f"Error deleting collection from vectorstore: {datastore_id}: {e}"
                )
        if len(errors) > 0:
            logger.error(f"Errors deleting organization: {errors}")
            return {"status": "error", "errors": errors}
        return {"status": "success"}

    def delete_datastore(self, body: DataStoreDocument):

        collection_name = get_datastore_collection_name(
            body.organizationId, datastore_id=body.dataStoreId
        )

        try:
            self.delete_collection_from_vectorstore(collection_name=collection_name)
        except Exception as e:
            print(f"Error deleting collection from vectorstore: {e}.")

        try:
            self.delete_index_from_opensearch(collection_name=collection_name)
        except Exception as e:
            print(f"Error deleting index from OpenSearch: {e}.")

        return JSONResponse(content={"detail": "Data store deleted."}, status_code=200)

    def delete_index_from_opensearch(self, collection_name: str):
        opensearch_client = self.get_opensearch_client()

        opensearch_client.indices.delete(index=collection_name)
        logger.warn(
            f"[delete_datastore] data store {collection_name} deleted from OpenSearch"
        )

    def delete_collection_from_vectorstore(self, collection_name: str):
        self.delete_collection_from_pgvector(collection_name)

    def delete_collection_from_pgvector(self, collection_name: str):
        vectorstore = get_pgvector_for(collection_name)
        vectorstore.delete_collection()
        logger.warn(
            f"[delete_datastore] data store {collection_name} deleted from vectorstore"
        )
