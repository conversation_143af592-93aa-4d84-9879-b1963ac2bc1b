import hashlib
from typing import Iterator

from langchain_core.document_loaders import BaseBlobParser, Blob
from langchain_core.documents import Document


class CustomPyPDFParser(BaseBlobParser):
    """Loads a PDF with pypdf and chunks at character level."""

    def lazy_parse(self, blob: Blob) -> Iterator[Document]:
        """Lazily parse the blob."""
        import pypdf

        with blob.as_bytes_io() as pdf_file_obj:
            pdf_reader = pypdf.PdfReader(pdf_file_obj)
            blob_source = blob.source
            fnp = blob_source[
                blob_source.rfind("/") + 1 :
            ]  # get the file name with prefix: blai_1688717011413_file.pdf
            fname = fnp[
                fnp.find("_", fnp.find("_") + 1) + 1 :
            ]  # file name without the prefix: file.pdf
            org_substr = blob_source.find("org-data-stores/")
            fname_hash = hashlib.sha256(
                blob_source[org_substr:].encode("utf-8")
            ).hexdigest()
            yield from [
                Document(
                    page_content=page.extract_text(),
                    metadata={
                        "source": fname,
                        "page": page_number,
                        "fname_hash": fname_hash,
                    },
                )
                for page_number, page in enumerate(pdf_reader.pages)
            ]
