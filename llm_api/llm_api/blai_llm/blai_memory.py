from typing import Optional

from langchain.chains.conversation.memory import ConversationBufferWindowMemory
from langchain.memory.simple import SimpleMemory
from langchain.schema import AIMessage, HumanMessage
from langchain_community.chat_message_histories import RedisChatMessageHistory
from langchain_community.chat_message_histories.redis import RedisChatMessageHistory
from langchain_core.memory import BaseMemory
from langchain_core.messages import trim_messages
from pydantic import BaseModel, Field, PrivateAttr

from llm_api.blai_llm.constants import REDIS_PORT, REDIS_URL


class CustomRedisMemory(BaseMemory, BaseModel):
    session_id: Optional[str] = Field(
        description="Unique session ID for Redis storage", default=None
    )
    k: int = Field(15, description="Number of messages to retain in memory")

    _chat_memory: RedisChatMessageHistory = PrivateAttr()

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

        self._chat_memory = None
        if self.session_id:
            self._chat_memory = RedisChatMessageHistory(
                url=f"redis://{REDIS_URL}:{REDIS_PORT}/0",
                ttl=600,
                session_id=self.session_id,
            )

    def load_memory_variables(self, inputs):
        if not self._chat_memory:
            return {"chat_history": []}

        # Retrieve all messages
        messages = self._chat_memory.messages

        # Keep only the last `k` messages
        if len(messages) > self.k:
            messages = messages[-self.k :]

        return {"chat_history": messages}

    @property
    def memory_variables(self):
        return ["chat_history"]

    def save_context(self, inputs, outputs):
        if not self._chat_memory:
            return

        user_input = inputs.get("input", "")
        model_output = outputs.get("output", "")

        if user_input:
            if isinstance(user_input, dict):
                self._chat_memory.add_message(HumanMessage(content=user_input["input"]))
            else:
                self._chat_memory.add_message(HumanMessage(content=user_input))
        if model_output:
            self._chat_memory.add_message(AIMessage(content=model_output))

    def clear(self):
        if self._chat_memory:
            self._chat_memory.clear()


def get_memory(conversationId: str) -> ConversationBufferWindowMemory:
    history = CustomRedisMemory(session_id=conversationId)

    return history
