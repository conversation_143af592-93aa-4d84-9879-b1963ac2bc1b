from typing import List

import nltk
from fastapi import HTTPException
from langchain_community.callbacks import get_openai_callback
from langchain_core.documents import Document
from langchain_core.vectorstores import VectorStore
from langchain_experimental.text_splitter import <PERSON>manticChunker
from langchain_text_splitters import RecursiveCharacterTextSplitter
from pydantic import BaseModel
from pydantic_settings import BaseSettings

from llm_api.blai_api.dtos import DataStoreDocument
from llm_api.blai_llm.blai_loaders import S3UnstructuredDocumentLoader
from llm_api.blai_llm.chunk_traceability import (
    ChunkDocNameIDFunction,
    ChunkIndexHashMetaDataFunction,
    get_split_chunks_with_metadata,
    retry,
)
from llm_api.blai_llm.constants import S3_BUCKET
from llm_api.blai_llm.data_stores import DataStoreManager, logger, rag_flags
from llm_api.blai_llm.openai import azure_openai_embeddings
from llm_api.blai_llm.utils import get_datastore_collection_name
from llm_api.vectorstores.pgvector_integration import get_pgvector_for

TotalCost = float


class RAGSettings(BaseSettings):
    USE_UNSTRUCTURED_PDF_LOADER: bool = False
    USE_NEW_SMALL_EMBEDDINGS: bool = False
    USE_NEW_LARGE_EMBEDDINGS: bool = False
    FORCE_GPT4_IN_DATASTORE: bool = False
    USE_UPLOAD_V2_PATH: bool = False
    INCLUDE_RAGAS_METRICS_IN_EVAL: bool = False
    CUSTOM_CHUNK_SIZE: int = 500
    USE_SEMANTIC_CHUNKING: bool = True


# TODO: Reimplement DataStoreManager to allow for proper configuration.
class TraceableDataStoreManager(DataStoreManager):
    def __init__(self, settings: RAGSettings = RAGSettings()):
        super().__init__()
        self._settings = settings

    @retry(Exception, tries=3, delay=1, backoff=2)
    def add_pdf_document_to_vectorstores(
        self, data_store_document: DataStoreDocument, vectorstores: List[VectorStore]
    ) -> TotalCost:
        # Force reset index
        self.delete_document(data_store_document)
        total_cost = 0
        for doc_name in data_store_document.documents:
            logger.info(f"[upload_document] adding document to index: {doc_name}")
            loader = S3UnstructuredDocumentLoader(S3_BUCKET, doc_name)
            # Configure the splitter
            if self._settings.USE_SEMANTIC_CHUNKING:
                logger.debug("Using semantic chunking")
                text_splitter = SemanticChunker(
                    azure_openai_embeddings, breakpoint_threshold_type="percentile"
                )
            else:
                logger.debug("Using RecursiveCharacterTextSplitter")
                text_splitter = RecursiveCharacterTextSplitter(
                    chunk_size=rag_flags.CUSTOM_CHUNK_SIZE, chunk_overlap=20
                )
            chunks = get_split_chunks_with_metadata(
                text_splitter,
                loader,
                id_function=ChunkDocNameIDFunction(doc_name=doc_name),
                metadata_functions=[ChunkIndexHashMetaDataFunction(doc_name=doc_name)],
            )
            for vectorstore in vectorstores:
                # Mirror the reup'd CM per vectorstore call
                with get_openai_callback() as cb:
                    vectorstore.add_documents(chunks)
                total_cost += cb.total_cost
        return total_cost

    def upload_document(self, data_store_document: DataStoreDocument):
        # Handle CSV
        if (
            data_store_document.documentType
            and data_store_document.documentType.lower() == "csv"
        ):
            return self.upload_csv_document(data_store_document)

        nltk.download("averaged_perceptron_tagger")

        logger.info(f"[upload document] body: {data_store_document}")
        # Handle Missing Data
        if not data_store_document.documents:
            raise HTTPException(status_code=400, detail="Missing data.")

        llm_cost = 0

        # Handle pdf/document
        if data_store_document.documentType.lower() in ["pdf", "document"]:
            collection_name = get_datastore_collection_name(
                data_store_document.organizationId, data_store_document.dataStoreId
            )
            pgvector_vectorstore = get_pgvector_for(collection_name)
            opensearch_vectorstore = get_opensearch_for(collection_name=collection_name)
            llm_cost += self.add_pdf_document_to_vectorstores(
                data_store_document, [pgvector_vectorstore, opensearch_vectorstore]
            )

        self.update_index_and_cost(data_store_document, llm_cost)

        return None


class OpenSearchAsFauxVectorStore(BaseModel):
    collection_name: str

    def add_documents(self, documents: List[Document]):
        logger.info(
            f"uploading to opensearch: collection: {self.collection_name} texts: {len(documents)} ids: {len(documents)})"
        )
        # TODO: Fix OpenSearchClient to be standalone
        opensearch_client = DataStoreManager().get_opensearch_client()
        DataStoreManager().ensure_index(
            client=opensearch_client, collection_name=self.collection_name
        )

        opensearch_requests = []
        for idx, document in enumerate(documents):
            index = {
                "index": {
                    "_index": self.collection_name,
                    "_id": document.id,
                }
            }
            try:
                # TODO: Fix coupling between s3_path and OpenSearch
                url = document.metadata.get("s3_path")
            except KeyError as e:
                logger.error(f"Missing s3_path for document: {document}\n\n{e}")
                raise e
            data = {
                "content": document.page_content or "",
                "url": url,
            }

            opensearch_requests.append(index)
            opensearch_requests.append(data)

        response = opensearch_client.bulk(body=opensearch_requests)
        if response["errors"]:
            error_messages = []
            for item in response["items"]:
                if "error" in item["index"]:
                    status = item["index"]["status"]
                    error_type = item["index"]["error"]["type"]
                    error_messages.append(f"{status}: {error_type}")
            consolidated_message = "There were errors:\n" + "\n".join(error_messages)
            logger.error(consolidated_message)
        else:
            logger.info(
                f"Bulk-inserted {len(response['items'])} items into OpenSearch."
            )
        opensearch_client.indices.refresh(index=self.collection_name)


def get_opensearch_for(collection_name: str) -> OpenSearchAsFauxVectorStore:
    """Mirror pgvector interface"""
    return OpenSearchAsFauxVectorStore(collection_name=collection_name)
