from langchain_community.callbacks.openai_info import MODEL_COST_PER_1K_TOKENS

MODEL_COST_PER_1K_TOKENS["gpt-4o"] = 0.005
MODEL_COST_PER_1K_TOKENS["gpt-4o-completion"] = 0.015

MODEL_COST_PER_1K_TOKENS["gpt-4o-2024-05-13"] = 0.005
MODEL_COST_PER_1K_TOKENS["gpt-4o-2024-05-13-completion"] = 0.015

MODEL_COST_PER_1K_TOKENS["gpt-4o-2024-05-13-2024-05-13"] = 0.005
MODEL_COST_PER_1K_TOKENS["gpt-4o-2024-05-13-2024-05-13-completion"] = 0.015

MODEL_COST_PER_1K_TOKENS["gpt-4o-mini"] = 0.00015
MODEL_COST_PER_1K_TOKENS["gpt-4o-mini-completion"] = 0.0006

MODEL_COST_PER_1K_TOKENS["gpt-4o-mini-2024-07-18"] = 0.00015
MODEL_COST_PER_1K_TOKENS["gpt-4o-mini-2024-07-18-completion"] = 0.0006

MODEL_COST_PER_1K_TOKENS["gpt-4o-mini-2024-07-18-2024-07-18"] = 0.00015
MODEL_COST_PER_1K_TOKENS["gpt-4o-mini-2024-07-18-2024-07-18-completion"] = 0.0006
