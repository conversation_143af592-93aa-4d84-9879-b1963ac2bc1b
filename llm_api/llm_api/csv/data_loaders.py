import os
from abc import ABC, abstractmethod
from io import String<PERSON>
from typing import Dict, List, <PERSON>ple

import boto3
import pandas as pd

from llm_api.blai_llm.constants import S3_BUCKET


def download_csv_file(s3_key: str) -> pd.DataFrame:
    s3 = boto3.client("s3")
    local_path = f"/tmp/{os.path.basename(s3_key)}"
    s3.download_file(S3_BUCKET, s3_key, local_path)
    df = pd.read_csv(local_path)
    return df


def download_and_merge_csv_files(s3_path_prefix: str) -> Tuple[pd.DataFrame, List[str]]:
    s3 = boto3.client("s3")
    objects = s3.list_objects_v2(Bucket=S3_BUCKET, Prefix=s3_path_prefix)

    if "Contents" not in objects:
        raise ValueError("No files found in the datastore.")

    csv_keys = [
        obj["Key"] for obj in objects["Contents"] if obj["Key"].lower().endswith(".csv")
    ]
    csv_keys = [k for k in csv_keys if k != s3_path_prefix]

    if not csv_keys:
        raise ValueError("No CSV files found in the datastore.")

    if len(csv_keys) > 20:
        csv_keys = csv_keys[:20]

    dfs = []
    for key in csv_keys:
        local_path = f"/tmp/{os.path.basename(key)}"
        s3.download_file(S3_BUCKET, key, local_path)
        df = pd.read_csv(local_path)
        dfs.append((os.path.basename(key), df))

    headers = [list(df.columns) for _, df in dfs]
    for h in headers[1:]:
        if h != headers[0]:
            raise ValueError("CSV files have different headers. Cannot merge.")

    if len(dfs) == 1:
        final_df = dfs[0][1].copy()
    else:
        final_df = pd.DataFrame()
        for fname, df in dfs:
            df_copy = df.copy()
            df_copy["Source Document"] = fname
            final_df = pd.concat([final_df, df_copy], ignore_index=True)

    if len(final_df) > 1000:
        final_df = final_df.head(1000)

    return final_df, csv_keys


# ---------- data loader strategy classes ----------


class BaseDataLoader(ABC):
    @abstractmethod
    def load_dataframe(self) -> Tuple[pd.DataFrame, Dict[str, List[str]]]: ...


class SingleFileLoader(BaseDataLoader):
    def __init__(self, s3_key: str):
        self.s3_key = s3_key

    def load_dataframe(self) -> Tuple[pd.DataFrame, Dict[str, List[str]]]:
        df = download_csv_file(self.s3_key)
        return df, {"csv_keys": [self.s3_key]}


class FolderMergeLoader(BaseDataLoader):
    def __init__(self, s3_prefix: str):
        self.s3_prefix = s3_prefix

    def load_dataframe(self) -> Tuple[pd.DataFrame, Dict[str, List[str]]]:
        df, csv_keys = download_and_merge_csv_files(self.s3_prefix)
        return df, {"csv_keys": csv_keys}
