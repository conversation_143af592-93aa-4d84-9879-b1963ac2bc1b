import csv
import json
import logging
from io import <PERSON><PERSON>
from typing import Dict, List

import pandas as pd
from langchain_core.messages import SystemMessage

from llm_api.csv.csv_utils import format_df_as_csv
from llm_api.csv.data_loaders import download_and_merge_csv_files
from llm_api.llm.factory import (
    default_4_gpt_spec_data_json_enabled,
    get_model_from_spec,
)
from llm_api.specs.llm_spec import LLMSpec, LLMType

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


def extract_csv_columns_data(csv_str: str) -> dict:
    f = StringIO(csv_str)
    reader = csv.DictReader(f)
    columns_data = {key: [] for key in reader.fieldnames}
    for row in reader:
        for key in columns_data:
            if len(columns_data[key]) < 100:
                columns_data[key].append(row[key])
    return columns_data


def shorten_data_descriptions(csv_str: str, descriptions: list) -> list:
    columns_data = extract_csv_columns_data(csv_str)

    llm = get_model_from_spec(
        LLMSpec(
            type=LLMType.AzureChatOpenAI,
            data=default_4_gpt_spec_data_json_enabled,
        )
    )

    prompt = f"""You are given the following CSV columns with their data:
{json.dumps(columns_data, indent=2)}

And you are given the following data descriptions:
{json.dumps(descriptions, indent=2)}

Please generate a new JSON array with the same format as the descriptions array, but with the descriptions made as short as possible.
Keep only the necessary words to add extra info not present in the column name.
Ensure each new description is strictly shorter (in characters) than the original.
If it cannot be shortened further, keep it as is.
If the column name is clear enough, you SHOULD leave the description empty.

Exaple of redundant descriptions that should be left empty:
- column: "Date/Time (UTC)" description: "UTC timestamp"
- column: "Date/Time (Local)" description: "Local timestamp"
- column: "Source Host" description: "Origin host"
- column: "Details/Comments" description: "Additional info"
- column: "Size (bytes)" description: "File size"
- column: "Hash (MD5, SHA1, SHA256)" description: "File hash"
- column: "Submitted By" description: "Submitter"


Format:
{{
    "data_description": [
        {{
            "column_name": "column1",
            "description": "short description"
        }},
        ...
    ]
}}
"""
    response = llm.invoke([SystemMessage(content=prompt)])
    try:
        new_descriptions = json.loads(response.content)["data_description"]
    except Exception:
        new_descriptions = descriptions

    logger.debug(f"Shortened descriptions: {new_descriptions}")

    final_descriptions = []
    for old_item, new_item in zip(descriptions, new_descriptions):
        old_desc = old_item.get("description", "").strip()
        new_desc = new_item.get("description", "").strip()
        if len(new_desc) > len(old_desc):
            new_desc = old_desc
        final_descriptions.append(
            {"column_name": old_item["column_name"], "description": new_desc}
        )
    return final_descriptions


def generate_data_description_for_csv(csv_str: str) -> str:
    columns_data = extract_csv_columns_data(csv_str)

    llm = get_model_from_spec(
        LLMSpec(
            type=LLMType.AzureChatOpenAI,
            data=default_4_gpt_spec_data_json_enabled,
        )
    )

    prompt = f"""You have the following CSV columns with their data:
{json.dumps(columns_data, indent=2)}

Please generate a JSON with data description for this CSV data.
The description should be short and concise, it should only add information that is not in the coulmn names. 
The column names and descriptions are used to determine if a specific CSV file can answer a user's question.
Keep descriptions short and to the point. Don't repeat information that is already in the column names.
Don't include redundant information like the type of data (ex string, number) if the column name already implies it.
If the column name is clear enough, you SHOULD leave the description empty.
Avoid using extra words like "Indicates if the value is" or "Contains the value of".

Example of bad descriptions:
id -> "Unique identifier for each record." // in this case the description should be ""
name -> "Full name of the individual." // in this case the description should be "Full name" (because this clarifies that the name has both first and last name) or "first name"/"last name" (if this is just the first/last name)
email -> "Email address associated with the individual." // in this case the description should be ""

Keep in mind that you need to use the minimal amount of words to describe the data in the columns. Only use words that are necessary to add extra information that is the data and not in the column name (or implied by the cloumn name).

Format:
{{ 
    "data_description": [
        {{
            "column_name": "column1",
            "description": "description of column1"
        }},
        ...
    ]
}}
"""
    response = llm.invoke([SystemMessage(content=prompt)])
    data_description = json.loads(response.content).get("data_description", [])

    return shorten_data_descriptions(csv_str, data_description)


def get_valid_tool_description(
    tool_description: str,
    display_description: str,
    spec_data: List[Dict[str, str]],
    s3_key: str,
) -> str:
    initial_data_description = ""
    for col in spec_data:
        desc = col.get("description", "").strip()
        if desc:
            initial_data_description += f"{col['column_name']}: {desc}\n"
        else:
            initial_data_description += f"{col['column_name']}\n"
    candidate = tool_description.format(
        description=display_description, data_description=initial_data_description
    )
    if len(candidate) <= 1024:
        return candidate
    try:
        # first try to shorten the description using the CSV data
        df, _ = download_and_merge_csv_files(s3_key)
        csv_str = format_df_as_csv(df)
        descriptions_list = [
            {
                "column_name": col["column_name"],
                "description": col.get("description", ""),
            }
            for col in spec_data
        ]
        shortened_list = shorten_data_descriptions(csv_str, descriptions_list)
        shortened_data_description = ""
        for item in shortened_list:
            desc = item.get("description", "").strip()
            if desc:
                shortened_data_description += f"{item['column_name']}: {desc}\n"
            else:
                shortened_data_description += f"{item['column_name']}\n"
        candidate = tool_description.format(
            description=display_description, data_description=shortened_data_description
        )
        if len(candidate) <= 1024:
            return candidate
    except Exception:
        pass
    # if the shortened description is still too long only use the column names
    only_column_names = ""
    for col in spec_data:
        only_column_names += f"{col['column_name']}\n"
    candidate = tool_description.format(
        description=display_description, data_description=only_column_names
    )
    if len(candidate) <= 1024:
        return candidate
    # and if that is still too long, fallback to empty data description
    candidate = tool_description.format(
        description=display_description,
        data_description="",  # fallback to empty description
    )
    return candidate
