import json
import logging
import os
import re
import time
from io import String<PERSON>
from typing import Any

import boto3
import pandas as pd
from langchain_core.messages import SystemMessage

from llm_api.blai_llm.constants import S3_BUCKET
from llm_api.code_sandbox.code_sandbox import execute_code

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

MAX_CSV_RESULT_LENGTH = int(os.environ.get("MAX_CSV_RESULT_LENGTH", 50000))

PYTHON_CODE_GENERATION_RULES = """
Rules for writing Python code:
1. Read the CSV file at /tmp/merged.csv into a pandas DataFrame.
2. Process the data to answer the user's question. Your answer must be a CSV string.
3. Print only the resulting CSV string to stdout using DataFrame.to_csv(index=False).
4. Do not print JSON, dicts, or any extra text besides the CSV.
5. Never wrap the code in a code-block or add comments to stdout.
6. Always use the ipaddress library when handling IP addresses when possible.
7. The following pip packages are available: pandas, numpy, openpyxl, xlsxwriter, matplotlib, seaborn, scikit-learn, requests, PyYAML, scapy, and networkx.
8. Make sure to output only the data the user explicitly requested. (IN CSV FORMAT!)
9. All output CSV must have a header row.
10. You should properly handle multiline cells/fields when printing CSV data.
"""


def format_df_as_csv(df: pd.DataFrame) -> str:
    csv_buffer = StringIO()
    df.to_csv(csv_buffer, index=False)
    return csv_buffer.getvalue()


async def generate_code_for_question(
    llm, question: str, data_description: str, df: pd.DataFrame
) -> str:
    prompt_content = f"""
You will generate code to answer a question about a CSV file.
You have a CSV file at /tmp/merged.csv. 

# Data description - use this to design your code
{data_description}

# Full CSV data - use this to design your code
{format_df_as_csv(df)}

# The question from the user
User asked: "{question}"

{PYTHON_CODE_GENERATION_RULES}
"""
    logger.debug(f"Generating code with prompt: {prompt_content}")
    code_response = await llm.ainvoke([SystemMessage(content=prompt_content)])
    return code_response.content.strip()


async def fix_code_if_error(llm, question: str, code: str, error: str) -> str:
    fix_prompt = f"""
The following code gave an error when trying to answer the question from the CSV:

User question: "{question}"

Code:
{code}

Error:
{error}

{PYTHON_CODE_GENERATION_RULES}

Rules for code fixing:
- Please fix the code so it works and prints the answer as expected.
- Write only python code that compiles and runs without errors. Don't use any other formatting or code blocks.
- Write the full code, not just the changes.
- Only fix the code that is necessary to make it work.
- Write the code directly, never use a code block or other formatting for the code.
"""
    logger.debug(f"Fixing code with prompt: {fix_prompt}")
    fix_response = await llm.ainvoke([SystemMessage(content=fix_prompt)])
    return fix_response.content.strip()


def run_code_in_sandbox(code: str, files: dict | None = None) -> Any:
    return execute_code(code, files)


def code_output_is_error(exec_result) -> bool:
    return exec_result.returncode != 0


async def answers_question(llm, result: str, question: str) -> bool:
    prompt = f"""
The user asked this question about a CSV file: {repr(question)}
The returned answer is: {repr(result)}

Your task is to evaluate if the output answers the user's question.
Return a JSON object with the following format:
{{
    "reasoning" : string,
    "answers_question" : boolean
}}
Write only the JSON object.
"""
    response = await llm.ainvoke([SystemMessage(content=prompt)])
    try:
        answer_obj = json.loads(response.content.strip())
        return answer_obj.get("answers_question", False)
    except Exception:
        return False


async def fix_code_if_bad_answer(
    llm,
    question: str,
    code: str,
    result: str,
    data_description: str,
    df: pd.DataFrame,
) -> str:
    prompt_content = f"""
You have generated code to answer a user's question about a CSV file.
User question: "{question}"
The current code is:
{code}
It produced the following output:
{result}
This output does not adequately answer the user's question.
Please generate revised code that correctly answers the user's question using the CSV data.
You have a CSV file at /tmp/merged.csv with the following data description:
{data_description}
And the full CSV data is:
{format_df_as_csv(df)}
Follow these rules:
{PYTHON_CODE_GENERATION_RULES}
"""
    fix_response = await llm.ainvoke([SystemMessage(content=prompt_content)])
    return fix_response.content.strip()


def upload_csv_and_wrap(csv_str: str, prefix: str) -> tuple[str, str]:
    """Save csv_str to S3, return (wrapped_block, s3_key)."""
    ts = int(time.time())
    key = f"{prefix.rstrip('/')}/results/result_{ts}.csv"
    boto3.client("s3").put_object(
        Bucket=S3_BUCKET,
        Key=key,
        Body=csv_str.encode("utf-8"),
        ContentType="text/csv",
    )
    max_raw_csv_length = 250
    if len(csv_str) > max_raw_csv_length:
        csv_str = (
            csv_str[:max_raw_csv_length]
            + "... results truncated ... this will be replaced by the full CSV by the system so the user will have the full content in the response."
        )
    wrapped = f'[CSV s3_key="{key}"]\n{csv_str}\n[/CSV]'
    return wrapped, key


def expand_csv_blocks(answer: str, organization_id: str) -> str:
    """
    Replace truncated CSV previews inside [CSV s3_key="..."] blocks with the full file content,
    but only for files whose path contains the specified organization_id.

    The function scans for blocks of the form:
        [CSV s3_key="path/to/file.csv"]
        truncated preview...
        [/CSV]

    It then downloads the CSV from S3 and substitutes the inner content with the full file.
    If the organization_id is not present in the path, the block is left unchanged.
    If the file cannot be fetched, the original preview is kept and an error note is appended.
    """
    pattern = re.compile(r'\[CSV\s+s3_key="([^"]+)"\](.*?)\[/CSV\]', re.DOTALL)
    s3 = boto3.client("s3")

    def _replace(match: re.Match) -> str:
        key = match.group(1)
        if organization_id not in key:
            return match.group(0)

        preview = match.group(2).strip()
        try:
            obj = s3.get_object(Bucket=S3_BUCKET, Key=key)
            csv_bytes = obj["Body"].read()
            csv_str = csv_bytes.decode("utf-8", errors="replace").strip()
        except Exception as exc:
            csv_str = f"{preview}\n<!-- error fetching full CSV: {exc} -->"
        return f'[CSV s3_key="{key}"]\n{csv_str}\n[/CSV]'

    return pattern.sub(_replace, answer)
