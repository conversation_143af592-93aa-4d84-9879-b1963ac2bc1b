import logging
import os
from typing import Any, Dict, <PERSON>, <PERSON>ple

import pandas as pd

from llm_api.csv.csv_utils import (
    answers_question,
    code_output_is_error,
    fix_code_if_bad_answer,
    fix_code_if_error,
    generate_code_for_question,
    run_code_in_sandbox,
    upload_csv_and_wrap,
)

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

MERGED_CSV_PATH = "/tmp/merged.csv"


class CSVAnswerPipeline:
    def __init__(
        self, llm, dataloader, data_description: str = "", output_prefix: str = ""
    ):
        self.llm = llm
        self.dataloader = dataloader
        self.data_description = data_description
        self.output_prefix = output_prefix.rstrip("/")

    async def run(
        self, question: str, max_attempts: int = 3
    ) -> Tuple[str, Dict[str, Any]]:
        df, context = self.dataloader.load_dataframe()
        df.to_csv(MERGED_CSV_PATH, index=False)

        code = await generate_code_for_question(
            self.llm, question, self.data_description, df
        )

        logger.debug(f"Generated code:\n{code}")

        attempts = 0
        result = ""
        exec_result = None
        while attempts <= max_attempts:
            attempts += 1
            exec_result = run_code_in_sandbox(
                code, {MERGED_CSV_PATH: open(MERGED_CSV_PATH, "rb").read()}
            )
            result = exec_result.stdout
            if code_output_is_error(exec_result):
                code = await fix_code_if_error(
                    self.llm, question, code, exec_result.stdout + exec_result.stderr
                )
                logger.debug(f"Fixed code:\n{code}")
                continue
            if not await answers_question(self.llm, result, question):
                code = await fix_code_if_bad_answer(
                    self.llm, question, code, result, self.data_description, df
                )
                continue
            break

        if code_output_is_error(exec_result):
            raise RuntimeError("Sorry, I could not process your request.")

        stdout_csv = exec_result.stdout
        answer, csv_key = upload_csv_and_wrap(stdout_csv, self.output_prefix)
        context["result_key"] = csv_key
        return answer, context, code
