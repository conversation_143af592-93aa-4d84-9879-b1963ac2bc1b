# datastore_tool.py
import logging
import os
import re
from typing import Any, Dict, Literal, Optional
from uuid import UUID

from langchain_core.messages import SystemMessage
from pydantic import root_validator
from pydantic_settings import BaseSettings, SettingsConfigDict

from llm_api.blai_api.dtos import Evidence, EvidenceOrigin
from llm_api.blai_llm.utils import get_datastore_collection_name, sanitize_tool_name
from llm_api.callbacks import <PERSON>gging<PERSON>allbackHandler
from llm_api.eval.rag_flags import RAGFlags
from llm_api.llm.factory import get_model_from_spec
from llm_api.retrievers.opensearch_retriever import OpenSearchRetriever
from llm_api.retrievers.vectorstore_keyword_retriever import (
    VectorStoreKeywordRetriever,
    WeightedReciprocicalRankStrategy,
)
from llm_api.specs.datastore_spec import DatastoreSpec

rag_flags = RAGFlags.get_instance()
from llm_api.datastores.base_rag_tool import BaseRAGTool
from llm_api.http_client import get_session
from llm_api.vectorstores.pgvector_integration import get_pgvector_for

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

HEADERS = {"LLMApiKey": os.environ["LLM_API_KEY"]}


def get_procedure_run_id_by_report(
    procedure_id: int, report_id: str, organization_id: str
) -> str | None:
    url = f"{os.environ['PUBLIC_BACKEND_URL']}/internal/api/llm/procedureRunIdByReport/{procedure_id}/{report_id}".replace(
        "/api/v1", ""
    )
    session = get_session()

    response = session.get(
        url,
        params={"organizationId": organization_id},
        timeout=30,
        headers=HEADERS,
    )

    if response.status_code == 200:
        return response.json().get("procedureRunId")
    elif response.status_code == 404:
        return None
    else:
        logger.error(
            f"Error getting procedureRunId: {response.status_code} - {response.text}"
        )
        return None


class DatastoreToolSettings(BaseSettings):
    model_config = SettingsConfigDict(env_prefix="DATASTORE_TOOL_")
    RETRIEVER_MODE: Literal["VECTOR", "FUSION"] = "FUSION"


class DatastoreTool(BaseRAGTool):
    spec: DatastoreSpec

    name: str = "Private Data Store"
    description: str = (
        "useful when you need to answer questions about an organization data. Input should be a fully formed question."
    )
    collection_name: str
    settings: DatastoreToolSettings = DatastoreToolSettings()
    conversation_id: Optional[str] = None

    plan_id: UUID
    logging_cb: LoggingCallbackHandler = None
    organization_id: str = ""
    display_name: str = ""
    is_memory_datastore: bool = False
    procedure_id: Optional[int] = None
    procedure_name: Optional[str] = None

    @root_validator(pre=True)
    def parse_datastore_params(cls, values: Dict[str, Any]) -> Dict[str, Any]:
        organization_id = values["spec"].organization_id
        datastore_id = values["spec"].datastore_id
        display_name = values["spec"].display_name

        collection_name = get_datastore_collection_name(
            organization_id,
            datastore_id,
        )
        values["name"] = sanitize_tool_name(collection_name)
        values["collection_name"] = collection_name

        display_description = values["spec"].display_description
        tool_description = values["spec"].tool_description
        values["description"] = tool_description.format(description=display_description)
        values["organization_id"] = values["spec"].organization_id
        values["display_name"] = display_name
        values["is_memory_datastore"] = values["spec"].is_memory_datastore
        values["procedure_id"] = values["spec"].procedure_id
        values["procedure_name"] = values["spec"].procedure_name

        return values

    def get_evidence_for_source(self, origin, doc: str) -> Evidence:
        if origin == EvidenceOrigin.Memory:
            source = doc.metadata.get("source", "Unknown")
            filename = doc.metadata.get("filename", "Unknown")
            match = re.search(r"\[(.*?)\]", filename)
            procedure_run_id = match.group(1) if match else None
            name = (
                self.procedure_name
                if self.procedure_name
                else f"Procedure {self.procedure_id}"
            )
            if not procedure_run_id:
                # Extract UUID reportId after "report-"
                report_id_match = re.search(
                    r"report-([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})",
                    filename,
                )
                report_id = report_id_match.group(1) if report_id_match else None
                if report_id:
                    procedure_run_id = get_procedure_run_id_by_report(
                        self.procedure_id, report_id, self.organization_id
                    )
            if procedure_run_id:
                source = f"/procedure-runs/{self.procedure_id}/{procedure_run_id}"
            else:
                # Fallback to Datastore evidence type if procedure_run_id is not found
                return Evidence(
                    name=name,
                    evidenceLocation=source,
                    origin=EvidenceOrigin.Datastore,
                )

            return Evidence(
                name=name,
                evidenceLocation=source,
                origin=origin,
            )
        else:
            return super().get_evidence_for_source(origin, doc)

    def get_evidence_origin(self) -> EvidenceOrigin:
        if self.is_memory_datastore:
            return EvidenceOrigin.Memory
        if self.spec.organization_id == "public":
            return EvidenceOrigin.PublicDatastore
        return EvidenceOrigin.Datastore

    def get_tool_name(self):
        return self.spec.display_name

    async def build_chain(self):
        vectorstore = get_pgvector_for(self.collection_name, with_async_engine=True)
        llm = get_model_from_spec(self.spec.llm)
        if self.settings.RETRIEVER_MODE == "FUSION":
            logger.info("Using FUSION retriever")
            retriever = VectorStoreKeywordRetriever(
                vectorstore_retriever=vectorstore.as_retriever(),
                keyword_retriever=OpenSearchRetriever.create(
                    **OpenSearchRetriever.get_default_params(
                        index_name=self.collection_name,
                        filter_by_date=False,
                    )
                ),
                fusing_strategy=WeightedReciprocicalRankStrategy(),
                collection_name=self.collection_name,
                plan_id=self.plan_id,
            )
        elif self.settings.RETRIEVER_MODE == "VECTOR":
            logger.info("Using VECTOR retriever")
            retriever = vectorstore.as_retriever()
        return self.build_custom_rag_chain(llm, retriever)
