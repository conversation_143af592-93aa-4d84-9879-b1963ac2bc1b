import json
import logging

from langchain_core.documents import Document
from langchain_core.messages import SystemMessage
from langchain_core.tools import BaseTool

from llm_api.blai_api.dtos import (
    LTRB,
    Evidence,
    EvidenceOrigin,
    EvidenceTrace,
    Excerpt,
    PDFCoordinates,
)
from llm_api.blai_llm.utils import PlanLogger

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


class BaseRAGTool(BaseTool):
    async def _arun(self, *args, **kwargs):
        question = self.extract_question_from_args(*args, **kwargs)
        logger.debug(f"{self.__class__.__name__} question: {question}")
        self.log_input(question)
        chain = await self.build_chain()
        answer, source_docs = await chain(question)
        evidence = self.process_evidence(source_docs)
        return {
            "answer": answer,
            "source_documents": source_docs,
            "evidence": evidence,
            "query": question,
        }

    def extract_question_from_args(self, *args, **kwargs) -> str:
        question = ""
        if "question" in kwargs:
            question = kwargs["question"]
        else:
            is_first = True
            for arg in args:
                question += f"{arg}{'' if is_first else '; '}"
                is_first = False
            for _, value in kwargs.items():
                question += f"{value}{'' if is_first else '; '}"
                is_first = False
        return question

    def _run(self, question: str):
        raise NotImplementedError(
            f"You are calling a sync method on {self.__class__}. That's not what you want to do! Probably..."
        )

    def get_tool_name(self) -> str:
        raise NotImplementedError("Subclasses must implement tool_name method.")

    def log_input(self, question: str):
        log_plan = PlanLogger()
        log_text = f"Used '{self.get_tool_name()}' with the input '{question}'"
        log_plan.addToLog(log_text, self.plan_id)

    async def build_chain(self):
        raise NotImplementedError("Subclasses must implement build_chain method.")

    def get_evidence_origin(self) -> EvidenceOrigin:
        raise NotImplementedError(
            "Subclasses must implement get_evidence_origin method."
        )

    def process_evidence(self, source_docs):
        origin = self.get_evidence_origin()
        all_evidence = []
        for doc in source_docs:
            match doc:
                # Case handler for chunks created with TextSplitters
                case Document(
                    metadata={
                        "page_number": page_number,
                        "source": source,
                        "filename": filename,
                        "coordinates": {
                            "layout_width": layout_width,
                            "points": ((x0, y0), (_, y1), (x1, _), (_, _)),
                        },
                        "category": category,
                    }
                ):
                    trace = PDFCoordinates(
                        coord=LTRB(x0=x0, y0=y0, x1=x1, y1=y1),
                        page_number=page_number,
                        category=category,
                        layout_width=layout_width,
                        excerpt=doc.page_content[: min(170, len(doc.page_content))],
                    )
                    evidence = Evidence(
                        name=filename,
                        evidenceLocation=source,
                        origin=origin,
                        trace=trace,
                    )
                # Other Known case
                case Document(metadata={"source": source, "filename": filename}):
                    trace = Excerpt(
                        excerpt=doc.page_content[: min(170, len(doc.page_content))],
                    )
                    evidence = Evidence(
                        name=filename,
                        evidenceLocation=source,
                        origin=origin,
                        trace=trace,
                    )
                # Unknown case
                case _:
                    evidence = Evidence(
                        name="Unknown", evidenceLocation="Unknown", origin=origin
                    )

            all_evidence.append(evidence)
        return all_evidence

    def serialize_documents_xml(self, source_docs) -> str:
        lines = ["<documents>"]
        for doc in source_docs:
            lines.append("  <document>")
            lines.append(
                f"    <source>{doc.metadata.get('source', 'Unknown')}</source>"
            )
            publish_date = doc.metadata.get("publish_date") or doc.metadata.get(
                "published_date"
            )
            if publish_date:
                lines.append(f"    <publish_date>{publish_date}</publish_date>")
            lines.append(f"    <content>{doc.page_content}</content>")
            lines.append("  </document>")
        if not source_docs:
            lines.append("  <!-- No source documents found -->")
        lines.append("</documents>")
        return "\n".join(lines)

    def build_rag_prompt(self, query: str, source_docs) -> str:
        prompt_lines = [
            "Your task is to give the best answer you can to the user’s question based ONLY the information found in the documents.",
            "",
            "Rules",
            "- If at least one document contains relevant information, use it—even if the coverage is partial.",
            "- If none of the documents provide relevant information, reply exactly: I don’t have enough information.",
            "- Do not add facts or opinions from outside the documents.",
            "- Keep sentences short and direct.",
            "- Use line breaks or bullets to separate distinct points.",
            '- Only when a document has a publish date annotated with <publish_date> tag, include it naturally in the answer (e.g. "(current as of 12 March 2025)").',
            "- No greetings, apologies, or offers of further help.",
            "",
            "Examples",
            "",
            "### Example 1 – relevant info with dates",
            "Documents:",
            "<documents>",
            "  <document>",
            "    <source>/docs/Breach_Report</source>",
            "    <publish_date>12 March 2025</publish_date>",
            "    <content>Hackers used a zero-day to breach Acme Bank and stole customer data.</content>",
            "  </document>",
            "  <document>",
            "    <source>/docs/Patch_Advisory</source>",
            "    <publish_date>11 March 2025</publish_date>",
            "    <content>SoftSecure released a patch for CVE-2025-1234, a critical remote-code flaw.</content>",
            "  </document>",
            "</documents>",
            "",
            "Question: What happened in cybersecurity this week?",
            "Answer:",
            "- Hackers breached Acme Bank using a zero-day (current as of 12 March 2025).",
            "- SoftSecure released a patch for critical flaw CVE-2025-1234 (current as of 11 March 2025).",
            "",
            "### Example 2 – partial coverage is acceptable",
            "Documents:",
            "<documents>",
            "  <document>",
            "    <source>/docs/Cloud_Spend_Survey</source>",
            "    <content>60 % of firms say cloud bills jumped in 2024 because of under-used reserved instances.</content>",
            "  </document>",
            "</documents>",
            "",
            "Question: Why are companies paying more for cloud in 2024, and how can they cut costs?",
            "Answer:",
            "- 60 % of firms paid more because they didn’t use their reserved instances.",
            "",
            "### Example 3 – no relevant info",
            "Documents:",
            "<documents>",
            "  <document>",
            "    <source>/docs/Fruit_Export_Stats</source>",
            "    <content>Apple exports from Country X rose 5 % in 2023.</content>",
            "  </document>",
            "</documents>",
            "",
            "Question: What causes lunar eclipses?",
            "Answer:",
            "I don’t have enough information.",
            "",
            "END OF EXAMPLES",
            "",
            "Documents retrieved related to the user query:",
            self.serialize_documents_xml(source_docs),
            "",
            f"Question: {query}",
            "Answer:",
        ]
        return "\n".join(prompt_lines)

    async def perform_quality_check(self, answer: str, source_docs, llm) -> dict:
        docs_text = self.serialize_documents_xml(source_docs)
        quality_prompt = (
            "Your task is to check if the answer is fully supported by the documents.\n"
            "Extract each distinct fact or statement from the answer.\n"
            "For each fact, state whether it is directly supported by the documents (yes/no).\n"
            'Return a JSON object with key "facts", where each fact is an object with "fact" and "from_documents".\n'
            "IMPORTANT: Respond with valid JSON only. Do not include any explanation or extra text.\n\n"
            f"{docs_text}\n\nAnswer:\n{answer}"
        )
        quality_response = await llm.ainvoke([SystemMessage(content=quality_prompt)])
        return json.loads(quality_response.content.strip())

    async def improve_answer(
        self, answer: str, hallucinated_facts: list, prompt_content: str, llm
    ) -> str:
        logger.debug("Improving answer")
        improved_prompt = (
            prompt_content
            + f"\n\nThe previous answer was:\n{answer}\n"
            + "\nSome parts of the answer were not supported by the documents.\n"
            + "Please rewrite the answer using ONLY information from the documents.\n"
            + "Do NOT include the following unsupported statements:\n"
            + "\n".join(f"- {fact['fact']}" for fact in hallucinated_facts)
            + "\n\nFollow all original prompt rules. Keep it short and clear."
        )
        improved_response = await llm.ainvoke([SystemMessage(content=improved_prompt)])
        return improved_response.content.strip()

    async def maybe_improve_answer(
        self, answer: str, prompt_content: str, source_docs, llm
    ) -> str:
        quality_data = await self.perform_quality_check(answer, source_docs, llm)
        hallucinated = [
            fact
            for fact in quality_data.get("facts", [])
            if not fact.get("from_documents")
        ]
        if not hallucinated:
            return answer
        return await self.improve_answer(answer, hallucinated, prompt_content, llm)

    def build_custom_rag_chain(self, llm, retriever):
        async def rag_chain(query: str):
            source_docs = await retriever.ainvoke(query)
            prompt_content = self.build_rag_prompt(query, source_docs)
            completions = await llm.ainvoke([SystemMessage(content=prompt_content)])
            answer = completions.content.strip()
            answer = await self.maybe_improve_answer(
                answer, prompt_content, source_docs, llm
            )

            filtered_docs = source_docs  # No filtering needed anymore

            logger.debug(f"RAG Prompt: {prompt_content}")
            logger.debug(f"Initial RAG Documents: {source_docs}")
            logger.debug(f"Filtered RAG Documents: {filtered_docs}")
            logger.debug(f"RAG Answer: {answer}")

            return answer, filtered_docs

        return rag_chain
