import json
import logging
import os
import time
from typing import Any, Dict, List, Optional
from uuid import UUID

import boto3
from langchain_core.tools import BaseTool
from pydantic import root_validator

from llm_api.blai_api.dtos import Evidence, EvidenceOrigin
from llm_api.blai_llm.constants import S3_BUCKET
from llm_api.blai_llm.utils import (
    PlanLogger,
    get_datastore_collection_name,
    sanitize_tool_name,
)
from llm_api.callbacks import LoggingCallbackHandler
from llm_api.csv.csv_answer_pipeline import CSVAnswerPipeline
from llm_api.csv.csv_utils import MAX_CSV_RESULT_LENGTH
from llm_api.csv.data_loaders import FolderMergeLoader
from llm_api.csv.datastore_utils import get_valid_tool_description
from llm_api.llm.factory import get_model_from_spec
from llm_api.specs.datastore_spec import CSVDatastoreSpec

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


class CSVDatastoreTool(BaseTool):
    spec: CSVDatastoreSpec

    args_schema: dict = {
        "type": "object",
        "properties": {
            "question": {
                "type": "string",
                "description": "The question from the user about the CSV datastore.",
            }
        },
        "required": ["question"],
    }

    name: str = "CSV Data Store"
    description: str = (
        "Always use this tool to retrieve information about or related to the given CSV datastore."
    )
    collection_name: str
    conversation_id: Optional[str] = None

    plan_id: UUID
    logging_cb: LoggingCallbackHandler = None
    organization_id: str = ""
    display_name: str = ""
    data_description: str = ""
    s3_key: str = ""

    @root_validator(pre=True)
    def parse_datastore_params(cls, values: Dict[str, Any]) -> Dict[str, Any]:
        organization_id = values["spec"].organization_id
        datastore_id = values["spec"].datastore_id
        display_name = values["spec"].display_name
        display_description = values["spec"].display_description
        tool_description = values["spec"].tool_description
        spec_data = []
        if values["spec"].data_description:
            for column in values["spec"].data_description:
                spec_data.append(
                    {
                        "column_name": column.column_name,
                        "description": column.description,
                    }
                )
        values["s3_key"] = values["spec"].s3_key
        valid_description = get_valid_tool_description(
            tool_description, display_description, spec_data, values["s3_key"]
        )

        collection_name = get_datastore_collection_name(
            organization_id,
            datastore_id,
        )
        values["name"] = sanitize_tool_name(collection_name)
        values["collection_name"] = collection_name

        values["description"] = valid_description
        values["organization_id"] = organization_id
        values["display_name"] = display_name
        values["data_description"] = valid_description

        return values

    def _run(self, question: str):
        raise NotImplementedError("Use async method _arun instead.")

    async def _arun(self, *args, **kwargs):
        question = kwargs.get("question", args[0] if args else "")

        log_plan = PlanLogger()
        log_plan.addToLog(
            f"Used '{self.spec.display_name}' with the input '{question}'", self.plan_id
        )

        loader = FolderMergeLoader(self.s3_key)
        llm = get_model_from_spec(self.spec.llm)
        output_prefix = f"org-files/{self.organization_id}/{self.collection_name}/"
        pipeline = CSVAnswerPipeline(llm, loader, self.data_description, output_prefix)

        try:
            answer, context, code = await pipeline.run(question)
        except Exception as e:
            logger.error(e)
            return {"answer": str(e)}

        evidence: List[Evidence] = []

        # save code in the /code subfolder of the CSV datastore
        code_file_name = f"code_{int(time.time())}.py"
        code_path = os.path.join(self.s3_key, "code", code_file_name)
        boto3.client("s3").put_object(
            Bucket=S3_BUCKET, Key=code_path, Body=code.encode("utf-8")
        )
        evidence.append(
            Evidence(
                name="code.py",
                evidenceLocation=code_path,
                origin=EvidenceOrigin.Code,
            )
        )

        for key in context.get("csv_keys", []):
            evidence.append(
                Evidence(
                    name=os.path.basename(key),
                    evidenceLocation=key,
                    origin=EvidenceOrigin.Datastore,
                )
            )

        return {
            "answer": answer,
            "source_documents": [],
            "evidence": evidence,
            "query": question,
        }
