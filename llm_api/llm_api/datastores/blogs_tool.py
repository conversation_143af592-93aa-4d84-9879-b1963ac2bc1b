import logging
import os
import traceback
from typing import Any, Dict, List, Optional
from uuid import UUID

from langchain_core.callbacks import (
    AsyncCallbackManagerForRetrieverRun,
    CallbackManagerForRetrieverRun,
)
from langchain_core.documents import Document
from langchain_core.retrievers import BaseRetriever
from pydantic import Extra, PrivateAttr, root_validator

from llm_api.blai_api.dtos import Evidence, EvidenceOrigin, Excerpt
from llm_api.blai_llm.utils import get_datastore_collection_name, sanitize_tool_name
from llm_api.callbacks import LoggingCallbackHandler
from llm_api.datastores.base_rag_tool import BaseRAGTool
from llm_api.llm.factory import get_model_from_spec
from llm_api.retrievers.opensearch_retriever import OpenSearchRetriever
from llm_api.retrievers.vectorstore_keyword_retriever import (
    VectorStoreKeywordRetriever,
    WeightedReciprocicalRankStrategy,
)
from llm_api.specs.blogs_spec import BlogsSpec
from llm_api.utils.blogs_metrics import BlogsMetricsEvent, report_event
from llm_api.vectorstores.pgvector_integration import get_pgvector_for

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


class CombinedRetriever(BaseRetriever):
    _primary_retriever: Any = PrivateAttr()
    _secondary_retriever: Optional[Any] = PrivateAttr()

    def __init__(self, primary_retriever, secondary_retriever: Optional[Any] = None):
        super().__init__()
        self._primary_retriever = primary_retriever
        self._secondary_retriever = secondary_retriever

    def _get_relevant_documents(
        self, query: str, *, run_manager: CallbackManagerForRetrieverRun
    ) -> List[Document]:
        raise NotImplementedError(
            f"You are calling a sync method on {self.__class__}. Think about what you are doing..."
        )

    async def _aget_relevant_documents(
        self, query: str, *, run_manager: AsyncCallbackManagerForRetrieverRun
    ) -> List[Document]:
        # Call underlying retrievers without passing run_manager
        docs_primary = await self._primary_retriever._aget_relevant_documents(
            query, run_manager=run_manager
        )
        docs_secondary = []
        if self._secondary_retriever is not None:
            docs_secondary = await self._secondary_retriever._aget_relevant_documents(
                query, run_manager=run_manager
            )
        merged = docs_primary + docs_secondary
        merged_sorted = sorted(
            merged, key=lambda d: d.metadata.get("score", 0), reverse=True
        )

        return merged_sorted


class BlogsTool(BaseRAGTool):
    spec: BlogsSpec
    plan_id: UUID
    logging_cb: LoggingCallbackHandler
    local_timezone: Optional[str] = None

    args_schema: dict = {
        "type": "object",
        "properties": {
            "question": {
                "type": "string",
                "description": (
                    "The question from the user about the cybersecurity blogs.\n"
                    "The question should be passed with the same date reference the user used. So if the user asked about the news today - make sure to include today in the question.\n"
                    "A fully formed question with all the necessary context.\n"
                    "The question should be a the exact question that the user asked unless the user references something from the conversation context, in which case the question should include the reference to be self-contained.\n"
                    "Don't change the question if it is already self-contained.\n"
                    "CRITICAL FOR GOOD RESULTS: Alway keep all the date references in the question.\n"
                    "CRITICAL: Make sure to pass the question without removing any details. If the user asks 'What's the news today?' - the question should be 'What's the news today?' not 'What's the news?'\n"
                    "So if the users asks 'What's the news about Kubernetes today?' the question should be 'What's the news about Kubernetes today?' not 'What's the news about Kubernetes?' - the today (date) part is super important.\n"
                    "If the user asks 'What are the news?' - the question is missing the context of the tool - this tool retrieves the news about cybersecurity - so the question should be 'What are the news about cybersecurity?'\n"
                    "If one of the previous messages was 'What are the news about Kubernetes this week?' and the user asks 'What about APT28?' then the question should be 'What are the news about APT28 this week?'\n"
                    "EXAMPLES:\n"
                    "- What's the news today?\n"
                    "- What are the news about Kubernetes today?\n"
                    "- What do we know about APT28 ransomware group?\n"
                    "- What was the MoveIt malware news from a day before yesterday?\n"
                    "- What happened in cybersecurity for finance last week?\n"
                    "- Tell about any incidents that happened 3 days ago?\n"
                ),
            }
        },
        "required": ["question"],
    }

    class Config:
        extra = Extra.allow

    @root_validator(pre=True)
    def validate_basic(cls, values: Dict[str, Any]) -> Dict[str, Any]:
        if not values.get("name"):
            values["name"] = sanitize_tool_name(values["spec"].name)
        values["description"] = (
            "ALWAYS USE THIS TOOL before answering. It probably has the information your need to answer.\n"
            "This tool retrieves information from the internet (mostly about cybersecurity, but other topics are also possible).\n"
            "The input is a fully formed question with all the necessary context.\n"
            "Pay extra attention to dates or time references in the question - they should be included in the question.\n"
            "Don't change the user's question if it's already self-contained.\n"
            "Examples:\n"
            "- What are the news about Kubernetes?\n"
            "- What do we know about APT28 ransomware group?\n"
            "- What happened in cybersecurity today?\n"
        )
        logger.debug("BlogsTool description: %s", values["description"])
        return values

    def get_evidence_origin(self) -> EvidenceOrigin:
        return EvidenceOrigin.Url

    def get_tool_name(self):
        return self.spec.name

    def process_evidence(self, source_docs):
        origin = self.get_evidence_origin()
        all_evidence = []
        for doc in source_docs:
            source = doc.metadata.get("source", "Unknown")
            page_title = doc.metadata.get("page_title")
            trace = Excerpt(
                excerpt=doc.page_content[: min(170, len(doc.page_content))],
            )
            evidence_data = {
                "name": source,
                "evidenceLocation": source,
                "origin": origin,
                "trace": trace,
            }
            if page_title:
                evidence_data["page_title"] = page_title
            all_evidence.append(Evidence(**evidence_data))
        return all_evidence

    async def build_chain(self):
        primary_collection = os.environ.get(
            "BLOGS_PGVECTOR_COLLECTION_NAME", "general_knowledge_blogs"
        )
        primary_vectorstore = get_pgvector_for(
            primary_collection, with_async_engine=True
        )
        primary_retriever = VectorStoreKeywordRetriever(
            vectorstore_retriever=primary_vectorstore.as_retriever(),
            keyword_retriever=OpenSearchRetriever.create(
                **OpenSearchRetriever.get_default_params(
                    disabled_feed_ids=self.spec.disabled_public_source_feed_ids
                )
            ),
            fusing_strategy=WeightedReciprocicalRankStrategy(),
            collection_name=primary_collection,
            plan_id=self.plan_id,
            local_timezone=self.local_timezone,
        )

        secondary_retriever = None
        if getattr(self.spec, "secondary_index_id", None):
            secondary_collection = get_datastore_collection_name(
                self.spec.organization_id, self.spec.secondary_index_id
            )
            secondary_vectorstore = get_pgvector_for(
                secondary_collection, with_async_engine=True
            )
            secondary_retriever = VectorStoreKeywordRetriever(
                vectorstore_retriever=secondary_vectorstore.as_retriever(),
                keyword_retriever=OpenSearchRetriever.create(
                    **OpenSearchRetriever.get_default_params(
                        index_name=secondary_collection,
                        disabled_feed_ids=self.spec.disabled_feed_ids_for_secondary_index,
                    )
                ),
                fusing_strategy=WeightedReciprocicalRankStrategy(),
                collection_name=secondary_collection,
                plan_id=self.plan_id,
                local_timezone=self.local_timezone,
            )

        combined_retriever = CombinedRetriever(primary_retriever, secondary_retriever)
        llm = get_model_from_spec(self.spec.llm)
        return self.build_custom_rag_chain(llm, combined_retriever)

    async def _arun(self, *args, **kwargs):
        try:
            result = await super()._arun(*args, **kwargs)
            report_event(
                BlogsMetricsEvent.BlogsQuery,
                data={"question": result.get("query", "")},
                organization_id=self.spec.organization_id or "public",
            )
            return result
        except Exception as e:
            report_event(
                BlogsMetricsEvent.BlogsQueryError,
                data={
                    "error": {
                        "message": str(e),
                        "traceback": traceback.format_exc(),
                    },
                },
                organization_id=self.spec.organization_id or "public",
            )
            raise
