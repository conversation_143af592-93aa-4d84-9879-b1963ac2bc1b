import datetime
import json

from langchain_core.messages import SystemMessage

from llm_api.eval.eval_utils import retry_with_exponential_delay
from llm_api.llm.factory import default_3_5_gpt_spec_data, get_model_from_spec
from llm_api.specs.llm_spec import LLMSpec, LLMType


@retry_with_exponential_delay(max_retries=10, initial_delay=2)
def evaluate_answer(question: str, expected_answer: str, given_answer: str):
    judge_llm = get_model_from_spec(
        spec=LLMSpec(
            type=LLMType.AzureChatOpenAI,
            data=default_3_5_gpt_spec_data,
        )
    )

    today = datetime.datetime.now()
    date_str = today.strftime("%Y-%m-%d")

    judge_llm_response = judge_llm.invoke(
        [
            SystemMessage(
                content=f"""You are an expert evaluator. Your task is to evaluate a given answer based on its accuracy and completeness compared to the ground truth answer.

Input:
1. Question: {question}
2. Ground truth: {expected_answer}
3. Given answer: {given_answer}
4. The current date is {date_str}. When evaluating the answer, please consider the date and time references in the question and the ground truth. The answer should be relevant to the current date.

You compare the given answer to the ground truth and give a score of 1 to 5 on the following criteria:
- Accuracy: Does the given answer correctly address the question and match the ground truth answer?
- Completeness: Does the given answer include all necessary information provided in the ground truth answer?
- Clarity: Is the given answer clear and easy to understand?

Take in consideration the question when evaluating the answer, the given answer should respond to the question, it's not necessary to have all the information from the ground truth.

The answer most importantly should answer the question, it's ok to have less (or more) information than the ground truth, as long as it answers the question.
A good answer can have less information than the ground truth, as long as it answers the question.
If the ground truth contains details that are not critical to answering the question they can be omitted.
Details that do not answer the question directly are not necessary. When reasoning the first thing you will do is to simplify the ground truth to its core elements.
So a good answer can omit some information from the ground truth, as long as it is correct and it answers the question.
A good answer can have more information than the ground truth, as long as it answers the question.
A good answer can rephrase the ground truth, as long as it is correct and it answers the question.

The ground truth can be a summary of a larger text, a paragraph, a sentence or a list of facts. So if the answer is larger than the ground truth it is not necessarily wrong.
If the answer is larger than the ground truth when reasoning think if you can summarize the answer to its core elements and get to something similar to the ground truth.

Critical details are those that are essential to correctly answering the question and cannot be omitted without losing the core meaning.
Non-critical details are additional pieces of information that provide depth but are not necessary to answer the question accurately. For example if the question is "How many X does Y have?" and the ground truth is "Y has 10 X, 5 of them are red and 5 are blue", the critical details are "Y has 10 X" and the non-critical details are "5 of them are red and 5 are blue". The answer can omit non-critical details.
Repeated information or things that can be inferred from the question are not considered critical details. For example if the question is "What is the capital of France?" and the ground truth is "The capital of France is Paris. It is located in Europe." the critical detail is "Paris" and the non-critical detail is "It is located in Europe." The answer can omit non-critical details.
The answer can have compressed version of one or more parts from the ground truth. For example if the ground truth has "Sit down, slide your feet into the shoes, adjust for comfort, and tie the laces securely." and the answer has "put on shoes" the answer is correct because they mean the same thing - the steps of putting on shoes are not critical details, they all describe the action.

Answers with strength below 3 are considered incorrect.

JSON RESPONSE FORMAT
--------------------

All responses should be written in JSON object format.
When responding please ONLY write responses like this:
{{
  "simplified_ground_truth": string,
  "reasoning": string,
  "accuracy": int,
  "completeness": int,
  "clarity": int,
  "strength": int
}}
You write the JSON directly. Don't write anything else. No need to wrap inside a code block."""
            )
        ]
    )
    return json.loads(judge_llm_response.content)
