import json
import math
from datetime import datetime, timed<PERSON>ta
from typing import List, Optional

from fastapi import APIRouter, HTTPException, Query
from fastapi.responses import JSONResponse

from llm_api.blai_llm.constants import S3_BUCKET
from llm_api.eval.blogs_eval_builder import InsufficientQuestions, build_blogs_eval
from llm_api.eval.config import s3
from llm_api.eval.runner import get_experiment_results, run_experiment
from llm_api.eval.storage import hash_string, s3_object_exists
from llm_api.utils.blogs_metrics import BlogsMetricsEvent, report_event

api_router = APIRouter()


def _list_experiment_keys() -> List[str]:
    prefix = "eval/experiments/"
    paginator = s3.get_paginator("list_objects_v2")
    keys: List[str] = []
    for page in paginator.paginate(Bucket=S3_BUCKET, Prefix=prefix, Delimiter="/"):
        keys.extend(
            k["Key"]
            for k in page.get("Contents", [])
            if k["Key"].endswith(".json")
            and "/results/" not in k["Key"]
            and "/metadata/" not in k["Key"]
        )
    return keys


@api_router.get("/experiments")
def list_experiments(
    page: int = Query(1, ge=1),
    per_page: int = Query(50, ge=1, le=250),
):
    keys = _list_experiment_keys()
    total_pages = max(1, math.ceil(len(keys) / per_page))
    if page > total_pages:
        raise HTTPException(status_code=400, detail="page out of range")
    start, end = (page - 1) * per_page, page * per_page
    items = [
        key.removeprefix("eval/experiments/").removesuffix(".json")
        for key in keys[start:end]
    ]
    return JSONResponse(
        {
            "page": page,
            "per_page": per_page,
            "total_pages": total_pages,
            "items": items,
        }
    )


@api_router.get("/experiments/{name}")
def get_experiment(name: str):
    key = f"eval/experiments/{name}.json"
    if not s3_object_exists(key):
        raise HTTPException(status_code=404, detail="experiment not found")
    obj = s3.get_object(Bucket=S3_BUCKET, Key=key)
    return JSONResponse(json.loads(obj["Body"].read().decode("utf-8")))


@api_router.post("/experiments/blogs")
async def build_blogs_experiment(
    start: str,
    end: str,
    q_count: int = Query(..., ge=1, le=250),
    q_per: int = Query(3, ge=1, le=5),
):
    try:
        datetime.strptime(start, "%Y-%m-%d")
        datetime.strptime(end, "%Y-%m-%d")
    except ValueError:
        raise HTTPException(status_code=400, detail="dates must be YYYY-MM-DD")

    experiment_name: Optional[str] = None
    try:
        gen = build_blogs_eval(start=start, end=end, q_count=q_count, q_per=q_per)
        async for kind, payload in gen:
            if kind == "redirect":
                # payload is /edit/experiments/<name>.json
                experiment_name = payload.split("/")[-1].removesuffix(".json")
    except InsufficientQuestions as e:
        raise HTTPException(status_code=422, detail=str(e))
    except Exception:
        raise HTTPException(status_code=507, detail="failed to save experiment")

    if not experiment_name:
        raise HTTPException(status_code=500, detail="experiment name not captured")

    return JSONResponse({"name": experiment_name}, status_code=201)


@api_router.post("/experiments/{name}/run")
async def run_experiment_sync(name: str):
    key = f"eval/experiments/{name}.json"
    if not s3_object_exists(key):
        raise HTTPException(status_code=404, detail="experiment not found")

    redirect_url = None
    async for kind, payload in run_experiment(name):
        if kind == "redirect":
            redirect_url = payload

    if redirect_url is None:
        raise HTTPException(status_code=500, detail="run did not finish")

    run_name = redirect_url.rsplit("/", 1)[-1]
    res = get_experiment_results(run_name)
    if "tests" in res:
        del res["tests"]
    if "metrics" in res:
        del res["metrics"]
    res["run_name"] = run_name
    return JSONResponse(res)


@api_router.get("/experiments/runs/{run_name}")
def get_run_results(run_name: str):
    """
    Return the raw JSON produced by a finished run.
    Storage layout: eval/experiments/results/<experiment>/<run_name>.json
    """
    experiment = "_".join(run_name.split("_")[:-1])
    key = f"eval/experiments/results/{experiment}/{run_name}.json"
    if not s3_object_exists(key):
        raise HTTPException(status_code=404, detail="run results not found")
    obj = s3.get_object(Bucket=S3_BUCKET, Key=key)
    return JSONResponse(json.loads(obj["Body"].read().decode("utf-8")))


@api_router.post("/experiments/daily_check")
async def daily_check():
    today = datetime.utcnow().date()
    start = (today - timedelta(days=1)).isoformat()
    end = today.isoformat()

    # Build 25-question experiment for the last 24 h
    experiment_name = None
    try:
        gen = build_blogs_eval(start=start, end=end, q_count=25, q_per=3)
        async for kind, payload in gen:
            if kind == "redirect":
                experiment_name = payload.split("/")[-1].removesuffix(".json")
    except InsufficientQuestions:
        report_event(
            BlogsMetricsEvent.DailyBlogsCheckError, {"reason": "InsufficientQuestions"}
        )
        raise HTTPException(status_code=422, detail="no documents in range")

    if experiment_name is None:
        report_event(BlogsMetricsEvent.DailyBlogsCheckError, {"reason": "no_docs"})
        raise HTTPException(status_code=422, detail="no documents in range")

    # Run experiment synchronously
    redirect_url = None
    async for kind, payload in run_experiment(experiment_name):
        if kind == "redirect":
            redirect_url = payload
    if redirect_url is None:
        report_event(BlogsMetricsEvent.DailyBlogsCheckError, {"reason": "run_failed"})
        raise HTTPException(status_code=500, detail="run failed")

    run_name = redirect_url.rsplit("/", 1)[-1]
    res = get_experiment_results(run_name)
    accuracy = res.get("processed", {}).get("accuracy", 0)

    if accuracy >= 0.8:
        report_event(
            BlogsMetricsEvent.DailyBlogsCheckPass,
            {
                "accuracy": accuracy,
                "experiment_name": experiment_name,
                "run_name": run_name,
            },
        )
        return JSONResponse("PASS")
    else:
        report_event(
            BlogsMetricsEvent.DailyBlogsCheckError,
            {
                "accuracy": accuracy,
                "experiment_name": experiment_name,
                "run_name": run_name,
            },
        )
        return JSONResponse("FAIL")
