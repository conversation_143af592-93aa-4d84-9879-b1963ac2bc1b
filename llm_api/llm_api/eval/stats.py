from typing import List

from scipy import stats as scipy_stats


def evaluate_significance(first: List[int], second: List[int]):
    _, p = scipy_stats.ttest_ind(first, second)
    return p, p < 0.05


def process_results(results):
    correct = sum(1 for t in results["tests"] if t["correct"])
    total = len(results["tests"])
    results["processed"] = {
        "correct": correct,
        "total": total,
        "accuracy": round(correct / total, 2) if total else 0,
    }
    return results
