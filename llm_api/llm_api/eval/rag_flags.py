import os


class RAGFlags:
    _instance = None

    @staticmethod
    def get_instance():
        if RAGFlags._instance is None:
            RAGFlags()
        return RAGFlags._instance

    def __init__(self):
        if RAGFlags._instance is not None:
            raise Exception("This class is a singleton!")
        RAGFlags._instance = self

        self.USE_UNSTRUCTURED_PDF_LOADER = os.getenv(
            "USE_UNSTRUCTURED_PDF_LOADER", "False") == "True"
        self.USE_NEW_SMALL_EMBEDDINGS = os.getenv(
            "USE_NEW_SMALL_EMBEDDINGS", "False") == "True"
        self.USE_NEW_LARGE_EMBEDDINGS = os.getenv(
            "USE_NEW_LARGE_EMBEDDINGS", "False") == "True"
        self.FORCE_GPT4_IN_DATASTORE = os.getenv(
            "FORCE_GPT4_IN_DATASTORE", "False") == "True"
        self.USE_UPLOAD_V2_PATH = os.getenv("USE_UPLOAD_V2_PATH", "False") == "True"
        self.INCLUDE_RAGAS_METRICS_IN_EVAL = os.getenv(
            "INCLUDE_RAGAS_METRICS_IN_EVAL", "False") == "True"
        self.CUSTOM_CHUNK_SIZE = os.getenv("CUSTOM_CHUNK_SIZE", None)
        self.USE_SEMANTIC_CHUNKING = os.getenv(
            "USE_SEMANTIC_CHUNKING", "False") == "True"
        self.NONCE = 1337133713371337

    def set_flag(self, flag_name, value):
        if hasattr(self, flag_name):
            setattr(self, flag_name, value)
        else:
            raise AttributeError(f"Flag {flag_name} does not exist in RAGFlags")


# Example usage:
# rag_flags = RAGFlags.get_instance()
# rag_flags.set_flag("USE_UNSTRUCTURED_PDF_LOADER", True)
