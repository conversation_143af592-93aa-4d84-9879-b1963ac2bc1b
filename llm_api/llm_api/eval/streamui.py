def scroll_to_bottom_js():
    return "<script>scrollToBottom();</script>"


async def convert_to_streaming_ui(generator):
    yield """<script>
  function scrollToBottom() {
    window.scrollTo({
      top: document.body.scrollHeight,
      behavior: 'smooth'
    });
  }
  </script>
  """
    async for gen in generator.__aiter__():
        if isinstance(gen, tuple):
            if gen[0] == "print":
                yield gen[1] + scroll_to_bottom_js()
            elif gen[0] == "redirect":
                yield f'<script>window.location.href="{gen[1]}";</script>'
