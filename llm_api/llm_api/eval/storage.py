import hashlib
import json

from botocore.exceptions import ClientError

from llm_api.blai_llm.constants import S3_BUCKET
from llm_api.eval.config import s3


def hash_string(text: str) -> str:
    return hashlib.sha1(text.encode()).hexdigest()[:13]


def get_experiment_config(name: str):
    key = f"eval/experiments/{name}.json"
    obj = s3.get_object(Bucket=S3_BUCKET, Key=key)
    return json.loads(obj["Body"].read().decode("utf-8"))


def s3_object_exists(key: str) -> bool:
    try:
        s3.head_object(Bucket=S3_BUCKET, Key=key)
        return True
    except ClientError:
        return False


def meta_key(name: str) -> str:
    return f"eval/experiments/metadata/experiments/{name}.json"


def results_key(name: str, run_no: int) -> str:
    return f"eval/experiments/results/{name}/{name}_{run_no}.json"
