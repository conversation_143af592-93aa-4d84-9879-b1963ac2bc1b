import time


class Metrics:
    _stack = []
    _current_test_metrics = None
    _expected_metrics = {}
    _experiment_name = ""
    _metrics_data = []
    _start_time = None

    @staticmethod
    def start_experiment(name):
        Metrics._experiment_name = name
        Metrics._metrics_data = []
        Metrics._expected_metrics = {}
        Metrics._start_time = time.time()
        Metrics._stack.append([])

    @staticmethod
    def end_experiment():
        end_time = time.time()
        duration = end_time - Metrics._start_time
        Metrics._normalize_metrics()
        experiment_data = {
            "metrics": Metrics._expected_metrics,
            "experiment_name": Metrics._experiment_name,
            "duration": duration,
            "tests": Metrics._metrics_data
        }
        Metrics._stack.pop()
        return experiment_data

    @staticmethod
    def metric_type(value):
        if isinstance(value, bool):
            return "bool"
        elif isinstance(value, (int, float)):
            return "number"
        elif isinstance(value, str):
            return "string"
        else:
            return "unknown"

    @staticmethod
    def add(test_data):
        Metrics._metrics_data.append(test_data)
        for key in test_data:
            if key not in Metrics._expected_metrics:
                Metrics._expected_metrics[key] = Metrics.metric_type(
                    test_data[key])

    @staticmethod
    def expect(key, dtype):
        Metrics._expected_metrics[key] = dtype

    @staticmethod
    def _normalize_metrics():
        new_data = []
        for test in Metrics._metrics_data:
            normalized = {}
            for key in list(Metrics._expected_metrics.keys()) + ["duration"]:
                if key in test:
                    normalized[key] = test[key]
                else:
                    normalized[key] = None
            new_data.append(normalized)
        Metrics._metrics_data = new_data
        return Metrics._metrics_data


class Test:
    def __init__(self):
        self._test_metrics = {"start_time": time.time()}

    @staticmethod
    def start():
        return Test()

    def set(self, key, value):
        self._test_metrics[key] = value

    def end(self):
        end_time = time.time()
        self._test_metrics["duration"] = end_time - \
            self._test_metrics["start_time"]
        del self._test_metrics["start_time"]
        return self._test_metrics

# Usage example:
# Metrics.start_experiment("experiment_name")
# Metrics.expect("correct", "bool")
# Metrics.expect("accuracy", "number")

# # Simulating a test run
# test = Test.start()
# test.set("correct", True)
# test.set("accuracy", 0.95)
# Metrics.add(test.end())

# # Another test run
# test = Test.start()
# test.set("correct", False)
# test.set("complexity", 1.23)
# Metrics.add(test.end())

# metrics = Metrics.end_experiment()
# print(metrics)
