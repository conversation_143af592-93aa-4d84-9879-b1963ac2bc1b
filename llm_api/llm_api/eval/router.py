import json
from datetime import datetime

import boto3
from botocore.exceptions import NoCredentialsError, PartialCredentialsError
from fastapi import APIRouter, Request
from fastapi.responses import HTMLResponse, RedirectResponse, StreamingResponse
from fastapi.templating import Jinja2Templates

from llm_api.blai_llm.constants import S3_BUCKET
from llm_api.eval import get_experiment_results, run_experiment
from llm_api.eval.api import api_router
from llm_api.eval.blogs_eval_builder import build_blogs_eval
from llm_api.eval.opensearch_utils import blogs_date_bounds
from llm_api.eval.rag_flags import RAGFlags
from llm_api.eval.streamui import convert_to_streaming_ui
from llm_api.retrievers.opensearch_retriever import OpenSearchRetriever

rag_flags = RAGFlags.get_instance()
s3 = boto3.client("s3")

router = APIRouter()
router.include_router(api_router)

templates = Jinja2Templates(directory="llm_api/eval/templates")


# ---------------------------------------------------------------------------
# Home
# ---------------------------------------------------------------------------
@router.get("/eval")
async def redirect_home():
    return RedirectResponse(url="/eval/home", status_code=303)


@router.get("/eval/home", response_class=HTMLResponse)
async def eval_home(request: Request):
    return templates.TemplateResponse("eval_home.html", {"request": request})


# ---------------------------------------------------------------------------
# RAG flags
# ---------------------------------------------------------------------------
@router.get("/ragflags", response_class=HTMLResponse)
async def read_ragflags(request: Request):
    flags = {n: getattr(rag_flags, n) for n in dir(rag_flags) if n.isupper()}
    return templates.TemplateResponse(
        "ragflags.html", {"request": request, "flags": flags}
    )


@router.post("/setflags", response_class=HTMLResponse)
async def set_flags(request: Request):
    data = await request.form()
    for k, v in data.items():
        if not hasattr(rag_flags, k):
            return HTMLResponse(f"Flag {k} does not exist", status_code=400)
        cur = getattr(rag_flags, k)
        rag_flags.set_flag(
            k, v.lower() in ["true", "1", "on"] if isinstance(cur, bool) else v
        )
    return RedirectResponse(url="/ragflags", status_code=303)


# ---------------------------------------------------------------------------
# Blogs eval
# ---------------------------------------------------------------------------
@router.get("/blogs_eval", response_class=HTMLResponse)
async def blogs_eval_form(request: Request):
    index_name = OpenSearchRetriever.get_default_params()["index_name"]
    oldest, newest = await blogs_date_bounds(index_name)
    return templates.TemplateResponse(
        "blogs_eval.html",
        {"request": request, "defaults": {"start": oldest, "end": newest}},
    )


@router.post("/blogs_eval/build")
async def blogs_eval_build(request: Request):
    f = await request.form()
    start, end = f.get("start"), f.get("end")
    q_count = int(f.get("q_count", 0))
    q_per = int(f.get("q_per", 0))

    if q_count < 1 or q_count > 250 or q_per < 1 or q_per > 5 or start > end:
        return HTMLResponse("Invalid parameters", status_code=400)

    generator = build_blogs_eval(start=start, end=end, q_count=q_count, q_per=q_per)
    return StreamingResponse(convert_to_streaming_ui(generator), media_type="text/html")


# ---------------------------------------------------------------------------
# Run experiment / results
# ---------------------------------------------------------------------------
@router.get("/run/{path:path}")
async def run_and_stream(request: Request, path: str):
    name = path.split("/")[-1].replace(".json", "")
    return StreamingResponse(
        convert_to_streaming_ui(run_experiment(name)), media_type="text/html"
    )


@router.get("/results/{name}")
async def results_page(name: str, request: Request):
    res = get_experiment_results(name)
    return templates.TemplateResponse(
        "results.html", {"request": request, "results": res}
    )


# ---------------------------------------------------------------------------
# Browse / edit / upload  (unchanged from before)
# ---------------------------------------------------------------------------
@router.get("/browse/{path:path}", response_class=HTMLResponse)
async def browse(request: Request, path: str = ""):
    cur = f"eval/{path}"
    try:
        resp = s3.list_objects_v2(Bucket=S3_BUCKET, Prefix=cur, Delimiter="/")
    except (NoCredentialsError, PartialCredentialsError):
        return HTMLResponse("Credentials not available", status_code=403)

    folders = [p["Prefix"].split("/")[-2] for p in resp.get("CommonPrefixes", [])]
    files = [
        c["Key"].split("/")[-1] for c in resp.get("Contents", []) if c["Key"] != cur
    ]
    crumbs = [
        {"name": n, "path": "/".join(cur.split("/")[1 : i + 1]) + ("/" if i else "")}
        for i, n in enumerate(cur.split("/"))
        if n
    ]
    return templates.TemplateResponse(
        "browse.html",
        {
            "request": request,
            "path": path,
            "current_path": cur,
            "folders": folders,
            "files": files,
            "breadcrumbs": crumbs,
        },
    )


@router.get("/edit/{path:path}", response_class=HTMLResponse)
async def edit(request: Request, path: str):
    fp = f"eval/{path}"
    try:
        obj = s3.get_object(Bucket=S3_BUCKET, Key=fp)
        content = json.dumps(json.loads(obj["Body"].read().decode("utf-8")), indent=2)
    except (NoCredentialsError, PartialCredentialsError):
        return HTMLResponse("Credentials not available", status_code=403)

    if fp.endswith(".json") and path.startswith("experiments/results"):
        return RedirectResponse(
            url=f"/results/{fp.split('/')[-1][:-5]}", status_code=303
        )

    tmpl = "edit_json.html" if fp.endswith(".json") else "view_file.html"
    return templates.TemplateResponse(
        tmpl,
        {
            "request": request,
            "file_path": fp,
            "file_content": content,
            "filename": fp.split("/")[-1],
            "len": len,
        },
    )


@router.post("/edit/{path:path}")
async def save(request: Request, path: str):
    try:
        data = await request.json()
        s3.put_object(Bucket=S3_BUCKET, Key=path, Body=data["content"])
    except (NoCredentialsError, PartialCredentialsError):
        return HTMLResponse("Credentials not available", status_code=403)
    return HTMLResponse("File saved successfully", status_code=200)


@router.post("/upload")
async def upload_file(request: Request):
    form = await request.form()
    file, dest = form.get("file"), form.get("path")
    if not file or not dest:
        return HTMLResponse("No file or path provided", status_code=400)
    try:
        s3.put_object(
            Bucket=S3_BUCKET, Key=f"{dest}{file.filename}", Body=await file.read()
        )
    except (NoCredentialsError, PartialCredentialsError):
        return HTMLResponse("Credentials not available", status_code=403)
    return HTMLResponse("File uploaded successfully", status_code=200)
