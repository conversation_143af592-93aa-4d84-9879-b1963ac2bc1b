import asyncio
import random
import string

import markdown

from llm_api.blai_api.dtos import Message
from llm_api.blai_llm.llm import chat_about
from llm_api.eval.config import rag_flags
from llm_api.eval.constants import EVAL_ORGANIZATION_ID
from llm_api.eval.eval_utils import retry_with_exponential_delay
from llm_api.eval.judge import evaluate_answer
from llm_api.eval.metrics import Test
from llm_api.eval.ragas_metrics import ragas_evaluate


def _rid(n: int = 12) -> str:
    chars = string.ascii_letters + string.digits
    return "".join(random.choice(chars) for _ in range(n))


@retry_with_exponential_delay(max_retries=10, initial_delay=2)
async def run_test(idx: int, test: dict, components: list, q):
    ti = Test.start()
    ti.set("question", test["question"])
    ti.set("ground truth", test["answer"])

    body = Message(
        message=test["question"],
        organizationId=EVAL_ORGANIZATION_ID,
        conversationId=_rid(),
        messageId=_rid(),
        consumerId="ce287120-fdf1-4271-ac31-e2de5ca8201f",
        consumerType=0,
        aiFilteringPayload={"components": components},
    )
    body.format_final_answer = False

    q.put(("start", idx))

    @retry_with_exponential_delay(max_retries=5, initial_delay=2)
    async def _chat():
        return await chat_about(
            body=body, options={"return_dict": True, "include_source_documents": True}
        )

    try:
        resp = await _chat()
    except Exception as e:
        ti.set("error", str(e))
        return ti.end()

    judge = evaluate_answer(test["question"], test["answer"], resp["answer"])
    ti.set("correct", judge["strength"] >= 3)
    ti.set("accuracy", judge["accuracy"])
    ti.set("completeness", judge["completeness"])
    ti.set("clarity", judge["clarity"])
    ti.set("answer", markdown.markdown(resp["answer"]))
    ti.set("reasoning", judge["reasoning"])

    if rag_flags.INCLUDE_RAGAS_METRICS_IN_EVAL:
        metrics = await ragas_evaluate(
            question=test["question"],
            answer=resp["answer"],
            context=[d.page_content for d in resp["source_documents"]],
            ground_truth=test["answer"],
        )
        for m, v in metrics.scores[0].items():
            ti.set(m.replace("_", " "), f"{v:.2f}")

    sources_md = "\n".join(f"* {s} " for s in resp["sources"])
    sources_md += "\n\n# Dev Logs\n" + "\n".join(resp["dev_logs"])
    ti.set("sources", markdown.markdown(sources_md))
    if "query" in resp:
        ti.set("query", resp["query"])
    ti.set("cost", round(resp["cost"], 5))
    return ti.end()


def run_test_sync(idx, test, components, q):
    return asyncio.run(run_test(idx, test, components, q))
