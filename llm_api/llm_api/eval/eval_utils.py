import asyncio
import time
from functools import wraps


def retry_with_exponential_delay(max_retries=5, initial_delay=5):
    def decorator(func):
        is_coroutine = asyncio.iscoroutinefunction(func)

        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            delay = initial_delay
            for attempt in range(max_retries):
                try:
                    if attempt > 0:
                        print(f"Retrying {func.__name__} attempt {attempt + 1}")
                    return await func(*args, **kwargs)
                except Exception as e:
                    print(e)
                    import traceback

                    traceback.print_exc()
                    if "content filter" in str(e):
                        raise e
                    if attempt < max_retries - 1:
                        await asyncio.sleep(delay)
                        delay *= 2
                    else:
                        raise e

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            delay = initial_delay
            for attempt in range(max_retries):
                try:
                    if attempt > 0:
                        print(f"Retrying {func.__name__} attempt {attempt + 1}")
                    return func(*args, **kwargs)
                except Exception as e:
                    print(e)
                    import traceback

                    traceback.print_exc()
                    if "content filter" in str(e):
                        raise e
                    if attempt < max_retries - 1:
                        time.sleep(delay)
                        delay *= 5
                    else:
                        raise e

        return async_wrapper if is_coroutine else sync_wrapper

    return decorator
