from __future__ import annotations

import json
import logging
import random
from datetime import datetime
from typing import Any, <PERSON><PERSON><PERSON><PERSON><PERSON>, Dict, List

import boto3
from opensearchpy import AsyncOpenSearch
from opensearchpy.exceptions import OpenSearchException, RequestError

from llm_api.blai_llm.constants import S3_BUCKET
from llm_api.eval.generate_eval_questions import generate_eval_questions
from llm_api.eval.opensearch_utils import client_kwargs
from llm_api.retrievers.opensearch_retriever import OpenSearchRetriever

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

s3 = boto3.client("s3")
PRINT, REDIRECT = "print", "redirect"


class InsufficientQuestions(Exception):
    """Raised when q_count cannot be satisfied after processing every URL."""


def _html(msg: str, c: str | None = None) -> str:
    return f'<div style="color:{c}">{msg}</div>' if c else f"<div>{msg}</div>"


def _date_filter(start: str, end: str) -> Dict[str, Any]:
    rng = {"range": {"published_date": {"gte": start, "lte": end}}}
    return {"bool": {"should": [rng], "minimum_should_match": 1}}


async def _ensure_url_mapping(cl: AsyncOpenSearch, index: str) -> None:
    mapping = await cl.indices.get_mapping(index=index)
    props = mapping[index]["mappings"].get("properties", {})
    url_prop = props.get("url") or {}
    needs_update = False
    fields = url_prop.get("fields") or {}
    if "keyword" not in fields:
        fields["keyword"] = {"type": "keyword"}
        needs_update = True
    if not url_prop.get("fielddata"):
        url_prop["fielddata"] = True
        needs_update = True
    if needs_update:
        await cl.indices.put_mapping(
            index=index,
            body={
                "properties": {
                    "url": {
                        "type": "text",
                        "fielddata": True,
                        "fields": fields,
                    }
                }
            },
        )
        logger.debug("Updated mapping for url (keyword + fielddata)")


async def _agg_urls(cl: AsyncOpenSearch, index: str, start: str, end: str) -> List[str]:
    body = {
        "size": 0,
        "query": {"bool": {"filter": _date_filter(start, end)}},
        "aggs": {"urls": {"terms": {"field": "url", "size": 100_000}}},
    }
    res = await cl.search(index=index, body=body)
    buckets = res["aggregations"]["urls"]["buckets"]
    urls = [b["key"] for b in buckets]
    logger.debug("Terms agg retrieved %d urls", len(urls))
    return urls


async def _url_buckets(index: str, start: str, end: str) -> List[str]:
    cl = AsyncOpenSearch(**client_kwargs())
    try:
        await _ensure_url_mapping(cl, index)
        urls = await _agg_urls(cl, index, start, end)
        return urls
    finally:
        await cl.close()


async def build_blogs_eval(
    start: str, end: str, q_count: int, q_per: int = 3
) -> AsyncGenerator[tuple[str, str], None]:
    """
    Build an evaluation config with exactly `q_count` questions.
    Keeps iterating through shuffled URLs until the target is met or all URLs are exhausted.
    """
    index = OpenSearchRetriever.get_default_params()["index_name"]
    yield PRINT, _html(f"Using index <b>{index}</b>")
    yield PRINT, _html("Fetching URLs…")

    try:
        urls = await _url_buckets(index, start, end)
    except OpenSearchException as e:
        yield PRINT, _html(f"OpenSearch error: {e}", "red")
        return

    if not urls:
        yield PRINT, _html("No URLs in that date range.", "orange")
        return

    random.shuffle(urls)
    total_urls = len(urls)
    yield PRINT, _html(f"Found {total_urls} candidate URLs")

    tests: List[Dict[str, Any]] = []
    processed = 0
    cl = AsyncOpenSearch(**client_kwargs())
    try:
        for url in urls:
            if len(tests) >= q_count:
                break

            processed += 1
            need = min(q_per, q_count - len(tests))
            yield PRINT, _html(f"[{processed}/{total_urls}] {url}  (need {need})")

            body = {
                "query": {
                    "bool": {
                        "should": [
                            {"term": {"url.keyword": url}},
                            {"match_phrase": {"url": url}},
                        ],
                        "minimum_should_match": 1,
                        "filter": _date_filter(start, end),
                    }
                },
                "_source": ["content"],
                "size": 1000,
            }
            try:
                res = await cl.search(index=index, body=body)
            except OpenSearchException as e:
                logger.debug("OpenSearch error on %s: %s", url, e)
                continue

            chunks = [h["_source"]["content"] for h in res["hits"]["hits"]]
            if not chunks:
                continue

            try:
                qa = generate_eval_questions("\n\n".join(chunks)[:100_000], need)
            except Exception as e:
                logger.debug("LLM error on %s: %s", url, e)
                continue

            tests.extend(qa)
            yield PRINT, _html(
                f"Added {len(qa)} questions (total {len(tests)}/{q_count})"
            )
    finally:
        await cl.close()

    if len(tests) < q_count:
        raise InsufficientQuestions("Could not generate enough questions")

    name = f"blogs_eval_{datetime.utcnow():%Y%m%d_%H%M%S}"
    key = f"experiments/{name}.json"
    s3.put_object(
        Bucket=S3_BUCKET,
        Key=f"eval/{key}",
        Body=json.dumps(
            {
                "name": name,
                "datastore": None,
                "uses_blogs": True,
                "updates": {},
                "tests": tests,
            },
            indent=2,
        ),
        ContentType="application/json",
    )
    yield PRINT, _html(f"Saved as {key}", "green")
    yield REDIRECT, f"/edit/{key}"
