import os

from opensearchpy import AsyncOpenSearch


def _client_kwargs():
    return {
        "hosts": [
            {
                "host": os.environ.get("LLM__OPENSEARCH__HOST", "localhost"),
                "port": int(os.environ.get("LLM__OPENSEARCH__PORT", "9200")),
            }
        ],
        "http_auth": (
            os.environ.get("LLM__OPENSEARCH__USERNAME"),
            os.environ.get("LLM__OPENSEARCH__PASSWORD"),
        ),
        "use_ssl": True,
        "verify_certs": "amazonaws" in os.environ.get("LLM__OPENSEARCH__HOST", ""),
    }


async def blogs_date_bounds(index_name: str) -> tuple[str, str]:
    client = AsyncOpenSearch(**_client_kwargs())
    body = {
        "size": 0,
        "aggs": {
            "oldest": {"min": {"field": "published_date"}},
            "newest": {"max": {"field": "published_date"}},
        },
    }
    res = await client.search(index=index_name, body=body)
    await client.close()
    return (
        res["aggregations"]["oldest"]["value_as_string"][:10],
        res["aggregations"]["newest"]["value_as_string"][:10],
    )


def client_kwargs():
    return _client_kwargs()
