import json
import logging
import os
from functools import lru_cache

import psycopg2

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


@lru_cache
def _fetch_blogs_row():
    conn = psycopg2.connect(
        host=os.getenv("POSTGRES_HOST"),
        port=os.getenv("POSTGRES_PORT"),
        user=os.getenv("POSTGRES_USER"),
        password=os.getenv("POSTGRES_PASS"),
        dbname=os.getenv("POSTGRES_DB"),
    )
    with conn, conn.cursor() as cur:
        cur.execute(
            """
            SELECT "Id",
                   "DisplayName",
                   "DisplayDescription",
                   "Spec"
            FROM "Tool"
            WHERE "Type" = 'publicDataSource'
              AND "ConfiguredBy" = 'public'
            LIMIT 1
            """
        )
        row = cur.fetchone()
    conn.close()
    return row  # (id, name, desc, spec_json)


def get_blogs_component():
    id_, name, desc, spec_json = _fetch_blogs_row()

    spec = spec_json if isinstance(spec_json, dict) else json.loads(spec_json)
    spec.update(
        {
            "display_name": name,
            "display_description": desc,
        }
    )
    return {
        "id": id_,
        "type": "publicDataSource",
        "spec": spec,
        "children": [],
    }
