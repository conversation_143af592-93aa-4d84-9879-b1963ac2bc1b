from llm_api.eval.constants import EVAL_ORGANIZATION_ID


def create_datastore_component(datastore_id: str, datastore_description: str):
    return {
        "id": datastore_id,
        "type": "datastore",
        "spec": {
            "display_name": "test",
            "display_description": datastore_description,
            "organization_id": EVAL_ORGANIZATION_ID,
            "datastore_id": datastore_id,
        },
        "children": [],
    }
