import asyncio
import json
import logging
import multiprocessing

from llm_api.blai_llm.constants import S3_BUCKET
from llm_api.eval.components.blogs import get_blogs_component
from llm_api.eval.components.datastore import create_datastore_component
from llm_api.eval.config import rag_flags, s3
from llm_api.eval.datastores import get_datastore_config, load_datastore
from llm_api.eval.metrics import Metrics
from llm_api.eval.stats import process_results
from llm_api.eval.storage import (
    get_experiment_config,
    hash_string,
    meta_key,
    results_key,
    s3_object_exists,
)
from llm_api.eval.test_worker import run_test_sync

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


async def run_experiment(name: str):
    cfg = get_experiment_config(name)
    comps = []

    yield ("print", f"<div>Running experiment {cfg['name']}</div>")
    await asyncio.sleep(0)

    # datastore component (optional)
    ds_id = None
    if cfg.get("datastore"):
        ds_cfg = get_datastore_config(cfg["datastore"])
        ds_id = hash_string(f"{cfg['datastore']}_{cfg['updates']}_{ds_cfg}")
        comps.append(create_datastore_component(ds_id, ds_cfg["description"]))

    # blogs component (optional)
    if cfg.get("uses_blogs", False):
        comps.append(get_blogs_component())

    # set flags
    for f, v in cfg["updates"].items():
        rag_flags.set_flag(f, v)

    yield ("print", "<div>Loaded flags</div>")
    await asyncio.sleep(0)

    # load datastore if needed
    if ds_id:
        ds_meta = f"eval/experiments/metadata/datastores/{ds_id}"
        if not s3_object_exists(ds_meta):
            await load_datastore(cfg["datastore"], ds_id)
            s3.put_object(Bucket=S3_BUCKET, Key=ds_meta, Body="loaded")
        yield ("print", "<div>Loaded data store</div>")
        await asyncio.sleep(0)

    # metrics setup
    Metrics.start_experiment(name)
    Metrics.expect("correct", "bool")
    Metrics.expect("question", "string")
    Metrics.expect("answer", "string")
    Metrics.expect("ground truth", "string")

    manager = multiprocessing.Manager()
    q = manager.Queue()

    workers = 8
    if rag_flags.INCLUDE_RAGAS_METRICS_IN_EVAL:
        workers = 5
    if cfg.get("uses_blogs", False):
        workers = 5

    with multiprocessing.Pool(processes=workers) as pool:
        async_res = [
            pool.apply_async(run_test_sync, args=(i, t, comps, q))
            for i, t in enumerate(cfg["tests"])
        ]

        total = len(cfg["tests"])
        done = 0
        for _ in range(total):
            evt, _ = q.get()
            if evt == "start":
                done += 1
                yield ("print", f"<div>Running test {done}/{total}</div>")
                await asyncio.sleep(0)

        results = [r.get() for r in async_res]

    for r in results:
        Metrics.add(r)
    data = Metrics.end_experiment()

    # update metadata / save results
    mk = meta_key(name)
    if s3_object_exists(mk):
        meta = json.loads(
            s3.get_object(Bucket=S3_BUCKET, Key=mk)["Body"].read().decode("utf-8")
        )
    else:
        meta = {"runs": 0}
    meta["runs"] += 1
    s3.put_object(Bucket=S3_BUCKET, Key=mk, Body=json.dumps(meta))

    rk = results_key(name, meta["runs"])
    s3.put_object(Bucket=S3_BUCKET, Key=rk, Body=json.dumps(data))

    yield ("redirect", f"/results/{name}_{meta['runs']}")


def get_experiment_results(name: str):
    experiment = "_".join(name.split("_")[:-1])
    key = f"eval/experiments/results/{experiment}/{name}.json"
    obj = s3.get_object(Bucket=S3_BUCKET, Key=key)
    return process_results(json.loads(obj["Body"].read().decode("utf-8")))
