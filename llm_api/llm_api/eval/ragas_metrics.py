import asyncio
import logging
import os
import typing as t

import nest_asyncio
from datasets import Dataset
from langchain_openai import OpenAIEmbeddings
from langchain_openai.chat_models import ChatOpenAI
from ragas import evaluate
from ragas.callbacks import new_group
from ragas.embeddings import LangchainEmbeddingsWrapper
from ragas.llms import Langchain<PERSON><PERSON><PERSON>rapper
from ragas.metrics import (
    answer_relevancy,
    context_entity_recall,
    context_precision,
    context_recall,
    faithfulness,
)
from ragas.metrics.base import MetricWithEmbeddings, MetricWithLLM
from ragas.run_config import RunConfig

from llm_api.eval.eval_utils import retry_with_exponential_delay

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY")
OPENAI_ORGANIZATION = os.environ.get("OPENAI_ORGANIZATION")


def _build_llm() -> ChatOpenAI:
    return ChatOpenAI(
        model_name="gpt-4o-mini",
        temperature=0,
        openai_organization=OPENAI_ORGANIZATION,
        openai_api_key=OPENAI_API_KEY,
    )


def _build_embeddings() -> OpenAIEmbeddings:
    return OpenAIEmbeddings(
        openai_organization=OPENAI_ORGANIZATION,
        openai_api_key=OPENAI_API_KEY,
        model="text-embedding-ada-002",
    )


def _build_metrics(
    ragas_model: LangchainLLMWrapper, ragas_embeddings: LangchainEmbeddingsWrapper
):
    metrics = [
        faithfulness,
        answer_relevancy,
        context_recall,
        context_precision,
        context_entity_recall,
    ]
    run_config = RunConfig()
    for metric in metrics:
        if isinstance(metric, MetricWithLLM):
            metric.llm = ragas_model
        if isinstance(metric, MetricWithEmbeddings):
            metric.embeddings = ragas_embeddings
        metric.init(run_config)
    return metrics


def run_coroutine(coroutine):
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    return loop.run_until_complete(coroutine)


@retry_with_exponential_delay(max_retries=10, initial_delay=2)
async def ragas_evaluate(question, answer, context, ground_truth):
    if "uvloop" in str(type(asyncio.get_event_loop())):
        asyncio.set_event_loop(asyncio.new_event_loop())

    nest_asyncio.apply()

    llm = _build_llm()
    ragas_model = LangchainLLMWrapper(llm)
    embeddings = _build_embeddings()
    ragas_embeddings = LangchainEmbeddingsWrapper(embeddings)
    metrics = _build_metrics(ragas_model, ragas_embeddings)

    data = Dataset.from_dict(
        {
            "user_input": [question],
            "response": [answer],
            "retrieved_contexts": [context],
            "reference": [ground_truth],
        }
    )

    evaluation_rm, evaluation_group_cm = new_group(
        name="ragas evaluation", inputs={}, callbacks=[]
    )

    for i, row in enumerate(data):
        row = t.cast(t.Dict[str, t.Any], row)
        row_rm, row_group_cm = new_group(
            name=f"row {i}", inputs=row, callbacks=evaluation_group_cm
        )
        results = evaluate(dataset=data, metrics=metrics, llm=llm)

    await llm.client.aclose()
    logger.info(f"Results: {results}")
    return results


if __name__ == "__main__":
    score = asyncio.run(
        ragas_evaluate(
            question="When was the CVE-2024-26169 vulnerability patched by Microsoft?",
            answer="The CVE-2024-26169 vulnerability was patched by Microsoft in March 2024.",
            context=[
                "Threat actors linked to the Black Basta ransomware may have exploited a recently disclosed privilege escalation flaw in the Microsoft Windows Error Reporting Service as a zero-day, according to new findings from Symantec.\n\nThe security flaw in question is CVE-2024-26169 (CVSS score: 7.8), an elevation of privilege bug in the Windows Error Reporting Service that could be exploited to achieve SYSTEM privileges. It was patched by Microsoft in March 2024.",
                "Cybersecurity researchers have detailed a now-patched security flaw affecting the Ollama open-source artificial intelligence (AI) infrastructure platform that could be exploited to achieve remote code execution.\n\nTracked as CVE-2024-37032, the vulnerability has been codenamed Probllama by cloud security firm Wiz. Following responsible disclosure on May 5, 2024, the issue was addressed in version 0.1.34 released on May 7, 2024.",
            ],
            ground_truth="March 2024",
        )
    )
    print(score)
