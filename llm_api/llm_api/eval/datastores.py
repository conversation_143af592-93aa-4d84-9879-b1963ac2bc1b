import asyncio
import json

import boto3

from llm_api.eval.constants import EVAL_ORGANIZATION_ID
from llm_api.eval.rag_flags import RAGFlags

rag_flags = RAGFlags.get_instance()

from llm_api.blai_api.dtos import DataStoreDocument, FileBytes
from llm_api.blai_llm.constants import S3_BUCKET

s3 = boto3.client("s3")


def eval_datastore_config_path(config_name):
    return f"eval/data/{config_name}.json"


def get_datastore_config(config_name):
    path = eval_datastore_config_path(config_name)
    response = s3.get_object(Bucket=S3_BUCKET, Key=path)
    file_content = response["Body"].read().decode("utf-8")
    return json.loads(file_content)


async def load_datastore(config_name, data_store_id="test_data"):
    from llm_api.blai_llm.data_stores import DataStoreManager

    config = get_datastore_config(config_name)

    if rag_flags.USE_UPLOAD_V2_PATH:
        # get files from s3 in binary
        files = []
        for file in config["files"]:
            response = s3.get_object(Bucket=S3_BUCKET, Key="eval/data/" + file)
            file_content = response["Body"].read()
            file_name = file.split("/")[-1]
            files.append(FileBytes(file_bytes=file_content, file_name=file_name))

        body = DataStoreDocument(
            organizationId=EVAL_ORGANIZATION_ID,
            dataStoreId=data_store_id,
            binaryFiles=files,
            documentType="document",
            userId="some-user-id",
        )

        # use v2 path
        print("Uploading files to data store using V2 path")
        await DataStoreManager().upload_documents(body=body)
    else:
        body = DataStoreDocument(
            organizationId=EVAL_ORGANIZATION_ID,
            dataStoreId=data_store_id,
            documents=["eval/data/" + file for file in config["files"]],
            documentType="pdf",
            userId="some-user-id",
        )

        DataStoreManager().upload_document(body=body)


# load_datastore("kb")
