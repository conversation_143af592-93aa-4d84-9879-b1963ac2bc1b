<!DOCTYPE html>
<html>

<head>
  <title>Edit JSON File</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.1/codemirror.min.css">
  <style>
    body {
      font-family: monospace;
    }

    .CodeMirror {
      border: 1px solid #ccc;
      height: 80vh;
    }

    #loadingOverlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(255, 255, 255, 0.8);
      display: none;
      align-items: center;
      justify-content: center;
      font-size: 2em;
      color: #333;
    }
  </style>
</head>

<body>
  <h1>Editing: {{ file_path }}
    {% if file_path.startswith('eval/experiments') and len(file_path.split("/")) == 3 %}
    <a href="/run/{{ file_path }}"><button>🚀 run experiement</button></a>
    <a href="/browse/experiments/results/{{ filename.replace('.json', '') }}/"><button>📊 browse results</button></a>
    {% endif %}
  </h1>
  <form id="editorForm" onsubmit="return saveFile();">
    <textarea id="editor" name="content" style="display: none;">{{ file_content }}</textarea>
    <div style="margin-top: 20px">
      <button type="submit">Save</button>
    </div>
  </form>
  <div id="loadingOverlay">Saving...</div>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.1/codemirror.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.1/mode/javascript/javascript.min.js"></script>
  <script>
    var editor = CodeMirror.fromTextArea(document.getElementById('editor'), {
      mode: "application/json",
      lineNumbers: true,
      theme: "default"
    });

    function validateJson() {
      try {
        JSON.parse(editor.getValue());
      } catch (e) {
        alert("Invalid JSON");
        return false;
      }
      return true;
    }

    function saveFile() {
      if (!validateJson()) {
        return false;
      }
      document.getElementById('loadingOverlay').style.display = 'flex';
      var xhr = new XMLHttpRequest();
      xhr.open("POST", "/edit/{{ file_path }}", true);
      xhr.setRequestHeader("Content-Type", "application/json;charset=UTF-8");
      xhr.onreadystatechange = function () {
        if (xhr.readyState === 4) {
          document.getElementById('loadingOverlay').style.display = 'none';
          if (xhr.status === 200) {
            // alert("File saved successfully!");
          } else {
            alert("Error saving file: " + xhr.statusText);
          }
        }
      };
      xhr.send(JSON.stringify({ content: editor.getValue() }));
      return false;
    }
  </script>
</body>

</html>