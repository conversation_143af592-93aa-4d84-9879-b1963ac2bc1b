<!DOCTYPE html>
<html>
<head>
  <title>S3 Browser</title>
  <style>
    body {
      font-family: monospace;
    }
    .folder {
      color: blue;
    }
    .file {
      color: green;
    }
    .overlay {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      z-index: 1000;
      text-align: center;
      padding-top: 20%;
      color: white;
      font-size: 20px;
    }
  </style>
</head>
<body>
  <h1>Browsing: 
    {% for breadcrumb in breadcrumbs[:-1] %}
      <a href="/browse/{{ breadcrumb['path'] }}">{{ breadcrumb["name"] }}</a> / 
    {% endfor %}
    {{ breadcrumbs[-1]["name"] }}
    <button id="uploadButton">Upload File</button>
    <input type="file" id="fileInput" style="display:none"/>
  </h1>
  <ul>
    {% for folder in folders %}
      <li class="folder">
        <a href="/browse/{{ path }}{{ folder }}/">{{ '📁 ' + folder }}</a>
      </li>
    {% endfor %}
    {% for file in files %}
      <li class="file">
        <a href="/edit/{{ path }}{{ file }}">{{ '📝 ' + file }}</a>
      </li>
    {% endfor %}
  </ul>

  <div class="overlay" id="overlay">Uploading...</div>

  <script>
    document.getElementById('uploadButton').addEventListener('click', () => {
      document.getElementById('fileInput').click();
    });

    document.getElementById('fileInput').addEventListener('change', async (event) => {
      const file = event.target.files[0];
      if (file) {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('path', '{{ current_path }}');

        document.getElementById('overlay').style.display = 'block';

        const response = await fetch('/upload', {
          method: 'POST',
          body: formData
        });

        document.getElementById('overlay').style.display = 'none';

        if (response.ok) {
          window.location.reload();
        } else {
          alert('File upload failed');
        }
      }
    });
  </script>
</body>
</html>
