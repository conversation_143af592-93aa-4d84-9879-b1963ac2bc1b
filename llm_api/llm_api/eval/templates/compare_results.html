<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Comparison Results</title>
  <style>
    table {
      width: 100%;
      border-collapse: collapse;
    }
    th, td {
      border: 1px solid #ddd;
      padding: 8px;
    }
    th {
      background-color: #f2f2f2;
    }
    .second-result {
      background-color: #f2f2f2;
    }
  </style>
</head>
<body>
  <h1>Comparison Results</h1>
  <p>{{ first_results["experiment_name"] }} Accuracy: {{ "%.2f"|format(comparison.accuracies.first * 100) }}% 
     {{ second_results["experiment_name"] }} Accuracy: {{ "%.2f"|format(comparison.accuracies.second * 100) }}%</p>
  <p>p-value = {{ "%.4f"|format(comparison["p_value"]) }} - {{ "siginificant" if comparison["significant_results"] else "not siginificant"}}</p>
  <p>Wins: {{ comparison.counts.wins }} Losses: {{ comparison.counts.losses }} Ties: {{ comparison.counts.ties }} First: {{ comparison.counts.first_only }} Second: {{ comparison.counts.second_only }}</p>
  {% for category in ['wins', 'losses', 'ties', 'first_only', 'second_only'] %}
    <h2>{{ category | capitalize }}</h2>
    {% if comparison[category] %}
      <table>
        <thead>
          <tr>
            {% for header in comparison.metrics %}
              <th>{{ header }}</th>
            {% endfor %}
          </tr>
        </thead>
        <tbody>
          {% for test_pair in comparison[category] %}
            {% if test_pair[0] %}
              <tr>
                {% for metric in comparison.metrics %}
                  {% if metric == "correct" %}
                    <td>{{ "✅" if test_pair[0][metric] else "❌" }}</td>
                  {% else %}
                    <td>{{ test_pair[0][metric]|safe }}</td>
                  {% endif %}
                {% endfor %}
              </tr>
            {% endif %}
            {% if test_pair[1] %}
              <tr class="second-result">
                {% for metric in comparison.metrics %}
                  {% if metric == "correct" %}
                    <td>{{ "✅" if test_pair[1][metric] else "❌" }}</td>
                  {% else %}
                    <td>{{ test_pair[1][metric]|safe }}</td>
                  {% endif %}
                {% endfor %}
              </tr>
            {% endif %}
          {% endfor %}
        </tbody>
      </table>
    {% else %}
      <p>No {{ category }}</p>
    {% endif %}
  {% endfor %}
</body>
</html>
