<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Experiment Results</title>
  <style>
    table {
      width: 100%;
      border-collapse: collapse;
    }

    th,
    td {
      border: 1px solid #ddd;
      padding: 8px;
    }

    th {
      background-color: #f2f2f2;
    }
  </style>
</head>

<body>
  <h1>Experiment Results -
    <a href="/edit/experiments/{{ results.experiment_name }}.json">{{ results.experiment_name }}</a>
  </h1>
  <p>Duration: {{ "%.2f"|format(results.duration) }} seconds</p>
  <div>
    {{ results.processed.accuracy * 100 }}% Accuracy
  </div>
  <table>
    <thead>
      <tr>
        {% for header in results.metrics %}
        <th>{{ header }}</th>
        {% endfor %}
      </tr>
    </thead>
    <tbody>
      {% for test in results.tests %}
      <tr>
        {% for metric in results.metrics %}
        {% if metric == "correct" %}
        <td>{{ "✅" if test[metric] else "❌" }}</td>
        {% elif metric == "duration" %}
        <td>{{ "%.2f"|format(test.duration) }}</td>
        {% else %}
        <td>{{ test[metric]|safe }}</td>
        {% endif %}
        {% endfor %}

      </tr>
      {% endfor %}
    </tbody>
  </table>
</body>

</html>