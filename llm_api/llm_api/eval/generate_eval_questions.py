import json
from typing import Dict, List

from langchain_core.messages import HumanMessage, SystemMessage

from llm_api.llm.factory import (
    default_4_gpt_spec_data_json_enabled,
    get_model_from_spec,
)
from llm_api.specs.llm_spec import LLMSpec, LLMType

_llm = get_model_from_spec(
    spec=LLMSpec(
        type=LLMType.AzureChatOpenAI,
        data=default_4_gpt_spec_data_json_enabled,
    )
)

# ---------------------------------------------------------------------- PROMPTS

_FACT_EXTRACTION_PROMPT = """You are a tool that extracts stand-alone facts from raw text.

For every sentence that contains a named entity (product, company, person, CVE, law, country, etc.)
return an object:
  {"id": n,
   "entity": "<entity-string>",
   "fact": "<a self-contained statement about the entity without repeating the entity>",
   "time_anchor": "<explicit date / year / version / monetary amount / exact count>"}

Rules
• entity = exact span as it appears in the text, 1–4 words.
• fact = concrete statement ≥ 4 words, MUST NOT contain the entity.
• time_anchor is required if the fact has any numeric or temporal detail.
• Drop the sentence if it lacks both a time_anchor and a numeric / version detail.
• Skip author names, headlines, vague opinions.
• Facts extracted should be independent and self-contained.

GOOD ✔
Sentence: "Firefox users must upgrade to version 128 to patch CVE-2024-7100 released in July 2024."
→ {"id": 1,
   "entity": "Firefox",
   "fact": "users must upgrade to version 128 to patch CVE-2024-7100 released in July 2024",
   "time_anchor": "July 2024"}

Return JSON:
{
  "facts": [
    {"id": 1, "entity": "...", "fact": "...", "time_anchor": "..."},
    ...
  ]
}"""

_QG_PROMPT = """Convert each record into a self-contained Q-A pair.

Input "facts": list of {"id": n, "entity": e, "fact": f, "time_anchor": t}.

Rules
1. Build ONE pair per record.
2. **Question** must contain the entity and the time_anchor or another unique numeric/version detail from f.
   Start with Who, What, Which, When, Where, Why, or How many.
3. **Answer** = fact verbatim (case-sensitive) and must NOT contain the entity.
4. Question ≤ 25 words; answer ≤ 20 words.
5. Reject any question that begins with:
   "What has", "Which vulnerabilities has", "What critical vulnerability",
   "What poses", "Why do many", "What characteristic",
   "What topic", "What does the book", "How is AWS utilized",
   "How is Azure utilized", "How is GCP utilized".
6. Preserve the original id.

GOOD ✔
{"id": 1, "entity": "Firefox", "fact": "users must upgrade to version 128 to patch CVE-2024-7100 released in July 2024", "time_anchor": "July 2024"}
→ {"id": 1,
   "question": "To which version did Firefox users need to upgrade in July 2024 to fix CVE-2024-7100?",
   "answer": "users must upgrade to version 128 to patch CVE-2024-7100 released in July 2024"}

Return JSON:
{
  "qa": [
    {"id": 1, "question": "...", "answer": "..."},
    ...
  ]
}"""

_VALIDATION_PROMPT = """Validate each Q-A pair against the source DOC.

A pair is valid if:
• answer appears verbatim exactly once in DOC,
• question contains the entity and a time/version/amount anchor,
• answer does NOT contain the entity,
• question starts with Who, What, Which, When, Where, Why, or How many,
• question does not start with any black-listed template.

Return JSON:
{
  "valid": [
    {"id": 1, "valid": true},
    ...
  ]
}"""

# ---------------------------------------------------------------------- HELPERS


def _invoke_llm(messages) -> str:
    return _llm.invoke(messages).content.strip()


def _extract_facts(text: str, k: int) -> List[Dict]:
    messages = [
        SystemMessage(content=_FACT_EXTRACTION_PROMPT),
        HumanMessage(content=f'K = {k}\n\nTEXT:\n"""\n{text}\n"""'),
    ]
    data = json.loads(_invoke_llm(messages))
    return data.get("facts", [])[:k]


def _generate_questions(facts: List[Dict]) -> List[Dict]:
    messages = [
        SystemMessage(content=_QG_PROMPT),
        HumanMessage(content=json.dumps({"facts": facts}, ensure_ascii=False)),
    ]
    data = json.loads(_invoke_llm(messages))
    return data.get("qa", [])


def _validate_questions(text: str, qa: List[Dict]) -> List[Dict]:
    messages = [
        SystemMessage(content=_VALIDATION_PROMPT),
        HumanMessage(content=json.dumps({"DOC": text, "QA": qa}, ensure_ascii=False)),
    ]
    data = json.loads(_invoke_llm(messages))
    return data.get("valid", [])


# ---------------------------------------------------------------------- PUBLIC


def generate_eval_questions(text: str, k: int = 10) -> List[Dict]:
    if not text or not text.strip():
        raise ValueError("Input text is empty")
    if k <= 0:
        raise ValueError("k must be positive")

    facts = _extract_facts(text, max(k * 3, 15))
    if not facts:
        return []

    qa_pairs = _generate_questions(facts)
    if not qa_pairs:
        return []

    valid_map = {v["id"]: v["valid"] for v in _validate_questions(text, qa_pairs)}

    out: List[Dict] = []
    seen: set[str] = set()
    for pair in qa_pairs:
        if not valid_map.get(pair["id"], False):
            continue
        q = pair.get("question", "").strip()
        a = pair.get("answer", "").strip()
        if not q or not a or q in seen:
            continue
        seen.add(q)
        out.append({"question": q, "answer": a})
        if len(out) == k:
            break
    return out
