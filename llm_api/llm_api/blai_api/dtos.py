from enum import Enum
from typing import Annotated, Any, Dict, List, Literal, Optional, Tuple, Union

from pydantic import BaseModel, ConfigDict, Extra, Field, Json, RootModel

from llm_api.specs.component_spec import ComponentSpec


class ComponentList(BaseModel):
    components: List[ComponentSpec] = Field(
        description="The list of components used by the brain."
    )


class Message(BaseModel):
    message: str = Field(
        description="The message string.",
    )
    messageId: str = Field(
        description="UUID identifying the message.",
    )
    root_component: Optional[ComponentSpec] = Field(
        description="A root component for the current agents and tools configuration.",
        default=None,
    )
    ai_filtering_payload: Optional[ComponentList] = Field(
        description="A list of specialists and tool that can be added to a pre-defined root.",
        alias="aiFilteringPayload",
        default=None,
    )
    conversationId: Optional[str] = Field(
        description="The ID of the current conversation.",
        default=None,
    )
    organizationId: str = Field(
        description="The ID of the parent organzation of the user."
    )
    user_id: str = Field(
        description="The user_id that triggered this conversation.",
        alias="userId",
        default="",
    )
    format_final_answer: Optional[bool] = Field(
        description="Use formatting for the final AI answer.",
        default=True,
        alias="formatFinalAnswer",
    )
    store_json_location: str = Field(
        description="Optional location for the API plugin to store the API response.",
        default="",
        alias="storeJsonLocation",
    )
    consumer_id: str = Field(
        description="The ID of the initiator",
        default="",
        alias="consumerId",
    )
    consumer_type: int = Field(
        description="The type of consumer",
        default=-1,
        alias="consumerType",
    )
    local_timezone: Optional[str] = Field(
        description="The local time of the user",
        default=None,
        alias="localTimeZone",
    )


class EvidenceOrigin(Enum):
    Tool = "TOOL"
    Plugin = "PLUGIN"
    Url = "URL"
    Datastore = "DATASTORE"
    PublicDatastore = "PUBLIC_DATASTORE"
    Memory = "MEMORY"
    Report = "report"
    Code = "code"


class LTRB(BaseModel):
    kind: Literal["LTRB"] = "LTRB"
    """Left(x0) Top(y0) Right(x1) Bottom(y1)"""
    x0: float
    y0: float
    x1: float
    y1: float


Coord = Annotated[Union[LTRB], Field(discriminator="kind")]


class PDFCoordinates(BaseModel):
    kind: Literal["PDFCoordinates"] = "PDFCoordinates"
    coord: Coord = Field(description="On Page Coordinates")
    page_number: int = Field(description="Page Number")
    category: str = Field(description="Category of the document")
    layout_width: float = Field(description="Width of the page")
    excerpt: Optional[str] = Field(
        default=None, description="Excerpt of the text within the coordinates"
    )


class Excerpt(BaseModel):
    kind: Literal["Excerpt"] = "Excerpt"
    excerpt: str = Field(
        default=None, description="Excerpt of the text within the coordinates"
    )


EvidenceTrace = Annotated[
    Union[None, PDFCoordinates, Excerpt], Field(discriminator="kind")
]


class PDFAnnotationRequest(BaseModel):
    source: str = Field(
        description="Source of the annotation. Same as Evidence.evidence_location."
    )
    coordinates: List[PDFCoordinates] = Field(
        description="Coordinates of the annotation"
    )


class PDFAnnotationResponse(BaseModel):
    file_name: str = Field(description="File name of the annotation")
    file_content: str = Field(description="Encoded content of the annotation")


class Evidence(BaseModel):
    model_config = ConfigDict(extra="allow", use_enum_values=True)

    name: str = Field(description="The display name for this piece of evidence")
    evidence_location: str = Field(
        description="The location where the evidence is stored. (can also be an URL)",
        alias="evidenceLocation",
    )
    origin: EvidenceOrigin = Field(description="The origin/type of this evidence")
    trace: EvidenceTrace = Field(description="The evidence trace.", default=None)

    page_title: Optional[str] = Field(
        default=None,
        description="The title of the page from which the evidence was sourced",
    )


class MessageResponse(BaseModel):

    answer: str = Field(description="The AI answer.")
    sources: List[str] = Field(description="The list of sources for this answer.")
    evidence: List[Evidence] = Field(
        description="A list of stored evidences used in the LLM call",
        default=[],
    )
    plan: List[str] = Field(
        description="A list containg the plan used to arrive at this answer."
    )
    cost: float = Field(description="The total cost of the AI call.")


class MessageError(BaseModel):
    message: str
    cost: float = Field(
        description="The cost of the AI call up until the exception occurred."
    )


class Train(BaseModel):
    training: Enum("CSV", "SOAR")


class FileBytes(BaseModel):
    file_bytes: bytes
    file_name: str


class DataStoreDocument(BaseModel):
    organizationId: str
    dataStoreId: str
    documents: Optional[List[str]] = None
    documentType: Optional[str] = None
    userId: Optional[str] = None
    publishDate: Optional[str] = None
    shouldGenerateDataDescription: Optional[bool] = None


class BlogsDeleteFeedRequest(BaseModel):
    feedId: int
    organizationId: str
    secondaryIndexId: str


class DeleteOrganizationRequest(BaseModel):
    organizationId: str
    secondaryIndexId: str
    dataStoreIds: List[str]


class DataStore(BaseModel):
    organizationId: str
    dataStoreId: str


class CdiProcedureInput(BaseModel):
    input_string: str = Field(
        description="The input in the procedure",
        alias="input",
    )
    org_api_key: str = Field(
        description="The organization API KEY to be used for further API calls",
        alias="orgApiKey",
    )
    organization_id: str = Field(
        description="The organization id",
        alias="organizationId",
    )
    procedure_id: str = Field(
        default="1",
        description="The ID of the procedure",
        alias="procedureId",
    )


class CdiProcedureOutput(BaseModel):
    id: str
    procedure_id: str
    answer: str
    cost: float
    duration_sec: float
    input_truncated: bool
    sources: List[str]


class TestAssertion(BaseModel):
    context: str
    assertion: str
