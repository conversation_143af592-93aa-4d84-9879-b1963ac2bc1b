import datetime
import json
import logging
import os
from io import BytesIO
from pathlib import Path
from typing import Dict, List, Literal, Optional

from fastapi import APIRouter, BackgroundTasks, HTTPException, Query, Request
from fastapi.responses import FileResponse, JSONResponse, StreamingResponse
from newspaper import ArticleException
from pydantic import BaseModel, HttpUrl
from pydantic_settings import BaseSettings
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy.sql import text

from llm_api.annotation.pdf import AnnotatePDF, AnnotatePDFSettings, PDFSearch
from llm_api.blai_api.dtos import (
    BlogsDeleteFeedRequest,
    CdiProcedureInput,
    DataStore,
    DataStoreDocument,
    DeleteOrganizationRequest,
    Evidence,
    Message,
    MessageError,
    PDFAnnotationRequest,
    PDFCoordinates,
    TestAssertion,
    Train,
)
from llm_api.blai_llm.data_stores import DataStoreManager
from llm_api.blai_llm.llm import LLMEx<PERSON>, chat_about
from llm_api.blai_llm.testing import test_assertion
from llm_api.blai_llm.traceable_data_store_manager import TraceableDataStoreManager
from llm_api.code_sandbox.code_sandbox import execute_code
from llm_api.dummy_procedures.cdi_procedure import (
    BudgetExceeded,
    Unauthorized,
    cdi_api_call_to_bricklayer,
)
from llm_api.dummy_procedures.report import create_report
from llm_api.html_to_pdf.html_to_pdf_converter import ArticleToPDF, URLArticleFetcher
from llm_api.procedures.reports_converter.report_converter import (
    convert_report_to_docx,
    convert_report_to_pdf,
)
from llm_api.retrievers.test_date_extractor import run_date_keyword_tests
from llm_api.retrievers.test_keyword_extractor import run_keyword_extractor_tests
from llm_api.retrievers.test_opensearch_retriever import test_build_query_dict
from llm_api.vectorstores.pgvector_integration import CONNECTION_URL

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

router = APIRouter()


class DataStoreConfiguration(BaseModel):
    enable_chunk_traceability: bool = False


class LLMAPIRouterSettings(BaseSettings):
    datastore: DataStoreConfiguration = DataStoreConfiguration()
    annotate: AnnotatePDFSettings


@router.post("/message", responses={500: {"model": MessageError}})
async def message(msg: Message):
    try:
        resp = await chat_about(body=msg)
    except LLMException as err:
        resp = MessageError(
            message=err.message,
            cost=err.cost,
        )
        return JSONResponse(
            content=resp.dict(),
            status_code=500,
        )

    return resp


@router.get("/")
async def root():
    content = {"msg": "Welcome to BricklayerAI LLM API!"}
    return JSONResponse(content=content, status_code=200)


@router.post("/train")
async def train(training: Train):
    return training


@router.post("/alert")
async def alert(msg: Message):
    return {"message": "Alert created."}


@router.post("/datastore/documents/upload")
def datastore_document_upload(
    body: DataStoreDocument,
):
    # async indexing of uploaded file
    # background_tasks.add_task(DataStoreManager().upload_document, body)
    s = LLMAPIRouterSettings()
    # TODO: Returns two completely different kinds of objects.
    res = None
    match s.datastore:
        case DataStoreConfiguration(enable_chunk_traceability=True):
            logger.info("Using Chunk Traceability for upload.")
            res = TraceableDataStoreManager().upload_document(data_store_document=body)
        case DataStoreConfiguration(enable_chunk_traceability=False):
            res = DataStoreManager().upload_document(body=body)
    return res


@router.post("/datastore/documents/annotate")
async def datastore_document_annotate(pdf_annotation_request: PDFAnnotationRequest):
    s = LLMAPIRouterSettings()
    if not s.datastore.enable_chunk_traceability:
        return JSONResponse(
            content={"errors": "Feature is not enabled."},
            status_code=422,  # Unprocessable
        )
    annotate_pdf = AnnotatePDF(settings=s.annotate)
    annotated_pdf_path = await annotate_pdf.acall(pdf_annotation_request)
    res = FileResponse(annotated_pdf_path, media_type="application/pdf")
    return res


class BlogsGeneratePdf(BaseModel):
    blog_url: HttpUrl
    filename: str = "webpage.pdf"


@router.post("/datastore/blogs/generate-pdf")
def blog_generate_pdf(url_request: BlogsGeneratePdf):
    s = LLMAPIRouterSettings()
    if not s.datastore.enable_chunk_traceability:
        return JSONResponse(
            content={"errors": "Feature is not enabled."},
            status_code=422,  # Unprocessable
        )
    fetcher = URLArticleFetcher(url=url_request.blog_url)
    converter = ArticleToPDF()
    output_io = BytesIO()
    try:
        converter(fetcher, output_io)
    except ArticleException as e:
        return JSONResponse(
            content={"errors": str(e)},
            status_code=403,
        )
    output_io.seek(0)

    res = StreamingResponse(
        output_io,
        media_type="application/pdf",
        headers={"Content-Disposition": f"attachment; filename={url_request.filename}"},
    )
    return res


class BlogsSearchPdfRequest(BaseModel):
    blog_url: HttpUrl
    excerpts: List[str]


class BlogsSearchPdfResponse(BaseModel):
    blog_url: HttpUrl
    coords: List[Optional[PDFCoordinates]]


@router.post("/datastore/blogs/search-blog-pdf", response_model=BlogsSearchPdfResponse)
def search_blog_generate_pdf(request: BlogsSearchPdfRequest):
    s = LLMAPIRouterSettings()
    if not s.datastore.enable_chunk_traceability:
        return JSONResponse(
            content={"errors": "Feature is not enabled."},
            status_code=422,  # Unprocessable
        )
    fetcher = URLArticleFetcher(url=request.blog_url)
    converter = ArticleToPDF()
    output_io = BytesIO()
    try:
        converter(fetcher, output_io)
    except ArticleException as e:
        return JSONResponse(
            content={"errors": str(e)},
            status_code=403,
        )
    output_io.seek(0)
    search = PDFSearch(hit_max=1)
    results = []
    for excerpt in request.excerpts:
        res = search(output_io, excerpt)
        if len(res) == 1:
            results.append(res[0])  # Multi-hit disallowed for now
        elif len(res) == 0:
            results.append(None)  # Multi-hit disallowed for now

    return BlogsSearchPdfResponse(blog_url=request.blog_url, coords=results)


@router.post("/datastore/documents/delete")
def datastore_document_delete(body: DataStoreDocument):
    return DataStoreManager().delete_document(body)


@router.post("/datastore/delete")
def delete_datastore(body: DataStore):
    return DataStoreManager().delete_datastore(body)


@router.post("/datastore/organization_feed/delete")
def delete_feed(body: BlogsDeleteFeedRequest):
    return DataStoreManager().delete_blogs_feed_from_indexes(body)


@router.post("/delete-organization")
def delete_organization(body: DeleteOrganizationRequest):
    return JSONResponse(DataStoreManager().delete_organization(body))


@router.post("/cdi_procedure")
async def cdi_procedure(body: CdiProcedureInput):
    try:
        return await cdi_api_call_to_bricklayer(
            procedure_input=body.input_string,
            organization_id=body.organization_id,
            api_key=body.org_api_key,
            procedure_id=body.procedure_id,
        )

    except ExceptionGroup as exc_group:
        errors = []
        for err in exc_group.exceptions:
            logger.error(f"ERROR: {err}")
            if isinstance(err, Unauthorized):
                errors.append(
                    {
                        "error": f"Organization [{body.organization_id}] is not authorized to call procedure."
                    }
                )
            elif isinstance(err, BudgetExceeded):
                errors.append(
                    {
                        "error": f"Budget exceeded.",
                    }
                )
            else:
                errors.append(
                    {
                        "error": f"Something wrong has happened while running the procedure."
                    }
                )
        return JSONResponse(
            content={"errors": errors},
            status_code=500,
        )
    except Exception as e:
        logger.error(e)
        return JSONResponse(
            content={
                "error": f"Something wrong has happened while running the procedure."
            },
            status_code=500,
        )


@router.get("/procedure_report", response_class=StreamingResponse)
def procedure_report(
    orgs: List[str] = Query([], alias="org"),
    since: str = Query(""),
    until: str = Query(""),
    env: str = Query("dev"),
) -> StreamingResponse:
    stream = create_report(env=env, orgs=orgs, since_str=since, until_str=until)
    response = StreamingResponse(iter([stream.getvalue()]), media_type="text/csv")
    response.headers["Content-Disposition"] = (
        f"attachment; filename=report_{datetime.datetime.now().strftime('%Y_%m_%d')}.csv"
    )
    return response


class ReportFormatRequest(BaseModel):
    report: Dict[str, str]  # The report is a dict with string keys and values
    file_type: Literal["pdf", "docx"]
    title: Optional[str] = None  # Optional title


@router.post("/reports/convert")
def convert_report(request: ReportFormatRequest):
    logger.info("Converting report..")
    report_data = request.report
    file_type = request.file_type.lower()
    title = request.title

    if file_type == "pdf":
        pdf_data = convert_report_to_pdf(report_data, title=title)
        return StreamingResponse(
            BytesIO(pdf_data),
            media_type="application/pdf",
            headers={"Content-Disposition": f"attachment; filename=report.pdf"},
        )
    elif file_type == "docx":
        docx_data = convert_report_to_docx(report_data, title=title)
        return StreamingResponse(
            BytesIO(docx_data),
            media_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            headers={"Content-Disposition": f"attachment; filename=report.docx"},
        )
    else:
        raise HTTPException(
            status_code=400, detail="Unsupported file type. Choose 'pdf' or 'docx'."
        )


@router.get("/test-sandbox")
def test_sandbox():
    code = """print("Hello, sandbox!")"""
    result = execute_code(code)
    return result


@router.post("/test-assertion")
def test_assetion_route(
    test: TestAssertion,
):  # suffixed with `_route` so there's no confusion with the imported test_assertion
    return test_assertion(test)


@router.get("/test-blogs")
def test_blogs_route():
    return {"test_build_query_dict": test_build_query_dict()}


@router.get("/test-keyword-extraction")
def test_keyword_extraction_route():
    return {"test_keyword_extractor": run_keyword_extractor_tests()}


@router.get("/test-date-extraction")
def test_keyword_extraction_route():
    return {"test_date_extractor": run_date_keyword_tests()}


healthcheck_engine = create_async_engine(CONNECTION_URL)


@router.get("/healthcheck")
async def healthcheck():
    try:
        engine = healthcheck_engine

        async with engine.begin() as conn:
            await conn.execute(text("SELECT 1"))

        return JSONResponse(
            content={"message": "API and database are healthy"}, status_code=200
        )

    except SQLAlchemyError as e:
        logger.error(f"Database connection failed: {str(e)}")
        return JSONResponse(
            content={"message": "Database connection failed", "error": str(e)},
            status_code=500,
        )
