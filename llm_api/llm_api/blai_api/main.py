import json
import logging
import os
from pathlib import Path

from dotenv import load_dotenv
from fastapi import FastAPI
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from langchain_community.callbacks.openai_info import MODEL_COST_PER_1K_TOKENS
from starlette.requests import Request
from starlette.status import HTTP_422_UNPROCESSABLE_ENTITY

from llm_api.exceptions import HTTPProblem
from llm_api.log_utils import setup_logging

load_dotenv()

logger = logging.getLogger(__name__)


MODEL_COST_PER_1K_TOKENS["gpt-4-1106-preview"] = 0.01
MODEL_COST_PER_1K_TOKENS["gpt-4-1106-preview-completion"] = 0.03

secrets_path = "/mnt/secrets-store/secrets"
if Path(secrets_path).exists():
    with Path(secrets_path).open("r") as f:
        env_vars_json = json.load(f)
        for k, v in env_vars_json.items():
            logger.debug(f"Setting {k} from secrets store")
            os.environ[k] = v

if "SENTRY_DSN" in os.environ:
    import sentry_sdk

    sentry_sdk.init(
        dsn=os.environ["SENTRY_DSN"],
        # Set traces_sample_rate to 1.0 to capture 100%
        # of transactions for tracing.
        traces_sample_rate=float(os.environ.get("SENTRY_TRACES_SAMPLE_RATE", 1.0)),
        # Set profiles_sample_rate to 1.0 to profile 100%
        # of sampled transactions.
        # We recommend adjusting this value in production.
        profiles_sample_rate=float(os.environ.get("SENTRY_PROFILES_SAMPLE_RATE", 1.0)),
        environment=os.environ["BLAI_ENV"],
    )


def http422_error_handler(_: Request, exc: RequestValidationError) -> JSONResponse:
    logger.error(f"Received: {exc.body}")
    logger.error(f"Error: {exc.errors()}")
    return JSONResponse(
        {"errors": exc.errors()}, status_code=HTTP_422_UNPROCESSABLE_ENTITY
    )


def http_problem_error_handler(_: Request, exc: HTTPProblem) -> JSONResponse:
    """
    Send such raised exceptions upstream as Content-Type: application/problem+json.
    """
    return JSONResponse(
        content=exc.problem.model_dump(),
        headers={"Content-Type": "application/problem+json"},
        status_code=exc.status,
    )


async def global_exception_handler(request: Request, exc: Exception):
    logger.error(f"Exception occurred for \n request: {request}\n exception: {exc}")
    return JSONResponse(
        status_code=500, content={"detail": "An unexpected error occurred."}
    )


def get_app():
    """
    App factory class
    """
    from llm_api.lifecycle import lifespan

    setup_logging(
        log_level=os.environ.get("LLM__LOGGING_LEVEL", "info"),
        log_format=os.environ.get(
            "LLM__LOGGING__FORMAT", "%(asctime)s.%(msecs)d %(levelname)s %(message)s"
        ),
    )
    app = FastAPI(lifespan=lifespan)

    # set "export CORS_ORIGINS=*" for local testing
    CORS_ORIGINS = os.environ.get("CORS_ORIGINS", "")
    app.add_middleware(
        CORSMiddleware,
        allow_origins=CORS_ORIGINS.split(","),
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    app.add_exception_handler(Exception, global_exception_handler)
    app.add_exception_handler(RequestValidationError, http422_error_handler)
    app.add_exception_handler(HTTPProblem, http_problem_error_handler)

    return app
