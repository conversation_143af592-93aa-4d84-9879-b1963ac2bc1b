from fastapi import FastAPI, APIRouter
from fastapi.responses import JSONResponse
import os

router = APIRouter()

@router.get("/version")
async def get_version():
  version_file_path = os.path.join(os.path.dirname(__file__), '../../VERSION')
  with open(version_file_path, 'r') as file:
    version = file.read().strip()
  return JSONResponse(content={
    "name" : "ai-engine",
    "version": version
  })
