from requests import Session
from requests.adapters import H<PERSON><PERSON><PERSON>pter as _HTTPAdapter
from requests.cookies import extract_cookies_to_jar
from requests.exceptions import HTTPError
from requests.models import Response as _Response
from requests.structures import CaseInsensitiveDict
from requests.utils import get_encoding_from_headers
from urllib3.util.retry import Retry

from llm_api.exceptions import HTTPProblem
from llm_api.models import ProblemDetails


class Response(_Response):
    def raise_for_status(self):
        try:
            super().raise_for_status()
        except HTTPError as e:
            if self.headers.get("Content-Type") == "application/problem+json":
                raise HTTPProblem(
                    ProblemDetails(**self.json()),
                    self.status_code,
                )


class HTTPAdapter(_HTTPAdapter):
    def build_response(self, req, resp):
        response = Response()

        # Fallback to None if there's no status_code, for whatever reason.
        response.status_code = getattr(resp, "status", None)  # type: ignore

        # Make headers case-insensitive.
        response.headers = CaseInsensitiveDict(getattr(resp, "headers", {}))

        # Set encoding.
        response.encoding = get_encoding_from_headers(response.headers)
        response.raw = resp
        response.reason = response.raw.reason

        if isinstance(req.url, bytes):
            response.url = req.url.decode("utf-8")
        else:
            response.url = req.url

        # Add new cookies from the server.
        extract_cookies_to_jar(response.cookies, req, resp)

        # Give the Response some context.
        response.request = req
        response.connection = self

        return response


def get_session(with_retries=False) -> Session:
    adapter_kwargs = {}
    if with_retries:
        adapter_kwargs["max_retries"] = Retry(
            status=5,
            connect=5,
            backoff_factor=1,
            status_forcelist=[500, 502, 503, 504],
            allowed_methods=["GET", "POST", "PATCH"],
        )

    session = Session()
    session.mount("http://", HTTPAdapter(**adapter_kwargs))

    return session
