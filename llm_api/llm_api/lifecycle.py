import os
from contextlib import asynccontextmanager

import fastapi

from llm_api.blai_api.router import router
from llm_api.blai_api.version import router as version_router
from llm_api.eval.router import router as eval_router
from llm_api.integrations.router import router as integrations_router
from llm_api.log_utils import get_logger

env = os.environ["BLAI_ENV"]


@asynccontextmanager
async def lifespan(app: fastapi.FastAPI):
    """
    Lifecycle method that allows settings things up at startup
    and tearing things down at shutdown
    """

    await app_startup(app)

    yield

    await app_shutdown(app)


async def app_startup(app: fastapi.FastAPI):
    """
    Do startup things.
    """
    get_logger().info("APP STARTUP")
    app.include_router(router)
    app.include_router(integrations_router)
    app.include_router(version_router)
    if env == "dev":
        app.include_router(eval_router)


async def app_shutdown(_: fastapi.FastAPI):
    """
    Do shutdown things.
    """
    get_logger().info("APP SHUTDOWN")
    pass
