"""
Main entry point in the project.

Defines the main cli command.
Add subcommands at the bottom.
"""

import json
import logging
import os
from pathlib import Path

# import click
from dotenv import load_dotenv

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

load_dotenv()

secrets_path = "/mnt/secrets-store/secrets"
if Path(secrets_path).exists():
    with Path(secrets_path).open("r") as f:
        env_vars_json = json.load(f)
        for k, v in env_vars_json.items():
            logger.debug(f"Setting {k} from secrets store")
            os.environ[k] = v

