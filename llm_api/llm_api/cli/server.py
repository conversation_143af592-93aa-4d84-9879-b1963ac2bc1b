import os

import click
import uvicorn
from langchain_community.callbacks.openai_info import MODEL_COST_PER_1K_TOKENS

from llm_api.log_utils import setup_logging

MODEL_COST_PER_1K_TOKENS["gpt-4-1106-preview"] = 0.01
MODEL_COST_PER_1K_TOKENS["gpt-4-1106-preview-completion"] = 0.03


@click.group("llm")
@click.option(
    "--log-level",
    type=click.Choice(["info", "debug"], case_sensitive=False),
    envvar="LLM__LOGGING__LEVEL",
    default="info",
    help="Sets the logging level of the app.",
)
@click.option(
    "--log-format",
    type=str,
    envvar="LLM__LOGGING__FORMAT",
    default="%(asctime)s.%(msecs)d %(levelname)s %(message)s",
    help="Sets the logging level of the app.",
)
def cli(log_level: str, log_format: str):
    """
    Main entry point into the llm app.

    Use this for all your LLM needs.
    """
    setup_logging(log_level=log_level, log_format=log_format)


@cli.group("server")
def server():
    """
    Commands related to the llm server used in BricklayerAI.
    """
    pass


@server.command("run")
@click.option(
    "-p",
    "--port",
    help="The port where the server will listen",
    type=int,
    envvar="PORT",
    default=9000,
)
@click.option(
    "--workers",
    help="How many workers should the server spawn",
    type=int,
    envvar="WORKERS",
    default=1,
)
def run_server(port: int, workers: int):
    """
    Starts the llm server.
    """

    uvicorn.run(
        "llm_api.blai_api.main:get_app",
        factory=True,
        workers=workers,
        host="0.0.0.0",
        port=port,
        log_level="info",
        reload=os.getenv("RELOAD_SERVER", "False").lower() == "true",
    )
