import json
import logging.config
import os
from pathlib import Path

import click
import uvicorn
from dotenv import load_dotenv

load_dotenv()

secrets_path = "/mnt/secrets-store/secrets"
if Path(secrets_path).exists():
    with Path(secrets_path).open("r") as f:
        env_vars_json = json.load(f)
        for k, v in env_vars_json.items():
            os.environ[k] = v

from llm_api.log_utils import logging_config


@click.group("procedure_engine")
def procedure_engine():
    """
    Commands related to procedures in BricklayerAI.
    """
    logging.config.dictConfig(config=logging_config)


@procedure_engine.command("api")
@click.option(
    "-p",
    "--port",
    help="The port where the procedure server will listen",
    type=int,
    envvar="PROCEDURES__API__PORT",
    default=9001,
)
@click.option(
    "--workers",
    help="How many workers should the server spawn",
    type=int,
    envvar="PROCEDURES__API__WORKERS",
    default=1,
)
def api(port: int, workers: int):
    """
    Starts the llm server.
    """

    uvicorn.run(
        "llm_api.procedures.api.api:get_app",
        factory=True,
        workers=workers,
        host="0.0.0.0",
        port=port,
        log_level="info",
    )


@procedure_engine.command("workers")
@click.option(
    "-c",
    "--concurrency",
    help="How many workers for procedures?",
    type=int,
    envvar="PROCEDURES__WORKERS__WORKERS",
    default=4,
)
def workers(concurrency: int):
    from llm_api.procedures.celery import celery_app

    argv = [
        "worker",
        "--loglevel=INFO",
        "--pool=threads",
        f"--concurrency={concurrency}",
    ]
    celery_app.worker_main(argv=argv)
