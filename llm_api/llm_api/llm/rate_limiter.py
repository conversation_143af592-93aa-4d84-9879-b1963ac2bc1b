import asyncio
import logging
import threading
import time

import redis
from langchain_core.rate_limiters import BaseRateLimiter

LOG = logging.getLogger(__name__)
LOG.setLevel(logging.DEBUG)


class RedisRateLimiter(BaseRateLimiter):
    def __init__(
        self,
        *,
        requests_per_second: float = 1,
        check_every_n_seconds: float = 0.1,
        max_bucket_size: float = 1,
        redis_client: redis.Redis,
        redis_key: str = "rate_limiter_bucket",
    ) -> None:
        self.requests_per_second = requests_per_second
        self.max_bucket_size = max_bucket_size
        self.check_every_n_seconds = check_every_n_seconds
        self.redis = redis_client
        self.redis_key = redis_key

        if not self.redis.exists(self.redis_key):
            self.redis.hset(self.redis_key, "bucket", 0.0)
            self.redis.hset(self.redis_key, "last", time.monotonic())

        self._consume_lock = threading.Lock()
        LOG.debug(
            f"RedisRateLimiter: Initialized with RPS={self.requests_per_second}, "
            f"check_every={self.check_every_n_seconds}, bucket_size={self.max_bucket_size}, "
            f"redis_key={self.redis_key}"
        )

    def _consume(self) -> bool:
        LOG.debug("RedisRateLimiter: Attempting to consume token")
        with self._consume_lock:
            pipeline = self.redis.pipeline()
            while True:
                pipeline.watch(self.redis_key)
                data = self.redis.hgetall(self.redis_key)
                current_bucket = max(0, float(data.get(b"bucket", 0.0)))
                last_ts = float(data.get(b"last", time.monotonic()))

                now = time.monotonic()
                elapsed = now - last_ts
                if elapsed < 0:
                    elapsed = 0

                LOG.debug(
                    f"RedisRateLimiter: Current bucket={current_bucket}, last={last_ts}, now={now}, elapsed={elapsed}"
                )

                new_tokens = current_bucket + elapsed * self.requests_per_second
                new_tokens = min(new_tokens, self.max_bucket_size)
                LOG.debug(f"RedisRateLimiter: Calculated new_tokens={new_tokens}")

                if new_tokens >= 1:
                    new_tokens -= 1
                    pipeline.multi()
                    pipeline.hset(self.redis_key, "bucket", new_tokens)
                    pipeline.hset(self.redis_key, "last", now)
                    try:
                        pipeline.execute()
                        LOG.debug("RedisRateLimiter: Token consumed")
                        return True
                    except redis.WatchError:
                        LOG.debug("RedisRateLimiter: WatchError: retrying consume")
                        continue
                else:
                    pipeline.multi()
                    pipeline.hset(self.redis_key, "bucket", new_tokens)
                    pipeline.hset(self.redis_key, "last", now)
                    try:
                        pipeline.execute()
                        LOG.debug(
                            "RedisRateLimiter: Not enough tokens available, returning False"
                        )
                    except redis.WatchError:
                        LOG.debug("RedisRateLimiter: WatchError: retrying consume")
                        continue
                    return False

    def acquire(self, *, blocking: bool = True) -> bool:
        LOG.debug(f"RedisRateLimiter: Acquiring token blocking={blocking}")
        if not blocking:
            result = self._consume()
            LOG.debug(f"RedisRateLimiter: Non-blocking acquire result={result}")
            return result

        while not self._consume():
            LOG.debug("RedisRateLimiter: Blocking until token available")
            time.sleep(self.check_every_n_seconds)
        LOG.debug("RedisRateLimiter: Blocking acquire successful")
        return True

    async def aacquire(self, *, blocking: bool = True) -> bool:
        LOG.debug(f"RedisRateLimiter: Async acquiring token blocking={blocking}")
        if not blocking:
            result = self._consume()
            LOG.debug(f"RedisRateLimiter: Async non-blocking acquire result={result}")
            return result

        while not self._consume():
            LOG.debug("RedisRateLimiter: Async blocking until token available")
            await asyncio.sleep(self.check_every_n_seconds)
        LOG.debug("RedisRateLimiter: Async blocking acquire successful")
        return True
