import logging
import os
from typing import Union

import redis
from langchain_core.callbacks import BaseCallbackHandler
from langchain_core.language_models import BaseChatModel, BaseLLM
from langchain_openai.chat_models import AzureChatOpenAI
from langchain_openai.llms import AzureOpenAI

from llm_api.blai_llm.constants import REDIS_PORT, REDIS_URL
from llm_api.llm.proxyllm import ProxyLLM
from llm_api.llm.rate_limiter import RedisRateLimiter
from llm_api.specs.llm_spec import AzureOpenAISpec, LLMSpec, LLMType

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

_redis_client = redis.Redis(
    host=REDIS_URL,
    port=REDIS_PORT,
)

_rate_limiter_cache = {}

default_3_5_gpt_spec_data = AzureOpenAISpec(
    temperature=0,
    deployment_name=os.environ.get("AZURE_OPENAI_DEPLOYMENT"),
    openai_api_version=os.environ.get(
        "LLM__AZUREOPENAI__GPT3_5__APIVERSION", "2024-02-01"
    ),
    openai_api_type=os.environ.get("LLM__AZUREOPENAI__GPT3_5__APITYPE", "azure"),
    openai_api_base=os.environ.get("AZURE_OPENAI_API_BASE"),
    openai_api_key=os.environ.get("AZURE_OPENAI_API_KEY"),
    model_name="gpt-4o-mini",
    model_version="2024-07-18",
)

default_4_gpt_spec_data = AzureOpenAISpec(
    temperature=0,
    deployment_name=os.environ.get("AZURE_OPENAI_DEPLOYMENT_GPT4"),
    openai_api_version=os.environ.get(
        "LLM__AZUREOPENAI__GPT4__APIVERSION", "2024-02-01"
    ),
    openai_api_type=os.environ.get("LLM__AZUREOPENAI__GPT4__APITYPE", "azure"),
    openai_api_base=os.environ.get("AZURE_OPENAI_API_BASE_GPT_4"),
    openai_api_key=os.environ.get("AZURE_OPENAI_API_KEY_GPT_4"),
    model_name="gpt-4o-mini",
    model_version="2024-07-18",
)

default_4_gpt_spec_data_json_enabled = AzureOpenAISpec(
    temperature=0,
    deployment_name=os.environ.get("AZURE_OPENAI_DEPLOYMENT_GPT4"),
    openai_api_version=os.environ.get(
        "LLM__AZUREOPENAI__GPT4__APIVERSION", "2024-02-01"
    ),
    openai_api_type=os.environ.get("LLM__AZUREOPENAI__GPT4__APITYPE", "azure"),
    openai_api_base=os.environ.get("AZURE_OPENAI_API_BASE_GPT_4"),
    openai_api_key=os.environ.get("AZURE_OPENAI_API_KEY_GPT_4"),
    model_name="gpt-4o-mini",
    model_version="2024-07-18",
    json_mode_enabled=True,
)


def get_model_from_spec(
    spec: LLMSpec,
    streaming_cb: BaseCallbackHandler = None,
) -> Union[BaseLLM, BaseChatModel]:
    match spec.type:
        case LLMType.AzureOpenAI:
            model_cls = AzureOpenAI
        case LLMType.AzureChatOpenAI:
            model_cls = AzureChatOpenAI
        case _:
            raise ValueError(f"Unknown llm type: {spec.type}")

    try:
        llm_params = {
            "azure_deployment": spec.data.deployment_name,
            "api_key": spec.data.openai_api_key.get_secret_value(),
            "azure_endpoint": spec.data.openai_api_base,
            "openai_api_type": spec.data.openai_api_type,
            "api_version": spec.data.openai_api_version,
            "temperature": spec.data.temperature,
            "streaming": spec.streaming,
            "model_name": spec.data.model_name,
            "max_retries": 5,  # Set the maximum number of retries
        }
        if spec.data.json_mode_enabled:
            llm_params["model_kwargs"] = {"response_format": {"type": "json_object"}}

        match spec.type:
            case LLMType.AzureOpenAI:
                llm_params["model_name"] = (
                    f"{llm_params['model_name']}-{spec.data.model_version}"
                )
            case LLMType.AzureChatOpenAI:
                llm_params["model_version"] = spec.data.model_version

        if streaming_cb:
            llm_params["callbacks"] = [streaming_cb]

        if os.environ.get("USE_REDIS_RATE_LIMITER", "False") == "True":
            redis_key = f"rate_limiter:{llm_params['model_name']}"
            if redis_key not in _rate_limiter_cache:
                limiter = RedisRateLimiter(
                    requests_per_second=float(os.environ.get("RATE_LIMIT_RPS", "1")),
                    check_every_n_seconds=float(
                        os.environ.get("RATE_LIMIT_CHECK_FREQ", "0.1")
                    ),
                    max_bucket_size=float(
                        os.environ.get("RATE_LIMIT_BUCKET_SIZE", "1")
                    ),
                    redis_client=_redis_client,
                    redis_key=redis_key,
                )
                _rate_limiter_cache[redis_key] = limiter
            else:
                limiter = _rate_limiter_cache[redis_key]

            llm_params["rate_limiter"] = limiter

        llm = model_cls(**llm_params)
        if os.environ.get("USE_PROXY_LLM", "False").lower() == "true":
            llm = ProxyLLM(llm)
        return llm
    except Exception as err:
        logger.error(f"Couldn't create model with spec: {spec.json()}")
        logger.error(err)
        raise err
