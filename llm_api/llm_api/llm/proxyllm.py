import logging
from functools import wraps
from typing import Callable, List, Optional

import sentry_sdk
from langchain.schema import (
    AIMessage,
    BaseMessage,
    ChatGeneration,
    ChatResult,
    HumanMessage,
)
from langchain_openai.chat_models.base import BaseChatOpenAI

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


def log_method_calls(cls):
    """Class decorator to log all method calls."""
    original_methods = {}
    for attr_name in dir(cls):
        attr = getattr(cls, attr_name)
        if callable(attr) and not attr_name.startswith("__"):
            original_methods[attr_name] = attr

    def wrapper_decorator(method_name):
        @wraps(original_methods[method_name])
        def wrapper(self, *args, **kwargs):
            logger.info(f"Method called: {method_name}")
            logger.info(f"Args: {args}")
            logger.info(f"Kwargs: {kwargs}")
            return original_methods[method_name](self, *args, **kwargs)

        return wrapper

    for method_name, method in original_methods.items():
        setattr(cls, method_name, wrapper_decorator(method_name))

    return cls


def _compose_message_log(messages: List[BaseMessage]) -> str:
    log_lines = []
    for message in messages:
        if isinstance(message, AIMessage):
            role = "AI"
        elif isinstance(message, HumanMessage):
            role = "Human"
        else:
            # Fallback to message.type if it's something else like system
            role = message.type.capitalize()
        log_lines.append(f"{role}: {message.content}")
    return "\n".join(log_lines)


@log_method_calls
class ProxyLLM(BaseChatOpenAI):
    chat_model: BaseChatOpenAI = None
    filters: List[Callable[[List[BaseMessage]], Optional[AIMessage]]] = []

    def __init__(self, chat_model: BaseChatOpenAI, backup_llm=None, **kwargs):
        super().__init__(**kwargs)
        self.chat_model = chat_model
        self.filters = []

    @property
    def _llm_type(self) -> str:
        return "proxy_chat_model"

    def add_filter(
        self, filter_function: Callable[[List[BaseMessage]], Optional[AIMessage]]
    ):
        """Register a filter function that takes a list of messages and returns either None or a fixed AIMessage response."""
        self.filters.append(filter_function)

    def maybe_report_content_filtering_error(self, e: Exception, llm_input: str):
        if "a content filter being triggered" in str(e):
            sentry_sdk.capture_exception(e)
            sentry_sdk.set_context("custom_context", {"llm_input": llm_input})

    def invoke(
        self, messages: List[BaseMessage], stop: Optional[List[str]] = None
    ) -> ChatResult:
        log_text = _compose_message_log(messages)
        logger.info(f"LLM Input:\n{log_text}")

        for filter_func in self.filters:
            response = filter_func(messages)
            if response is not None:
                logger.info(f"LLM Output (Filtered): {response.content}")
                return AIMessage(content=response.content)

        try:
            chat_result = self.chat_model.invoke(messages, stop=stop)
        except Exception as e:
            self.maybe_report_content_filtering_error(e, log_text)
            raise e

        logger.info(f"LLM Output: {chat_result.content}")
        return chat_result

    async def ainvoke(
        self, messages: List[BaseMessage], stop: Optional[List[str]] = None
    ) -> ChatResult:
        log_text = _compose_message_log(messages)
        logger.info(f"LLM Input (async):\n{log_text}")

        for filter_func in self.filters:
            response = filter_func(messages)
            if response is not None:
                logger.info(f"LLM Output (Filtered, async): {response.content}")
                return AIMessage(content=response.content)

        try:
            chat_result = await self.chat_model.ainvoke(messages, stop=stop)
        except Exception as e:
            self.maybe_report_content_filtering_error(e, log_text)
            raise e

        logger.info(f"LLM Output: {chat_result.content}")
        return chat_result

    async def _agenerate(
        self, messages: List[BaseMessage], stop: Optional[List[str]] = None
    ) -> ChatResult:
        log_text = _compose_message_log(messages)
        logger.info(f"LLM Input (async generate):\n{log_text}")

        for filter_func in self.filters:
            response = filter_func(messages)
            if response is not None:
                logger.info(
                    f"LLM Output (Filtered, async generate): {response.content}"
                )
                return ChatResult(generations=[ChatGeneration(message=response)])

        try:
            chat_result = await self.chat_model._agenerate(messages, stop=stop)
        except Exception as e:
            self.maybe_report_content_filtering_error(e, log_text)
            raise e

        for generation in chat_result.generations:
            logger.info(f"LLM Output (async generate): {generation.message.content}")
        return chat_result

    def stream(self, messages: List[BaseMessage], stop: Optional[List[str]] = None):
        log_text = _compose_message_log(messages)
        logger.info(f"Streaming LLM Input:\n{log_text}")

        for filter_func in self.filters:
            response = filter_func(messages)
            if response is not None:
                logger.info(f"Output (Filtered): {response.content}")
                yield response
                return

        for response in self.chat_model.stream(messages, stop=stop):
            logger.info(f"Output: {response.content}")
            yield response

    def bind(self, functions: List[Callable]):
        """Bind additional functions to the chat model."""
        return self.chat_model.bind(functions=functions)
