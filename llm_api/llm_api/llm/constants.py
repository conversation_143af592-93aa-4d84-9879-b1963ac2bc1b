from enum import StrEnum


class AzureOpenAIErrors(StrEnum):
    """Enum class for holding OpenAI errors we might want to handle differently.

    This might be flaky if the API error messages change often,
    but LangChain doesn't doesn't have a better approach either.
    """

    AZURE_CONTENT_FILTER_TRIGGERED = (
        "Azure has not provided the response due to a content filter being triggered"
    )
