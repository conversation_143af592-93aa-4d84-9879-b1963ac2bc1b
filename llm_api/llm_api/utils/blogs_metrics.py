import logging
import os
from enum import Enum

import requests

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

raw_backend_url = os.getenv("PUBLIC_BACKEND_URL", "http://host.docker.internal:8000")
PUBLIC_BACKEND_URL = raw_backend_url.replace("/api/v1", "")
EVENT_ENDPOINT = f"{PUBLIC_BACKEND_URL}/internal/api/blogs-metrics/addEvent"


class BlogsMetricsEvent(str, Enum):
    BlogsQuery = "BlogsQuery"
    BlogsQueryError = "BlogsQueryError"
    DailyBlogsCheckPass = "DailyBlogsCheckPass"
    DailyBlogsCheckError = "DailyBlogsCheckError"


def report_event(
    event: BlogsMetricsEvent, data: dict = None, organization_id: str = "public"
):
    payload = {
        "event": event.value,
        "data": data or {},
        "organizationId": organization_id,
    }

    try:
        response = requests.post(EVENT_ENDPOINT, json=payload, timeout=5)
        response.raise_for_status()
    except requests.RequestException as e:
        import traceback

        logger.error(f"Failed to report event '{event}': {e}\n{traceback.format_exc()}")
