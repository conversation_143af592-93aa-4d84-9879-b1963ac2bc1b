import requests
from bs4 import BeautifulSoup
from playwright.async_api import (
    async_playwright,
    TimeoutError as PlaywrightTimeoutError,
)
import asyncio


async def get_page_title(url: str) -> str:
    # Try fast method first: requests + BeautifulSoup
    try:
        headers = {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64)"}
        response = requests.get(url, headers=headers, timeout=5)  # Reduced timeout
        response.raise_for_status()
        soup = BeautifulSoup(response.text, "html.parser")
        title_tag = soup.find("title")
        if title_tag and title_tag.text.strip():
            return title_tag.text.strip()
    except Exception as e:
        print(f"[requests fallback failed]: {e}")

    # Fallback: Playwright for JS-rendered sites
    try:
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            page = await browser.new_page()
            await page.goto(url, timeout=10000, wait_until="networkidle")
            await page.wait_for_selector("title", timeout=3000)
            title = await page.title()
            await browser.close()
            return title
    except PlaywrightTimeoutError:
        print("[playwright timeout]")
    except Exception as e:
        print(f"[playwright error]: {e}")