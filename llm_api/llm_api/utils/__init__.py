import json
import logging
import os
import time
import uuid
import xml.etree.ElementTree as ET
from typing import Literal, Optional, Tuple
from urllib.parse import urlparse

import smart_open

from llm_api.blai_api.dtos import Evidence, EvidenceOrigin

EvidenceExtension = Literal["json", "xml"]

logger = logging.getLogger(__name__)


def _format_evidence(text: str) -> Tuple[str, EvidenceExtension]:
    try:
        ET.fromstring(text)
        return text, "xml"
    except ET.ParseError:
        pass

    try:
        json.loads(text)
        return text, "json"
    except json.JSONDecodeError:
        return json.dumps(text), "json"


def store_evidence(
    evidence_text: str,
    evidence_name: str,
    evidence_type: EvidenceOrigin = EvidenceOrigin.Tool,
    extension: Optional[str] = None,
    evidence_location: str = "",
    page_title: Optional[str] = None,

) -> Evidence | None:
    """
    Store evidence in AWS S3.

    The function will leave the contents of the evidence untouched if a file extension
    is provided. Otherwise, it will try to guess if the contents of the evidence
    are either XML or JSON, then forcefully convert them to JSON if none apply.
    """

    if evidence_location:
        # just for local development
        if not evidence_location.startswith("s3://"):
            os.makedirs(evidence_location, exist_ok=True)

        if not extension:
            evidence_text, extension = _format_evidence(evidence_text)

        evidence_id = uuid.uuid4()
        evidence_location = f"{evidence_location}/{evidence_id}.{extension}"
        logger.info(f"Storing evidence: {evidence_id} -> {evidence_location}")
        with smart_open.smart_open(evidence_location, "w") as f:
            f.write(evidence_text)

        s3_url = urlparse(evidence_location)
        # dev/org_id/procedure_id/.. -> org_id/procedure_id/...
        s3_path = s3_url.path[1:].split("/", 1)[1]

        evidence_kwargs = {
            "name": evidence_name,
            "evidenceLocation": s3_path,
            "origin": evidence_type,
        }

        if page_title:
            evidence_kwargs["page_title"] = page_title

        return Evidence(**evidence_kwargs)

    return None


def monitor_long_request(max_time_seconds=None):
    def decorator(func):
        def wrapper(*args, **kwargs):
            start_time = time.time()
            result = func(*args, **kwargs)
            elapsed_time = time.time() - start_time
            if elapsed_time > max_time_seconds:
                logger.error(
                    f"Method '{func.__name__}' took {elapsed_time:.2f} seconds to complete"
                )
            return result

        return wrapper

    return decorator


def get_feature_flag_value(flag_name: str) -> bool:
    """
    Returns True or False for the value of a feature flag
    stored in an environment variable. Allowed feature flag values
    are 0, 1, "0" and "1".
    """
    return bool(int(os.environ.get(f"FF_{flag_name}", "0")))
