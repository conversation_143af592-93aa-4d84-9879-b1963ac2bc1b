import datetime as dt
import json
import logging
import sys

import structlog

# Trigger logging system initialization early in the app lifecycle.
# This ensures logging works across all modules, even if setup_logging() is not called immediately.
# Important: if you're using logging.basicConfig() without force=True, this call prevents it from being a no-op.
logging.warning("Logging is configured")


LOG_RECORD_BUILTIN_ATTRS = [
    "name",
    "msg",
    "args",
    "levelname",
    "levelno",
    "pathname",
    "filename",
    "module",
    "exc_info",
    "exc_text",
    "stack_info",
    "lineno",
    "funcName",
    "created",
    "msecs",
    "relativeCreated",
    "thread",
    "threadName",
    "processName",
    "process",
]


def setup_logging(log_level: str, log_format: str):
    match log_level.lower():
        case "info":
            log_level = logging.INFO
        case "debug":
            log_level = logging.DEBUG

    logging.basicConfig(level=log_level, format=log_format, datefmt="%Y-%m-%d %H:%M:%S")
    logging.getLogger("openai").setLevel(level=logging.WARNING)
    logging.getLogger("httpx").setLevel(level=logging.WARNING)
    logging.getLogger("httpcore").setLevel(level=logging.WARNING)
    logging.getLogger("boto3").setLevel(level=logging.WARNING)
    logging.getLogger("urllib3").setLevel(level=logging.WARNING)
    logging.getLogger("botocore").setLevel(level=logging.WARNING)
    logging.getLogger("s3transfer").setLevel(level=logging.WARNING)

    configure_params = dict()

    configure_params["wrapper_class"] = structlog.make_filtering_bound_logger(log_level)

    shared_processors = [
        structlog.processors.add_log_level,
        structlog.processors.TimeStamper(fmt="iso"),
    ]
    if sys.stderr.isatty():
        processors = shared_processors + [structlog.dev.ConsoleRenderer()]
    else:
        processors = shared_processors + [
            structlog.processors.dict_tracebacks,
            structlog.processors.JSONRenderer(),
        ]

    # if log_type == "json":
    configure_params["processors"] = processors

    structlog.configure(**configure_params)


def get_logger():
    logger = structlog.get_logger()

    return logger


def fail(*args, **kwargs):
    logger = get_logger()

    logger.error(*args, **kwargs)
    sys.exit(1)


class JSONFormatter(logging.Formatter):
    def __init__(
        self,
        *,
        fmt_keys: dict[str, str] | None = None,
    ):
        super().__init__()
        self.fmt_keys = fmt_keys if fmt_keys is not None else {}

    def format(self, record: logging.LogRecord) -> str:
        message = self._prepare_log_dict(record)
        return json.dumps(message, default=str)

    def _prepare_log_dict(self, record: logging.LogRecord) -> dict:
        always_fields = {
            "message": record.getMessage(),
            "timestamp": dt.datetime.fromtimestamp(
                record.created, tz=dt.timezone.utc
            ).isoformat(),
        }
        if record.exc_info is not None:
            always_fields["exc_info"] = self.formatException(record.exc_info)
        if record.stack_info is not None:
            always_fields["stack_info"] = self.formatStack(record.stack_info)

        message = {
            key: (
                msg_val
                if (msg_val := always_fields.pop(val, None)) is not None
                else getattr(record, val)
            )
            for key, val in self.fmt_keys.items()
        }
        message.update(always_fields)

        for key, val in record.__dict__.items():
            if key not in LOG_RECORD_BUILTIN_ATTRS:
                message[key] = val

        return message


logging_config = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "simple": {
            "format": "%(levelname)s [%(name)s]: %(message)s",
        },
        "json": {
            "()": "llm_api.log_utils.JSONFormatter",
            "fmt_keys": {
                "level": "levelname",
                "message": "message",
                "timestamp": "timestamp",
                "logger": "name",
                "module": "module",
                "function": "funcName",
                "line": "lineno",
                "thread_name": "threadName",
            },
        },
    },
    "handlers": {
        "stdout": {
            "class": "logging.StreamHandler",
            "formatter": "json",
            "stream": "ext://sys.stdout",
        },
    },
    "loggers": {
        "root": {
            "level": "INFO",
            "handlers": ["stdout"],
        },
    },
}
