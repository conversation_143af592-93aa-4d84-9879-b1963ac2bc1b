import logging
import tempfile
from pathlib import Path
from typing import IO, Protocol, Union

from bs4 import BeautifulSoup
from playwright.sync_api import sync_playwright
from pydantic import BaseModel, HttpUrl
from readability import Document

logger = logging.getLogger(__name__)


class ArticleFetcher(Protocol):
    def get(self) -> tuple[str, str]: ...


class URLArticleFetcher(BaseModel):
    url: HttpUrl

    def get(self) -> tuple[str, str]:
        with sync_playwright() as p:
            browser = p.chromium.launch()
            page = browser.new_page()
            page.goto(str(self.url), wait_until="domcontentloaded")
            html = page.content()
            browser.close()

        doc = Document(html)
        title = doc.title()
        content = doc.summary()

        soup = BeautifulSoup(content, "html.parser")
        for tag in soup(["img", "a"]):
            tag.decompose()
        return title, str(soup)


class ArticleToPDF(BaseModel):
    def generate_html(self, title: str, content: str) -> str:
        style = """
        <style>
        body {
            font-family: Georgia, serif;
            max-width: 700px;
            margin: 40px auto;
            line-height: 1.6;
            font-size: 12px;
            color: #333;
        }
        h1, h2, h3 {
            font-family: 'Helvetica Neue', sans-serif;
        }
        </style>
        """
        return f"""
        <html>
        <head>
            <meta charset="utf-8">
            <title>{title}</title>
            {style}
        </head>
        <body>
            <h1>{title}</h1>
            {content}
        </body>
        </html>
        """

    def __call__(self, fetcher: ArticleFetcher, output: Union[str, Path, IO[bytes]]):
        logger.info(f"Article to PDF conversion for fetcher {fetcher} starting.")
        title, content = fetcher.get()
        html = self.generate_html(title, content)

        with sync_playwright() as p:
            browser = p.chromium.launch()
            page = browser.new_page()

            with tempfile.NamedTemporaryFile(
                suffix=".html", delete=False, mode="w", encoding="utf-8"
            ) as f:
                f.write(html)
                temp_path = f.name

            page.goto(f"file://{temp_path}", wait_until="load")

            # If output is file-like object, get PDF bytes and write
            if hasattr(output, "write") and callable(output.write):
                pdf_bytes = page.pdf(format="A4", print_background=True)
                output.write(pdf_bytes)
                output.flush()
            else:
                # Otherwise assume it's a path (str or Path)
                page.pdf(path=str(output), format="A4", print_background=True)

            browser.close()

        logger.info(f"✅ Article to PDF saved to {output}")
