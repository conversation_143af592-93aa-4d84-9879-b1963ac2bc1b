import json
import logging
import os
from typing import Any, Dict, List, Optional

from langchain_core.documents import Document
from langchain_postgres import PGVector
from langchain_postgres.vectorstores import PGVector
from sqlalchemy import create_engine, text
from sqlalchemy.ext.asyncio import create_async_engine

from llm_api.blai_llm.openai import azure_openai_embeddings

POSTGRES_USER = os.getenv("POSTGRES_USER", "postgres")
POSTGRES_PASS = os.getenv("POSTGRES_PASS", "example")
POSTGRES_HOST = os.getenv("POSTGRES_HOST", "host.docker.internal")
POSTGRES_PORT = os.getenv("POSTGRES_PORT", "5432")
POSTGRES_DB = os.getenv("POSTGRES_DB", "postgres")

CONNECTION_URL = f"postgresql+psycopg://{POSTGRES_USER}:{POSTGRES_PASS}@{POSTGRES_HOST}:{POSTGRES_PORT}/{POSTGRES_DB}"

DEFAULT_K = 10

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


class BlaiPGVector(PGVector):
    async def asimilarity_search(
        self,
        query: str,
        k: int = DEFAULT_K,
        filter: Dict[str, str] | None = None,
        **kwargs: Any,
    ) -> List[Document]:
        """
        Override method to insert the scores in the metadata
        """
        docs_and_scores = await self.asimilarity_search_with_score(
            query, k, filter=filter
        )
        for doc, score in docs_and_scores:
            doc.metadata["score"] = score
            doc.metadata["retriever_source"] = "vectorstore"
        return [doc for doc, _ in docs_and_scores]


def get_pgvector_for(collection_name: str, with_async_engine=False):
    connection = CONNECTION_URL
    if with_async_engine:
        connection = create_async_engine(connection)

    print("Creating PGVector object")
    return BlaiPGVector(
        embeddings=azure_openai_embeddings,
        collection_name=collection_name,
        connection=connection,
        use_jsonb=True,
    )


class BlaiPGVectorFetcher:
    def __init__(self, collection_name: str):
        self.collection_name = collection_name
        self.connection_url = CONNECTION_URL
        self.engine = create_engine(self.connection_url)

    def _get_collection_id(self) -> str:
        """
        Retrieves the collection ID (UUID) for the given collection name.
        """
        query = text(
            """
            SELECT uuid 
            FROM langchain_pg_collection 
            WHERE name = :name
        """
        )
        with self.engine.connect() as connection:
            result = connection.execute(
                query, {"name": self.collection_name}
            ).fetchone()
            if result:
                return str(result[0])
            else:
                raise ValueError(f"Collection '{self.collection_name}' not found.")

    def get_document_with_full_metadata(self, doc: Document) -> Document:
        """
        Retrieves the document with full metadata based on `source` and `page_content`.
        """
        collection_id = self._get_collection_id()
        if not doc.metadata.get("filename"):
            source_key = "filename"
            source = doc.metadata.get("source")
        else:
            source = doc.metadata.get("source")
            source_key = "source"

        if not source:
            raise ValueError("Document metadata must include 'source'.")

        logger.debug(
            f"Querying with collection_id: {collection_id}, source: {doc.metadata.get('source')}"
        )

        query = text(
            f"""
            SELECT document, cmetadata 
            FROM langchain_pg_embedding 
            WHERE collection_id = :collection_id 
              AND cmetadata->>'{source_key}' = :source
        """
        )

        logger.debug(
            f"Querying with collection_id: {collection_id}, filename: {source}"
        )

        with self.engine.connect() as connection:
            result = connection.execute(
                query,
                {
                    "collection_id": collection_id,
                    "source": source,
                },
            ).fetchall()

            if not result:
                logger.debug(
                    "No results found for filename. Check the database data and query."
                )
                return None

            for row in result:
                db_document, db_metadata = row
                logger.debug(
                    f"Comparing DB document: {db_document} with input document: {doc.page_content}"
                )

                if db_document.strip() == doc.page_content.strip():
                    logger.debug(
                        f"Found matching document with metadata: {db_metadata}"
                    )
                    return Document(page_content=db_document, metadata=db_metadata)

        logger.debug("No matching document found after comparison.")
        return None
