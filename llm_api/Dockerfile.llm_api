FROM bricklayerai/python:1.0
# Step 1: OS-level dependencies
RUN apt-get update && \
    apt-get install -y libmagic1 jq curl && \
    apt-get clean
# Step 2: Remove any leftover Docker binaries (for CVE-2024-7042 safety)
RUN rm -f /usr/bin/containerd-shim* /usr/bin/docker-proxy /usr/local/bin/docker* || true

RUN apt update && apt upgrade -y linux-libc-dev && apt clean

RUN apt-get update && apt-get upgrade -y

WORKDIR /code

RUN apt-get update && apt-get install -y libmagic1 jq curl

COPY ./requirements/common.txt .
RUN python -m pip install --no-cache-dir -r common.txt

COPY ./requirements/llm-api.txt .
RUN python -m pip install --no-cache-dir -r common.txt -r llm-api.txt

RUN playwright install

RUN playwright install-deps

COPY ./llm_api llm_api/
COPY ./docker-certs docker-certs/

EXPOSE 9000

CMD ["python", "-c", "from llm_api.cli.server import cli; cli(prog_name='llm', args=['server', 'run'])"]

