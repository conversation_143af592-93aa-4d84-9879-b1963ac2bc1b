FROM bricklayerai/python:1.0

RUN apt-get update && apt-get upgrade -y &&  apt-get install -y libmagic1 jq curl libzbar0 zbar-tools && apt-get clean

# Step 2: Remove any leftover Docker binaries (for CVE-2024-7042 safety)
RUN rm -f /usr/bin/containerd-shim* /usr/bin/docker-proxy /usr/local/bin/docker* || true

RUN apt update && apt upgrade -y linux-libc-dev && apt clean

WORKDIR /code

COPY ./requirements/common.txt .
RUN python -m pip install --no-cache-dir -r common.txt

COPY ./requirements/llm-api.txt .
RUN python -m pip install --no-cache-dir -r common.txt -r llm-api.txt

RUN playwright install

RUN playwright install-deps

COPY ./llm_api llm_api/
COPY ./docker-certs docker-certs/

EXPOSE 9000

ENV LLM__LOGGING__FORMAT="%(asctime)s.%(msecs)d %(levelname)s %(message)s"
ENV LLM__LOGGING__LEVEL="info"
ENV PORT=9000
ENV UVICORN_WORKERS_MAX_REQUESTS=50
ENV WORKERS=1

ENTRYPOINT ["uvicorn", "llm_api.blai_api.main:get_app"]

CMD [ \
  "--factory", \
  "--host", "0.0.0.0", \
  "--limit-max-requests", "${UVICORN_WORKERS_MAX_REQUESTS}", \
  "--log-level", "${LLM__LOGGING__LEVEL}", \
  "--port", "${PORT}", \
  "--workers", "${WORKERS}" \
]
