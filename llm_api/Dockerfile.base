FROM ubuntu:24.04

ENV DEBIAN_FRONTEND=noninteractive \
    DEBCONF_NONINTERACTIVE_SEEN=true \
    PYTHONUNBUFFERED=1

RUN apt-get update && apt-get upgrade -y && \
    apt-get install -y --no-install-recommends \
    software-properties-common \
    build-essential \
    libgl1 \
    poppler-utils \
    tesseract-ocr \
    docker.io \
    wget \
    gnupg \
    libpq-dev \
    libssl-dev \
    zlib1g-dev \
    libbz2-dev \
    libreadline-dev \
    libsqlite3-dev \
    libffi-dev \
    liblzma-dev && \
    apt-get clean && rm -rf /var/lib/apt/lists/*

RUN wget https://www.python.org/ftp/python/3.11.2/Python-3.11.2.tgz && \
    tar -xzf Python-3.11.2.tgz && \
    cd Python-3.11.2 && \
    ./configure --enable-optimizations && \
    make -j$(nproc) && \
    make altinstall && \
    cd .. && \
    rm -rf Python-3.11.2.tgz Python-3.11.2

RUN wget https://bootstrap.pypa.io/get-pip.py && \
    python3.11 get-pip.py && \
    rm get-pip.py

RUN update-alternatives --install /usr/bin/python3 python3 /usr/local/bin/python3.11 1 && \
    update-alternatives --install /usr/bin/python python /usr/local/bin/python3.11 1

RUN python --version && pip --version && python -c "import lzma; print('lzma module is available')"

RUN ldconfig && ldd --version

CMD ["python", "--version"]
