#!/usr/bin/env sh

reporter_service=$REPORTER_SERVICE
s3_report_location=$S3_REPORT_LOCATION

today=$(date -u "+%Y_%m_%d")
yesterday=$(date -u -d "yesterday" "+%Y_%m_%d")

report_file="report_$yesterday.csv"
dev_report_url="$reporter_service/procedure_report?env=dev&since=$yesterday&until=$today"
echo "Calling $dev_report_url"
curl -s -X GET $dev_report_url > $report_file
dev_report_location="s3://$s3_report_location/dev/$report_file"
echo "Uploading to $dev_report_location"
aws s3 cp $report_file $dev_report_location

prod_report_url="$reporter_service/procedure_report?env=prod&since=$yesterday&until=$today"
echo "Calling $prod_report_url"
curl -s -X GET $prod_report_url > $report_file
prod_report_location="s3://$s3_report_location/prod/$report_file"
echo "Uploading to $prod_report_location"
aws s3 cp $report_file $prod_report_location