import requests
import json
import random
import os

from dotenv import load_dotenv
from datetime import datetime, timedelta

load_dotenv()

test_input = {
    "id": "1",
    "type": "coordinator",
    "spec": {
        "name": "Brain Agent",
        "description": "",
        # "llm": {
        #     "type": "azure_chat",
        #     "data": {
        #         "temperature": 0.0,
        #         "deployment_name": "Local-GPT4",
        #         "openai_api_version": "2023-05-15",
        #         "openai_api_type": "azure",
        #         "openai_api_base": "https://local-gpt4-bricklayerai.openai.azure.com/",
        #         "openai_api_key": "********************************",
        #     },
        # },
        "prompt": """ I want you to act as a generative AI assistant powering Bricklayer AI. 
You are designed to be able to assist with a wide range of tasks using a list of trusty tools that are better at their tasks than you. 
You MUST follow a list of rules while answering any question asked of you:

***** RULES *****

Rule 1) Whenever possible you MUST use the tools to perform necessary actions and to retrieve relevant information. 

Rule 2) You MUST create a plan to answer the questions regardless of how simple they are first, and then execute the plan step by step.

Rule 3) For complex tasks, some might require the use of more than one tool. You MUST break the task down into atomic tasks and solve the main task step by step using the different tools at hand.

Rule 4) If more than one tool is useful for retrieving the information required - use ALL relevant tools one after the other to retrieve answers before summarizing the responses into a single coherent answer. 

Rule 5) You MUST use a tool for all technical questions about cybersecurity and for CISSP tasks no matter how simple and not try to answer the question yourself

Rule 6) ALWAYS include as many details as possible to best respond to an answer. Ideally follow the answering blueprint.

*****************

It is recommended that you follow the blueprint below to improve the quality of the answer:

*** BLUEPRINT ***

1) Have the answer in an ordered list format 

2) Include any references for further reading at the end.

*****************

Overall, you are a powerful system that can help with a wide range of tasks and provide valuable insights and information on a wide range of topics. You are here to assist. 
Make sure to think step by step.
""",
    },
    "children": [
        {
            "id": "1",
            "type": "coordinator",
            "spec": {
                "name": "Research Agent",
                "description": """Useful for researching any question and retrieving background information. 
    It is good practice to use this tool before answering any question or performing any analysis.
    Input should be a fully formed question.""",
                # "llm": {
                #     "type": "azure_chat",
                #     "data": {
                #         "temperature": 0.0,
                #         "deployment_name": "Local-GPT35Turbo",
                #         "openai_api_version": "2023-05-15",
                #         "openai_api_type": "azure",
                #         "openai_api_base": "https://local-gpt35turbo-bricklayerai.openai.azure.com/",
                #         "openai_api_key": "********************************",
                #     },
                # },
                "prompt": """ I want you to act as a Researcher. You are designed to be able to assist with 
question-answering tasks using a list of trusty tools that are more reliable resources than you. 
Use the information retrieved to create a DETAILED report.
You should ALWAYS USE at least 2 tools to try and answer the question. 
CREATE a PLAN with 2 tools you would like to use and then execute them.
                
You must THINK and decide which information out of all of the answers retrieved is relevent.
If NONE of them have relevant information, TRY answering the question yourself

You MUST follow a list of rules while answering any question asked of you:

***** RULES *****

Rule 1) Whenever possible you MUST use the tools to retrieve relevant information. 

Rule 2) You MUST CREATE A PLAN to answer the questions regardless of how simple they are first, and then execute the plan step by step. Always ask follow-up questions to better your research. Be curious about the impact of the previous question on a company, if there are any patterns associated with it, et cetera.

Rule 3) For complex questions, some might require the use of more than one tool. You MUST break the task down into atomic tasks and solve the main task step by step.

*****************

It is recommended that you follow the blueprint below to improve the quality of the answer:

*** BLUEPRINT ***

1) Always INCLUDE as many details as possible.

2) The answers must be in an itemized list if possible

*****************

Overall, you are a powerful system that can help with a wide range of tasks and provide valuable insights and information on a wide range of topics. You are here to assist. 
Make sure to think step by step.
""",
            },
            "children": [
                {
                    "id": "1",
                    "type": "publicDataSource",
                    "spec": {
                        "name": "Blogs Tool",
                        #                 "description": f"""useful for when you need to answer questions about security news.
                        # Input should be a STRING of a list with a fully formed question along with all the IMPORTANT keywords in the question AND any dates inferred from the question.
                        # These keywords are usually PROPER NOUNS. Use the date specified if explicitely mentioned, otherwise infer the required date
                        # from the question AND that today is {datetime.now().strftime("%Y_%m_%d %A")}.
                        # ACTION INPUT MUST be in the format below:
                        # "This is a question about yesterday's news on TopicA, TopicB, TopicC" --- "TopicA","TopicB","TopicC","{(datetime.now() - timedelta(days=1)).strftime('%Y_%m_%d')}"
                        # EXAMPLES:
                        # - "What was the cybersecurity news from a day before yesterday?" --- "cybersecurity, news, {(datetime.now() - timedelta(days=2)).strftime('%Y_%m_%d')}"
                        # - "What happened in cybersecurity for finance last week?" ---  "cybersecurity, finance, {(datetime.now() - timedelta(days=1)).strftime('%Y_%m_%d')}, {(datetime.now() - timedelta(days=2)).strftime('%Y_%m_%d')}, {(datetime.now() - timedelta(days=3)).strftime('%Y_%m_%d')}, {(datetime.now() - timedelta(days=4)).strftime('%Y_%m_%d')}, {(datetime.now() - timedelta(days=5)).strftime('%Y_%m_%d')}"
                        # - "Tell about any incidents that happened 3 days ago?" --- "incidents, {(datetime.now() - timedelta(days=3)).strftime('%Y_%m_%d')}"
                        # """,
                        "description": """useful for when you need to answer questions about security news.
                    Input should be a STRING of a list with a fully formed question along with all the IMPORTANT keywords in the question AND any dates inferred from the question.
                    These keywords are usually PROPER NOUNS. Use the date specified if explicitely mentioned, otherwise infer the required date
                    from the question AND that today is {0_days_ago} {today_str}.
                    ACTION INPUT MUST be in the format below:
                    "This is a question about yesterday's news on TopicA, TopicB, TopicC" --- "TopicA","TopicB","TopicC","{1_days_ago}"
                    EXAMPLES:
                    - "What was the cybersecurity news from a day before yesterday?" --- "cybersecurity, news, {2_days_ago}"
                    - "What happened in cybersecurity for finance last week?" ---  "cybersecurity, finance, {1_days_ago}, {2_days_ago}, {3_days_ago}, {4_days_ago}, {5_days_ago}"
                    - "Tell about any incidents that happened 3 days ago?" --- "incidents, {3_days_ago}"
                    """,
                        "chain_type": "stuff",
                    },
                },
                {
                    "id": "1",
                    "type": "plugin",
                    "spec": {
                        "name": "NVD Analyzer",
                        "description": """useful for getting common vulnerabily information about specific CPEs, CWEs, Device Names, Date Ranges and CVEIDs using the NVD (National Vulnerability) api.
                                        Input should be a fully formed question including a specific ID such as a CWE, CVE or CPE.
                                        CORRECT Examples are: 'Can you tell me the vulnerabilities associated with CWE-89?' or 'What do we know about CVE-2023-23397?'
                                        INCORRECT Examples are: 'Can you tell me about vulnerabiilities?'
                                        All responses should be considered sourced from [source: "NVD API"
                                    ]""",
                        "urls": [
                            {
                                "base_url": "https://services.nvd.nist.gov",
                                "method": "GET",
                                "endpoints": [
                                    {
                                        "path": "/rest/json/cves/2.0?cpeName=[value]",
                                        "definition": 'This endpoint should retrieve all Common Vulnerabilities (CVE) associated with a specific CPE identified by its CPE name. A CPE Name is a string of characters comprised of 13 colon separated values that describe a product. In CPEv2.3 the first two values are always \“cpe\” and \“2.3\”. The 11 values that follow are referred to as the CPE components. When filtering by cpeName the part, vendor, product, and version components are REQUIRED to contain values other than "*".',
                                        "arguments": {
                                            "CPE Name": {
                                                "examples": [
                                                    "cpe:2.3:o:microsoft:windows_10:1607:*:*:*:*:*:*:*",
                                                    "cpe:2.3:a:microsoft:internet_explorer:8.0.6001:beta:*:*:*:*:*:*",
                                                ]
                                            }
                                        },
                                    },
                                    {
                                        "path": "/rest/json/cves/2.0?cveId=[value]",
                                        "definition": "This endpoint returns information about a specific vulnerability identified by its unique Common Vulnerabilities and Exposures identifier (the CVE ID).",
                                        "arguments": {
                                            "CVE ID": {
                                                "examples": [
                                                    "CVE-2019-1010218",
                                                    "CVE-2022-22954",
                                                ]
                                            }
                                        },
                                    },
                                    {
                                        "path": "/rest/json/cves/2.0?cvssV2Metrics=[value]",
                                        "definition": "This endpoint returns only the CVEs that match the provided CVSSv2 vector string. Either full or partial vector strings may be used.",
                                        "arguments": {
                                            "CVSS V2 Vector string": {
                                                "examples": [
                                                    "AV:N/AC:H/Au:N/C:C/I:C/A:C",
                                                    "AV:L/AC:H/Au:M/C:N/I:N/A:N",
                                                ]
                                            }
                                        },
                                    },
                                    {
                                        "path": "/rest/json/cves/2.0?cvssV3Metrics=[value]",
                                        "definition": "This endpoint returns only the CVEs that match the provided CVSS V3 vector string. Either full or partial vector strings may be used.",
                                        "arguments": {
                                            "CVSS V3 vector string": {
                                                "examples": [
                                                    "AV:L/AC:L/PR:L/UI:R/S:U/C:N/I:L/A:L"
                                                ]
                                            }
                                        },
                                    },
                                    {
                                        "path": "/rest/json/cves/2.0?cweId=[value]",
                                        "definition": "This endpoint returns only the CVE that include a weakness identified by Common Weakness Enumeration using the provided CWE ID.",
                                        "arguments": {
                                            "CWE ID": {
                                                "examples": ["CWE-287", "CWE-89"]
                                            }
                                        },
                                    },
                                    {
                                        "path": "/rest/json/cves/2.0?keywordSearch=[value]",
                                        "definition": "This endpoint returns only the CVEs where a word or phrase is found in the current description.",
                                        "arguments": {
                                            "Keywords": {
                                                "examples": ["Microsoft", "Debian"]
                                            }
                                        },
                                    },
                                    {
                                        "path": "/rest/json/cves/2.0/?pubStartDate=[value 1]&pubEndDate=[value 2]",
                                        "definition": 'This endpoint return only the CVEs that were added to the NVD (i.e., published) during the specified period defaulting to GMT. If filtering by the published date, both pubStartDate and pubEndDate are REQUIRED. The maximum allowable range when using any date range parameters is 120 consecutive days. Values must be entered in the extended ISO-8061 date/time format: [YYYY][“-”][MM][“-”][DD][“T”][HH][“:”][MM][“:”][SS][Z]. The "T" is a literal to separate the date from the time. The Z indicates an optional offset-from-UTC. ',
                                        "arguments": {
                                            "Start Date": {
                                                "examples": [
                                                    "2021-08-04T00:00:00.000",
                                                    "2021-10-22T00:00:00.000",
                                                ]
                                            },
                                            "End Date": {
                                                "examples": [
                                                    "2021-08-04T00:00:00.000",
                                                    "2021-10-22T00:00:00.000",
                                                ]
                                            },
                                        },
                                    },
                                ],
                                "select_endpoint_prompt": """
                                                You are an api composing assistant that composes an API URL by following the steps below:
                                                Step 1) Analyze the input and identify the relevant entity.
                                                Here are a list of options and examples-
                                                {examples}
                                                Step 2) Pick the correct endpoint from the list of options below based on the intent of the user and the description of the end-point. Options: {options}
                                                \nStep 3) Compose the API by replacing [value] in the correct end-point with the actual value of the entity identified.
                                                Here are some examples:
                                                Example 1)
                                                [Input] Tell me all the vulnerabilities that were published between 1st Jan 2021 and 14th Jan 2021 end of day in Easter time (GMT -5 hours)?
                                                [Output] /rest/json/cves/2.0/?pubStartDate=2020-01-01T00: 00: 00.000-05: 00&pubEndDate=2020-01-14T23: 59: 59.999-05: 00
                                                Example 2)
                                                [Input] What vulnerabilities were published between the 4th August 2021 and 22nd October 2021 GMT?
                                                [Output] /rest/json/cves/2.0/?pubStartDate=2021-08-04T00: 00: 00.000&pubEndDate=2021-10-22T00: 00: 00.000
                                                Example 3)
                                                [Input] What do we know about vulnerabilities associated with Microsoft Windows 10 Version 1607?
                                                [Output] /rest/json/cves/2.0?cpeName=2.3:o:microsoft:windows_10_1607:-:*:*:*:*:*:*
                                                Generate an [Output] for the submitted [Input] using the steps mentioned. MAKE SURE to include only the ENDPOINT in the [Output]
                                                """,
                                "select_endpoint_validation_pattern": "\/rest\/json\/cves\/2.0\?[0-9a-zA-Z_\/\-:&=\.\*]*",
                                "interpret_response_prompt": """
                                                I want you to act as a cybersecurity analyst that summarises an input json string
                                                recieved from an endpoint {endpoint} of the National Vulnerability Database
                                                and returns a detailed list of information including all links for further information.
                                                Do not include explanations of the JSON but rather focus on the inferences drawn from it.
                                                The json string is the information on common vulnerabilities.
                                                Try answering questions such as what are the vulnerabilities, what is their description, what associated threat actors are,
                                                what devices are typically affected and its description.
                                                """,
                            }
                        ],
                    },
                },
                {
                    "id": "1",
                    "type": "plugin",
                    "spec": {
                        "name": "AlienVault Analyzer",
                        "description": """
                    useful for analyzing specific domains, ip addresses, host-names or CVEs using the alienvault otx api.
                    Input should be a fully formed question.
                    CORRECT Examples are: 'Can you analyze example.com?' or 'Is the IP Address 67.45.123.345 considered malicious?'
                    WRONG Examples are: 'What is an Indicator of Compromise (IOC)?'
                    All responses should be considered sourced from [source: "AlienVault API"]
                """,
                        "credentials": {
                            "type": "api_key",
                            "data": {
                                "key": os.environ["ALIENVAULT_API_KEY"],
                                "header": "X-OTX-API-KEY",
                            },
                        },
                        "urls": [
                            {
                                "base_url": "https://otx.alienvault.com",
                                "method": "GET",
                                # "credentials": {
                                #     "type": 1,
                                #     "data": {
                                #         "header": "X-OTX-API-KEY",
                                #         "key": "...",
                                #     },
                                # },
                                "endpoints": [
                                    {
                                        "path": "/api/v1/indicators/IPv4/[value]/general",
                                        "definition": "This endpoint should retrieve information related to the IPv4 indicator specified by the IP address.",
                                        "arguments": {
                                            "IPv4 Address": {
                                                "examples": ["*******", "**********"]
                                            }
                                        },
                                    },
                                    {
                                        "path": "/api/v1/indicators/IPv6/[value]/general",
                                        "definition": "This endpoint should retrieve information related to the IPv6 indicator specified by the IP address.",
                                        "arguments": {
                                            "IPv6 Address": {
                                                "examples": [
                                                    "2001:db8:3333:4444:5555:6666:7777:8888",
                                                    "2001:db8::",
                                                    "2001:0db8:0001:0000:0000:0ab9:C0A8:0102",
                                                ]
                                            }
                                        },
                                    },
                                    {
                                        "path": "/api/v1/indicators/domain/[value]/general",
                                        "definition": "This endpoint should retrieve information related to the domain indicator specified by the domain name.",
                                        "arguments": {
                                            "Domain Name": {
                                                "examples": [
                                                    "example.com",
                                                    "spywaresite.info",
                                                ]
                                            }
                                        },
                                    },
                                    {
                                        "path": "/api/v1/indicators/hostname/[value]/general",
                                        "definition": "This endpoint should retrieve information related to the hostname indicator specified by the hostname.",
                                        "arguments": {
                                            "Host Name": {
                                                "examples": [
                                                    "otx.alienvault.com",
                                                    "bad-guys.no-ip.org",
                                                    "alpha.beta.google.co.uk",
                                                ]
                                            }
                                        },
                                    },
                                    {
                                        "path": "/api/v1/indicators/file/[value]/general",
                                        "definition": "This endpoint should retrieve information related to the file indicator specified by the file hash.",
                                        "arguments": {
                                            "File Hash": {
                                                "examples": [
                                                    "6c5360d41bd2b14b1565f5b18e5c203cf512e493"
                                                ]
                                            }
                                        },
                                    },
                                    {
                                        "path": "/api/v1/indicators/url/[value]/general",
                                        "definition": "This endpoint should retrieve information related to the URL indicator specified by the URL.",
                                        "arguments": {
                                            "URL": {
                                                "examples": [
                                                    "http://www.fotoidea.com/sport/4x4_san_ponso/slides/IMG_0068.html"
                                                ]
                                            }
                                        },
                                    },
                                    {
                                        "path": "/api/v1/indicators/cve/[value]/general",
                                        "definition": "This endpoint should retrieve information related to the Common Vulnerabilities and Exposures (CVE) indicator specified by the CVE identifier.",
                                        "arguments": {
                                            "CVE": {"examples": ["CVE-2014-0160"]}
                                        },
                                    },
                                ],
                                "select_endpoint_prompt": """
                            You are an api composing assistant that composes an API URL by following the steps below:
                            Step 1) Analyze the input and identify the relevant entity.
                            Here are a list of options and examples-
                            {examples}
                            Step 2) Pick the correct endpoint from the list of options below based on the intent of the user and the description of the end-point. Options:
                            {options}
                            \nStep 3) Compose the API by replacing [value] in the correct end-point with the actual value of the entity identified.
                            Here are some examples:
                            Example 1)
                            [Input] Analyze bricklayer.ai
                            [Output] /api/v1/indicators/domain/bricklayer.ai/general
                            Example 2)
                            [Input] Is ************* malicious?
                            [Output] /api/v1/indicators/IPv4/*************/general
                            Example 3)
                            [Input] Does 2a03:2880:10:1f02:face:b00c::25 have any issues?
                            [Output] /api/v1/indicators/IPv6/2a03:2880:10:1f02:face:b00c::25/general
                            Generate an [Output] for the submitted [Input] using the steps mentioned. MAKE SURE to include only the ENDPOINT in the [Output]
                        """,
                                "select_endpoint_validation_pattern": "\/api\/[0-9a-zA-Z\/\-\.]*",
                                "interpret_response_prompt": """
                            I want you to act as a cybersecurity analyst that summarises an input json string
                            recieved from an endpoint {endpoint} of the AlienVault Open Threat Exchange Platform
                            and returns a detailed list of information including all links for further information.
                            Do not include explanations of the JSON but rather focus on the inferences drawn from it.
                            The json string is the information on a specific Indicator of Compromise (IOC)
                            Try answering questions such as whether the Indicator seems malicious, what associated threat actors are,
                            what devices are typically affected and its description
                        """,
                            }
                        ],
                    },
                },
                {
                    "id": "1",
                    "type": "tidal",
                    "spec": {},
                },
                # {
                #     "id": "1",
                #     "type": "summarization",
                #     "spec": {
                #         "name": "API Response Summarisation Tool",
                #         "description": "fsdfsdf useful when summarising external files at a known location. This tool should ONLY be used for EXTERNAL files. Input MUST be the location of the file for example: sample.txt or samplefolder/another_sample.txt",
                #         "prompt": """
                #             I want you to act as a cybersecurity analyst that summarises {file_details}
                #             and returns a detailed list of information including all links for further information.
                #             Do not include explanations of specific terms but rather focus on the inferences drawn from it.
                #             The json string is the information on vulnerabilities. Key items to look for in the JSON include
                #             - Descriptions in English,
                #             - CVSS V3 Base Scores and Severity,
                #             - Weaknesses and CWE-ID.
                #             Try answering questions such as:
                #             - Does the Indicator seems malicious?
                #             - What are associated threat actors are?
                #             - What devices are typically affected and what is their description?
                #             MAKE SURE that your answer is in valid MARKDOWN.
                #             """,
                #     },
                # },
            ],
        },
        {
            "id": "1",
            "type": "coordinator",
            "spec": {
                "name": "Librarian Agent",
                "description": """Useful for answering questions about MY organization using private data.""",
                # "llm": {
                #     "type": "azure_chat",
                #     "data": {
                #         "temperature": 0.0,
                #         "deployment_name": "Local-GPT35Turbo",
                #         "openai_api_version": "2023-05-15",
                #         "openai_api_type": "azure",
                #         "openai_api_base": "https://local-gpt35turbo-bricklayerai.openai.azure.com/",
                #         "openai_api_key": "********************************",
                #     },
                # },
                "prompt": """ I want you to act as a Researcher. You are designed to be able to assist with 
question-answering tasks using a list of trusty tools that are more reliable resources than you. 
Use the information retrieved to create a DETAILED report.
You should ALWAYS USE at least 2 tools to try and answer the question. 
CREATE a PLAN with 2 tools you would like to use and then execute them.
                
You must THINK and decide which information out of all of the answers retrieved is relevent.
If NONE of them have relevant information, TRY answering the question yourself

You MUST follow a list of rules while answering any question asked of you:

***** RULES *****

Rule 1) Whenever possible you MUST use the tools to retrieve relevant information. 

Rule 2) You MUST CREATE A PLAN to answer the questions regardless of how simple they are first, and then execute the plan step by step. Always ask follow-up questions to better your research. Be curious about the impact of the previous question on a company, if there are any patterns associated with it, et cetera.

Rule 3) For complex questions, some might require the use of more than one tool. You MUST break the task down into atomic tasks and solve the main task step by step.

*****************

It is recommended that you follow the blueprint below to improve the quality of the answer:

*** BLUEPRINT ***

1) Always INCLUDE as many details as possible.

2) The answers must be in an itemized list if possible

*****************

Overall, you are a powerful system that can help with a wide range of tasks and provide valuable insights and information on a wide range of topics. You are here to assist. 
Make sure to think step by step.
""",
            },
            "children": [
                {
                    "id": "1",
                    "type": "private_datastore",
                    "className": "BricklayerTool",
                    "spec": {
                        "display_name": "My datastore",
                        "display_description": "Information about Bricklayer AI",
                        "organization_id": "144738b9-031d-4793-9f16-1f136effd09d",
                        "datastore_id": "15a2c7ac-7a21-1234-1234-1f136effd09d",
                    },
                }
            ],
        },
    ],
}

with open("ceva2.json", "w") as f:
    json.dump(test_input, f, indent=2)

with open("ceva3.json", "r") as f:
    test_input = json.load(f)

resps = []

for i in range(1):
    resps.append(
        requests.post(
            # "http://localhost:9000/message-streaming",
            "http://localhost:9000/message",
            data=json.dumps(
                {
                    # "message": "Analyze *************",
                    "root_component": test_input,
                    "message": "Has Bricklayer AI been affected by malware?",
                    # "message": "Tell me about CWE-78",
                    # "message": "Analyze google.com",
                    # "message": "What is kubernetes?",
                    # "message": "What does the term 'trial' mean?",
                    # "message": "How does the WannaCry malware work?",
                    # "message": "What do you know about CVE-2017-5645?",
                    # "message": "Please analyze ***************",
                    # "message": "What are the latest detected vulnerabilities from 2023?",
                    # "message": "What is Blue Coat Systems?",
                    # "message": "analyze tion.ro",
                    # "message": "What do we know about the Moveit malware?",
                    # "message": "Using the Tidal tool, what can you tell me about APT28?",
                    # "message": "What is Dell PowerEdge Motherboard?",
                    # "message": "What do we know about the MoveIt malware and has my company had any related incidents in the past?",
                    # "message": "What do we know about the MoveIt malware?",
                    # "message": "What do you know about the log4j exploit?",
                    "organizationId": "144738b9-031d-4793-9f16-1f136effd09d",
                    "conversationId": f"convId_{random.randint(0,100)}",
                    "messageId": f"msgId_{random.randint(0,100)}",
                    # "dataStores": [
                    #     {
                    #         "dataStoreId": "46978e38-5df1-45cb-95e1-c433abfe3162",
                    #         "organizationId": "144738b9-031d-4793-9f16-1f136effd09d",
                    #         "dataStoreType": "pdf",
                    #     }
                    # ],
                    "storeJsonLocation": "procedure_data/tests",
                }
            ),
            # stream=True,
        )
    )

from concurrent.futures import ThreadPoolExecutor
import time


def read_response(resp, idx):
    # start = 0

    with open(f"resp_{idx}.txt", "w") as f:
        start = time.perf_counter()
        for token in resp.iter_content(128):
            # if not start:

            f.write(token.decode("utf-8").replace("<<data>>", "") + "\n")

    end = time.perf_counter()

    print(f"Call {idx} finished in: {end-start}")
    # f.write(token + "\n")


with ThreadPoolExecutor() as pool:
    for idx, resp in enumerate(resps):
        pool.submit(read_response, resp, idx)
