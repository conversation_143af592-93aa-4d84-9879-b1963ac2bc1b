import json
from textwrap import indent
import requests
import random

procedure_spec = {
    "name": "CDI procedure",
    "description": "Useful for analyzing json alerts using the CDI procedure. \nInput must be the string to be analyzed. BE SURE TO REMOVE DOUBLE QUOTES IN THE STRING.",
    "credentials": {
        "type": "api_key",
        "data": {
            "header": "Authorization",
            "key": "p~BOS6otOpIkqmnhPUSqLU5S5Edrj",
        },
    },
    "urls": [],
}

resp = requests.post(
    "http://localhost:9000/message",
    data=json.dumps(
        {
            "aiFilteringPayload": {
                "components": [{"id": 1, "type": "plugin", "spec": procedure_spec}]
            },
            "message": '{"tenantId":"cn://guest-services.alienvault.cloud","timestamp":1706820008364,"alarm":{"alarm_events_count":25,"alarm_source_countries":["SG"],"http_hostname":"xmlrpc.php","alarm_sensor_sources":["438b255c-35bd-4de2-b1ab-42990483e8a2"],"timestamp_occured":"1706819995000","uuid":"02f25da7-e3bd-6806-3a9f-6783ccd910de","alarm_source_cities":["Singapore"],"event_type":"Alarm","rule_attack_tactic":["Credential Access"],"number_of_events":25,"timestamp_received":"1706820008325","alarm_response_codes":["200"],"rule_strategy":"Brute Force Authentication","packet_data":["6ba13a0c-4fc7-fdba-0819-869b90e4d9f0","912bcbeb-492f-38be-bea5-0801bb2b8e88","85203482-e294-c64e-67a8-68b8c125cca7","77401575-4b64-7a0d-356b-dbb11ce35125","e586a876-33e1-a2db-d080-aa7d0ed76418","eca20185-e0b9-1717-2496-e28f1c1a4b04","5714d90c-95de-ebec-0c83-c0839d6b3bc7","fef24fd6-ed4b-3955-f633-7e2116e08272","72871caa-b425-b5da-9084-8724422b3046","3bd84a57-2a31-d7cc-b944-c17a1ec0e5d7","fe7ef0ce-f16a-5cf2-886e-5b0be360f71b","37d8d4b3-0126-87d7-806f-f88baa75eb56","ca67a006-eec0-bf82-0d15-3532a7295e24","bf615d5b-90f3-9313-cc49-8da10b68f618","52aa3ac4-1f20-3d2b-8721-d2b311740638","ce747a92-b1b4-4b8c-6f28-85a5d8869a54","b0db4a3f-c861-3f61-6bdd-bb46de6830e6","842d769b-720b-2a70-fc9a-13c42f43fe16","ff067bd5-cd5d-1eef-5648-2c9cafa0b696","8650f766-daa8-67ee-646b-329b0d87a112","338b1ea4-a1c9-313c-251a-bbc920fa76c0","deb1c852-9012-5caa-9b14-6f6c3434449f","f2f54a01-bae5-f504-2821-9b931fb139fc","7e6a7872-dd93-2b2f-72fa-359278062d0a","14e785d0-a27e-9079-40e5-954f2225c9dd"],"alarm_destinations":["gsaze204"],"priority":"20","request_url":"//xmlrpc.php","alarm_source_longitudes":["103.851780"],"alarm_source_organisations":["cloudflare  inc."],"sensor_uuid":"438b255c-35bd-4de2-b1ab-42990483e8a2","transient":false,"source_canonical":"***************","packet_type":"alarm","status":"open","rule_intent":"Delivery & Attack","alarm_source_ips":["***************"],"rule_dictionary":"HTTPRules-Dict","destination_name":"gsaze204","request_method":"POST","needs_enrichment":false,"source_organisation":"cloudflare  inc.","rule_method":"WordPress","x_att_tenant_subdomain":"guest-services","priority_label":"low","x_att_tenantid":"ada0ec23-c8c3-45a9-9372-34d8a19c4af9","suppressed":"false","source_name":"***************","destination_canonical":"gsaze204","plugin_device":"apache","alarm_sources":["***************"],"rule_attack_id":"T1110","timestamp_to_storage":"1706820008336","highlight_fields":["source_canonical"," source_organisation","destination_canonical","request_method","request_user_agent","request_url","alarm_response_codes","plugin_device","rule_attack_id","rule_attack_tactic","rule_attack_technique"],"alarm_source_names":["***************"],"request_user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36","rule_id":"WordPressBruteforce","needs_internal_enrichment":false,"alarm_source_latitudes":["1.287950"],"timestamp_arrived":"1706820008336","rule_attack_technique":"Brute Force"},"events":[{"was_fuzzied":false,"http_hostname":"xmlrpc.php","timestamp_occured":"1706819942000","plugin_device_version":"1","uuid":"fef24fd6-ed4b-3955-f633-7e2116e08272","rep_device_hostname":"50eac293-b665-418e-afd1-714dd8866199","rep_device_asset_id":"50eac293-b665-418e-afd1-714dd8866199","used_hint":true,"tag":"apache","was_guessed":false,"timestamp_received":"1706819983730","source_latitude":"1.287950","time_offset":"Z","request_url":"//xmlrpc.php","sensor_uuid":"438b255c-35bd-4de2-b1ab-42990483e8a2","bytes_out":453,"source_longitude":"103.851780","transient":false,"event_name":"HTTP OK","source_region":"01","source_registered_country":"SG","source_canonical":"***************","packet_type":"log","response_code":200,"control_id":"ada0ec23-c8c3-45a9-9372-34d8a19c4af9","plugin_version":"0.10","log":"*************** - - [01/Feb/2024:15:39:02 -0500] \\"POST //xmlrpc.php HTTP/1.1\\" 200 453 \\"-\\" \\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36\\"","event_description":"Standard response for successful HTTP requests. The actual response will depend on the request method used. In a GET request, the response will contain an entity corresponding to the requested resource. In a POST request the response will contain an entity describing or containing the result of the action.","destination_name":"gsaze204","request_method":"POST","needs_enrichment":true,"source_organisation":"cloudflare  inc.","received_from":"gsaze204","application_protocol":"HTTP/1.1","source_address":"***************","x_att_tenant_subdomain":"guest-services","x_att_tenantid":"ada0ec23-c8c3-45a9-9372-34d8a19c4af9","suppressed":"false","source_name":"***************","plugin_device_type":"Web Server","destination_canonical":"gsaze204","source_city":"Singapore","rep_dev_canonical":"50eac293-b665-418e-afd1-714dd8866199","plugin_device":"apache","timestamp_to_storage":"1706819983799","highlight_fields":["request_url","response_code","request_method","bytes_out"],"time_zone":"-0500","request_user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36","needs_internal_enrichment":false,"plugin":"Apache","source_country":"SG","timestamp_arrived":"1706819983799"},{"was_fuzzied":false,"http_hostname":"xmlrpc.php","timestamp_occured":"1706819983000","plugin_device_version":"1","uuid":"6ba13a0c-4fc7-fdba-0819-869b90e4d9f0","rep_device_hostname":"50eac293-b665-418e-afd1-714dd8866199","rep_device_asset_id":"50eac293-b665-418e-afd1-714dd8866199","used_hint":true,"tag":"apache","was_guessed":false,"timestamp_received":"1706820007982","source_latitude":"1.287950","time_offset":"Z","request_url":"//xmlrpc.php","sensor_uuid":"438b255c-35bd-4de2-b1ab-42990483e8a2","bytes_out":472,"source_longitude":"103.851780","transient":false,"event_name":"HTTP OK","source_region":"01","source_registered_country":"SG","source_canonical":"***************","packet_type":"log","response_code":200,"control_id":"ada0ec23-c8c3-45a9-9372-34d8a19c4af9","plugin_version":"0.10","log":"*************** - - [01/Feb/2024:15:39:43 -0500] \\"POST //xmlrpc.php HTTP/1.1\\" 200 472 \\"-\\" \\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36\\"","event_description":"Standard response for successful HTTP requests. The actual response will depend on the request method used. In a GET request, the response will contain an entity corresponding to the requested resource. In a POST request the response will contain an entity describing or containing the result of the action.","destination_name":"gsaze204","request_method":"POST","needs_enrichment":true,"source_organisation":"cloudflare  inc.","received_from":"gsaze204","application_protocol":"HTTP/1.1","source_address":"***************","x_att_tenant_subdomain":"guest-services","x_att_tenantid":"ada0ec23-c8c3-45a9-9372-34d8a19c4af9","suppressed":"false","source_name":"***************","plugin_device_type":"Web Server","destination_canonical":"gsaze204","source_city":"Singapore","rep_dev_canonical":"50eac293-b665-418e-afd1-714dd8866199","plugin_device":"apache","timestamp_to_storage":"1706820008050","highlight_fields":["request_url","response_code","request_method","bytes_out"],"time_zone":"-0500","request_user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36","needs_internal_enrichment":false,"plugin":"Apache","source_country":"SG","timestamp_arrived":"1706820008049"},{"was_fuzzied":false,"http_hostname":"xmlrpc.php","timestamp_occured":"1706819951000","plugin_device_version":"1","uuid":"eca20185-e0b9-1717-2496-e28f1c1a4b04","rep_device_hostname":"50eac293-b665-418e-afd1-714dd8866199","rep_device_asset_id":"50eac293-b665-418e-afd1-714dd8866199","used_hint":true,"tag":"apache","was_guessed":false,"timestamp_received":"1706820007960","source_latitude":"1.287950","time_offset":"Z","request_url":"//xmlrpc.php","sensor_uuid":"438b255c-35bd-4de2-b1ab-42990483e8a2","bytes_out":472,"source_longitude":"103.851780","transient":false,"event_name":"HTTP OK","source_region":"01","source_registered_country":"SG","source_canonical":"***************","packet_type":"log","response_code":200,"control_id":"ada0ec23-c8c3-45a9-9372-34d8a19c4af9","plugin_version":"0.10","log":"*************** - - [01/Feb/2024:15:39:11 -0500] \\"POST //xmlrpc.php HTTP/1.1\\" 200 472 \\"-\\" \\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36\\"","event_description":"Standard response for successful HTTP requests. The actual response will depend on the request method used. In a GET request, the response will contain an entity corresponding to the requested resource. In a POST request the response will contain an entity describing or containing the result of the action.","destination_name":"gsaze204","request_method":"POST","needs_enrichment":true,"source_organisation":"cloudflare  inc.","received_from":"gsaze204","application_protocol":"HTTP/1.1","source_address":"***************","x_att_tenant_subdomain":"guest-services","x_att_tenantid":"ada0ec23-c8c3-45a9-9372-34d8a19c4af9","suppressed":"false","source_name":"***************","plugin_device_type":"Web Server","destination_canonical":"gsaze204","source_city":"Singapore","rep_dev_canonical":"50eac293-b665-418e-afd1-714dd8866199","plugin_device":"apache","timestamp_to_storage":"1706820008066","highlight_fields":["request_url","response_code","request_method","bytes_out"],"time_zone":"-0500","request_user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36","needs_internal_enrichment":false,"plugin":"Apache","source_country":"SG","timestamp_arrived":"1706820008065"},{"was_fuzzied":false,"http_hostname":"xmlrpc.php","timestamp_occured":"1706819969000","plugin_device_version":"1","uuid":"85203482-e294-c64e-67a8-68b8c125cca7","rep_device_hostname":"50eac293-b665-418e-afd1-714dd8866199","rep_device_asset_id":"50eac293-b665-418e-afd1-714dd8866199","used_hint":true,"tag":"apache","was_guessed":false,"timestamp_received":"1706820007959","source_latitude":"1.287950","time_offset":"Z","request_url":"//xmlrpc.php","sensor_uuid":"438b255c-35bd-4de2-b1ab-42990483e8a2","bytes_out":472,"source_longitude":"103.851780","transient":false,"event_name":"HTTP OK","source_region":"01","source_registered_country":"SG","source_canonical":"***************","packet_type":"log","response_code":200,"control_id":"ada0ec23-c8c3-45a9-9372-34d8a19c4af9","plugin_version":"0.10","log":"*************** - - [01/Feb/2024:15:39:29 -0500] \\"POST //xmlrpc.php HTTP/1.1\\" 200 472 \\"-\\" \\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36\\"","event_description":"Standard response for successful HTTP requests. The actual response will depend on the request method used. In a GET request, the response will contain an entity corresponding to the requested resource. In a POST request the response will contain an entity describing or containing the result of the action.","destination_name":"gsaze204","request_method":"POST","needs_enrichment":true,"source_organisation":"cloudflare  inc.","received_from":"gsaze204","application_protocol":"HTTP/1.1","source_address":"***************","x_att_tenant_subdomain":"guest-services","x_att_tenantid":"ada0ec23-c8c3-45a9-9372-34d8a19c4af9","suppressed":"false","source_name":"***************","plugin_device_type":"Web Server","destination_canonical":"gsaze204","source_city":"Singapore","rep_dev_canonical":"50eac293-b665-418e-afd1-714dd8866199","plugin_device":"apache","timestamp_to_storage":"1706820008081","highlight_fields":["request_url","response_code","request_method","bytes_out"],"time_zone":"-0500","request_user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36","needs_internal_enrichment":false,"plugin":"Apache","source_country":"SG","timestamp_arrived":"1706820008081"},{"was_fuzzied":false,"http_hostname":"xmlrpc.php","timestamp_occured":"1706819958000","plugin_device_version":"1","uuid":"842d769b-720b-2a70-fc9a-13c42f43fe16","rep_device_hostname":"50eac293-b665-418e-afd1-714dd8866199","rep_device_asset_id":"50eac293-b665-418e-afd1-714dd8866199","used_hint":true,"tag":"apache","was_guessed":false,"timestamp_received":"1706820008010","source_latitude":"1.287950","time_offset":"Z","request_url":"//xmlrpc.php","sensor_uuid":"438b255c-35bd-4de2-b1ab-42990483e8a2","bytes_out":472,"source_longitude":"103.851780","transient":false,"event_name":"HTTP OK","source_region":"01","source_registered_country":"SG","source_canonical":"***************","packet_type":"log","response_code":200,"control_id":"ada0ec23-c8c3-45a9-9372-34d8a19c4af9","plugin_version":"0.10","log":"*************** - - [01/Feb/2024:15:39:18 -0500] \\"POST //xmlrpc.php HTTP/1.1\\" 200 472 \\"-\\" \\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36\\"","event_description":"Standard response for successful HTTP requests. The actual response will depend on the request method used. In a GET request, the response will contain an entity corresponding to the requested resource. In a POST request the response will contain an entity describing or containing the result of the action.","destination_name":"gsaze204","request_method":"POST","needs_enrichment":true,"source_organisation":"cloudflare  inc.","received_from":"gsaze204","application_protocol":"HTTP/1.1","source_address":"***************","x_att_tenant_subdomain":"guest-services","x_att_tenantid":"ada0ec23-c8c3-45a9-9372-34d8a19c4af9","suppressed":"false","source_name":"***************","plugin_device_type":"Web Server","destination_canonical":"gsaze204","source_city":"Singapore","rep_dev_canonical":"50eac293-b665-418e-afd1-714dd8866199","plugin_device":"apache","timestamp_to_storage":"1706820008125","highlight_fields":["request_url","response_code","request_method","bytes_out"],"time_zone":"-0500","request_user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36","needs_internal_enrichment":false,"plugin":"Apache","source_country":"SG","timestamp_arrived":"1706820008125"},{"was_fuzzied":false,"http_hostname":"xmlrpc.php","timestamp_occured":"1706819985000","plugin_device_version":"1","uuid":"52aa3ac4-1f20-3d2b-8721-d2b311740638","rep_device_hostname":"50eac293-b665-418e-afd1-714dd8866199","rep_device_asset_id":"50eac293-b665-418e-afd1-714dd8866199","used_hint":true,"tag":"apache","was_guessed":false,"timestamp_received":"1706820008069","source_latitude":"1.287950","time_offset":"Z","request_url":"//xmlrpc.php","sensor_uuid":"438b255c-35bd-4de2-b1ab-42990483e8a2","bytes_out":472,"source_longitude":"103.851780","transient":false,"event_name":"HTTP OK","source_region":"01","source_registered_country":"SG","source_canonical":"***************","packet_type":"log","response_code":200,"control_id":"ada0ec23-c8c3-45a9-9372-34d8a19c4af9","plugin_version":"0.10","log":"*************** - - [01/Feb/2024:15:39:45 -0500] \\"POST //xmlrpc.php HTTP/1.1\\" 200 472 \\"-\\" \\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36\\"","event_description":"Standard response for successful HTTP requests. The actual response will depend on the request method used. In a GET request, the response will contain an entity corresponding to the requested resource. In a POST request the response will contain an entity describing or containing the result of the action.","destination_name":"gsaze204","request_method":"POST","needs_enrichment":true,"source_organisation":"cloudflare  inc.","received_from":"gsaze204","application_protocol":"HTTP/1.1","source_address":"***************","x_att_tenant_subdomain":"guest-services","x_att_tenantid":"ada0ec23-c8c3-45a9-9372-34d8a19c4af9","suppressed":"false","source_name":"***************","plugin_device_type":"Web Server","destination_canonical":"gsaze204","source_city":"Singapore","rep_dev_canonical":"50eac293-b665-418e-afd1-714dd8866199","plugin_device":"apache","timestamp_to_storage":"1706820008204","highlight_fields":["request_url","response_code","request_method","bytes_out"],"time_zone":"-0500","request_user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36","needs_internal_enrichment":false,"plugin":"Apache","source_country":"SG","timestamp_arrived":"1706820008204"},{"was_fuzzied":false,"http_hostname":"xmlrpc.php","timestamp_occured":"1706819986000","plugin_device_version":"1","uuid":"deb1c852-9012-5caa-9b14-6f6c3434449f","rep_device_hostname":"50eac293-b665-418e-afd1-714dd8866199","rep_device_asset_id":"50eac293-b665-418e-afd1-714dd8866199","used_hint":true,"tag":"apache","was_guessed":false,"timestamp_received":"1706820008056","source_latitude":"1.287950","time_offset":"Z","request_url":"//xmlrpc.php","sensor_uuid":"438b255c-35bd-4de2-b1ab-42990483e8a2","bytes_out":472,"source_longitude":"103.851780","transient":false,"event_name":"HTTP OK","source_region":"01","source_registered_country":"SG","source_canonical":"***************","packet_type":"log","response_code":200,"control_id":"ada0ec23-c8c3-45a9-9372-34d8a19c4af9","plugin_version":"0.10","log":"*************** - - [01/Feb/2024:15:39:46 -0500] \\"POST //xmlrpc.php HTTP/1.1\\" 200 472 \\"-\\" \\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36\\"","event_description":"Standard response for successful HTTP requests. The actual response will depend on the request method used. In a GET request, the response will contain an entity corresponding to the requested resource. In a POST request the response will contain an entity describing or containing the result of the action.","destination_name":"gsaze204","request_method":"POST","needs_enrichment":true,"source_organisation":"cloudflare  inc.","received_from":"gsaze204","application_protocol":"HTTP/1.1","source_address":"***************","x_att_tenant_subdomain":"guest-services","x_att_tenantid":"ada0ec23-c8c3-45a9-9372-34d8a19c4af9","suppressed":"false","source_name":"***************","plugin_device_type":"Web Server","destination_canonical":"gsaze204","source_city":"Singapore","rep_dev_canonical":"50eac293-b665-418e-afd1-714dd8866199","plugin_device":"apache","timestamp_to_storage":"1706820008211","highlight_fields":["request_url","response_code","request_method","bytes_out"],"time_zone":"-0500","request_user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36","needs_internal_enrichment":false,"plugin":"Apache","source_country":"SG","timestamp_arrived":"1706820008211"},{"was_fuzzied":false,"http_hostname":"xmlrpc.php","timestamp_occured":"1706819995000","plugin_device_version":"1","uuid":"bf615d5b-90f3-9313-cc49-8da10b68f618","rep_device_hostname":"50eac293-b665-418e-afd1-714dd8866199","rep_device_asset_id":"50eac293-b665-418e-afd1-714dd8866199","used_hint":true,"tag":"apache","was_guessed":false,"timestamp_received":"1706820008166","source_latitude":"1.287950","time_offset":"Z","request_url":"//xmlrpc.php","sensor_uuid":"438b255c-35bd-4de2-b1ab-42990483e8a2","bytes_out":472,"source_longitude":"103.851780","transient":false,"event_name":"HTTP OK","source_region":"01","source_registered_country":"SG","source_canonical":"***************","packet_type":"log","response_code":200,"control_id":"ada0ec23-c8c3-45a9-9372-34d8a19c4af9","plugin_version":"0.10","log":"*************** - - [01/Feb/2024:15:39:55 -0500] \\"POST //xmlrpc.php HTTP/1.1\\" 200 472 \\"-\\" \\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36\\"","event_description":"Standard response for successful HTTP requests. The actual response will depend on the request method used. In a GET request, the response will contain an entity corresponding to the requested resource. In a POST request the response will contain an entity describing or containing the result of the action.","destination_name":"gsaze204","request_method":"POST","needs_enrichment":true,"source_organisation":"cloudflare  inc.","received_from":"gsaze204","application_protocol":"HTTP/1.1","source_address":"***************","x_att_tenant_subdomain":"guest-services","x_att_tenantid":"ada0ec23-c8c3-45a9-9372-34d8a19c4af9","suppressed":"false","source_name":"***************","plugin_device_type":"Web Server","destination_canonical":"gsaze204","source_city":"Singapore","rep_dev_canonical":"50eac293-b665-418e-afd1-714dd8866199","plugin_device":"apache","timestamp_to_storage":"1706820008224","highlight_fields":["request_url","response_code","request_method","bytes_out"],"time_zone":"-0500","request_user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36","needs_internal_enrichment":false,"plugin":"Apache","source_country":"SG","timestamp_arrived":"1706820008224"},{"was_fuzzied":false,"http_hostname":"xmlrpc.php","timestamp_occured":"1706819993000","plugin_device_version":"1","uuid":"7e6a7872-dd93-2b2f-72fa-359278062d0a","rep_device_hostname":"50eac293-b665-418e-afd1-714dd8866199","rep_device_asset_id":"50eac293-b665-418e-afd1-714dd8866199","used_hint":true,"tag":"apache","was_guessed":false,"timestamp_received":"1706820008175","source_latitude":"1.287950","time_offset":"Z","request_url":"//xmlrpc.php","sensor_uuid":"438b255c-35bd-4de2-b1ab-42990483e8a2","bytes_out":472,"source_longitude":"103.851780","transient":false,"event_name":"HTTP OK","source_region":"01","source_registered_country":"SG","source_canonical":"***************","packet_type":"log","response_code":200,"control_id":"ada0ec23-c8c3-45a9-9372-34d8a19c4af9","plugin_version":"0.10","log":"*************** - - [01/Feb/2024:15:39:53 -0500] \\"POST //xmlrpc.php HTTP/1.1\\" 200 472 \\"-\\" \\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36\\"","event_description":"Standard response for successful HTTP requests. The actual response will depend on the request method used. In a GET request, the response will contain an entity corresponding to the requested resource. In a POST request the response will contain an entity describing or containing the result of the action.","destination_name":"gsaze204","request_method":"POST","needs_enrichment":true,"source_organisation":"cloudflare  inc.","received_from":"gsaze204","application_protocol":"HTTP/1.1","source_address":"***************","x_att_tenant_subdomain":"guest-services","x_att_tenantid":"ada0ec23-c8c3-45a9-9372-34d8a19c4af9","suppressed":"false","source_name":"***************","plugin_device_type":"Web Server","destination_canonical":"gsaze204","source_city":"Singapore","rep_dev_canonical":"50eac293-b665-418e-afd1-714dd8866199","plugin_device":"apache","timestamp_to_storage":"1706820008252","highlight_fields":["request_url","response_code","request_method","bytes_out"],"time_zone":"-0500","request_user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36","needs_internal_enrichment":false,"plugin":"Apache","source_country":"SG","timestamp_arrived":"1706820008251"},{"was_fuzzied":false,"http_hostname":"xmlrpc.php","timestamp_occured":"1706819990000","plugin_device_version":"1","uuid":"ff067bd5-cd5d-1eef-5648-2c9cafa0b696","rep_device_hostname":"50eac293-b665-418e-afd1-714dd8866199","rep_device_asset_id":"50eac293-b665-418e-afd1-714dd8866199","used_hint":true,"tag":"apache","was_guessed":false,"timestamp_received":"1706820008168","source_latitude":"1.287950","time_offset":"Z","request_url":"//xmlrpc.php","sensor_uuid":"438b255c-35bd-4de2-b1ab-42990483e8a2","bytes_out":472,"source_longitude":"103.851780","transient":false,"event_name":"HTTP OK","source_region":"01","source_registered_country":"SG","source_canonical":"***************","packet_type":"log","response_code":200,"control_id":"ada0ec23-c8c3-45a9-9372-34d8a19c4af9","plugin_version":"0.10","log":"*************** - - [01/Feb/2024:15:39:50 -0500] \\"POST //xmlrpc.php HTTP/1.1\\" 200 472 \\"-\\" \\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36\\"","event_description":"Standard response for successful HTTP requests. The actual response will depend on the request method used. In a GET request, the response will contain an entity corresponding to the requested resource. In a POST request the response will contain an entity describing or containing the result of the action.","destination_name":"gsaze204","request_method":"POST","needs_enrichment":true,"source_organisation":"cloudflare  inc.","received_from":"gsaze204","application_protocol":"HTTP/1.1","source_address":"***************","x_att_tenant_subdomain":"guest-services","x_att_tenantid":"ada0ec23-c8c3-45a9-9372-34d8a19c4af9","suppressed":"false","source_name":"***************","plugin_device_type":"Web Server","destination_canonical":"gsaze204","source_city":"Singapore","rep_dev_canonical":"50eac293-b665-418e-afd1-714dd8866199","plugin_device":"apache","timestamp_to_storage":"1706820008266","highlight_fields":["request_url","response_code","request_method","bytes_out"],"time_zone":"-0500","request_user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36","needs_internal_enrichment":false,"plugin":"Apache","source_country":"SG","timestamp_arrived":"1706820008266"}],"assets":[],"id":"02f25da7-e3bd-6806-3a9f-6783ccd910de","operation":"alarm-create","licenseId":"ada0ec23-c8c3-45a9-9372-34d8a19c4af9","msspId":"ad3be88b-17fb-42a8-b4b9-a582575d5a40","usmc-region":"us-east-1"}',
            "organizationId": "144738b9-031d-4793-9f16-1f136effd09d",
            "conversationId": f"convId_{random.randint(0,100)}",
            "messageId": f"msgId_{random.randint(0,100)}",
            "formatFinalAnswer": False,
        },
    ),
)

print(resp.content)

with open("procedure_test.txt", "w") as f:
    f.write(json.dumps(json.loads(resp.content), indent=2))

# "input": "{'tags': [], 'alerts': [{'title': 'MediaArena' unwanted software was detected during a scheduled scan', 'status': 'Resolved', 'alertId': 'da52451e4e-ac14-43e7-9864- f62e1da5ff24_1', 'devices': [{'tags': [], 'osBuild': 19045, 'version': '22H2', 'frstSeen': '2022-09-09T19:01:00.677Z', 'riskScore': 'None', 'osPlatform': 'Windows10', 'vmMetadata': None, 'aadDeviceId': 'dfae4573-9e28-49d3-a419- ecac0de087fc', 'osProcessor': 'x64', 'healthStatus': 'Active', 'deviceDnsName': 'wh-2612lt.int.hopeservices.org', 'loggedOnUsers': [{'domainName': 'HOPE', 'accountName': 'knakahama'}], 'mdatpDeviceId': '40d2276ee066b03da622a501561539a49fb89f36', 'rbacGroupName': None, 'defenderAvStatus': 'Updated', 'onboardingStatus': 'Onboarded'}], 'category': 'DefenseEvasion', 'entities': [{'sha1': '33c02d70abb2f1f12a79cfd780d875a94e7fe877', 'sha256': 'e248b01e3ccde76b4d8e8077d4fcb4d0b70e5200bf4e738b45a0bd28fbc2cae6', 'verdict': 'Malicious', 'deviceId': '40d2276ee066b03da622a501561539a49fb89f36', 'fleName': 'PDFpower (1).exe', 'flePath': 'C:\\Users\\<USER>\\Downloads', 'entityType': 'File', 'detectionStatus': 'Detected', 'remediationStatus': 'Blocked', 'evidenceCreationTime': '2023-05-08T01:51:55.84Z', 'remediationStatusDetails': 'Entity was pre-remediated by Windows Defender'}], 'severity': 'Informational', 'actorName': None, 'assignedTo': 'Automation', 'detectorId': '910e98ad-0eb4-480b-8aa6-841e9ecad15e', 'incidentId': 5119, 'description': 'Potentially unwanted software is a category of applications that install and perform undesirable activity without adequate user consent. These applications are not necessarily malicious, but their behaviors often negatively impact the computing experience, even appearing to invade user privacy. Many of these applications display advertising, modify browser settings, and install bundled software.', 'creationTime': '2023-05-08T01:51:55.6556557Z', 'lastActivity': '2023-05-08T01:50:14.7525415Z', 'resolvedTime': '2023-05-08T02:08:39.9587147Z', 'determination': None, 'frstActivity': '2023-05-08T01:50:14.7525415Z', 'serviceSource': 'MicrosoftDefenderForEndpoint', 'classifcation': None, 'detectionSource': 'WindowsDefenderAv', 'investigationId': 84, 'lastUpdatedTime': '2023-05-08T02:08:40.0766667Z', 'mitreTechniques': [], 'providerAlertId': '52451e4e-ac14-43e7-9864-f62e1da5ff24_1', 'threatFamilyName': 'MediaArena', 'investigationState': 'SuccessfullyRemediated'}], 'status': 'Resolved', 'comments': [], 'severity': 'Informational', 'assignedTo': None, 'incidentId': 5119, 'createdTime': '2023-05-08T01:51:56.0166667Z', 'incidentUri': 'https://security.microsoft.com/incidents/5119? tid=a6a9dcec-3d59-40bf-9076-27a1ad4fe1f9', 'incidentName': 'MediaArena unwanted software was detected during a scheduled scan on one endpoint', 'determination': 'NotAvailable', 'classifcation': 'Unknown', 'lastUpdateTime': '2023-05-08T02:08:40.0766667Z', 'redirectIncidentId': None}]",
