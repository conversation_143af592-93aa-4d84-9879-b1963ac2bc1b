import time
import dotenv

dotenv.load_dotenv()

nvd_spec = {
    "name": "NVD Analyzer",
    "description": """useful for getting common vulnerabily information about specific CPEs, CWEs, Device Names, Date Ranges and CVEIDs using the NVD (National Vulnerability) api.
        Input should be a fully formed question including a specific ID such as a CWE, CVE or CPE. 
        CORRECT Examples are: 'Can you tell me the vulnerabilities associated with CWE-89?' or 'What do we know about CVE-2023-23397?' 
        INCORRECT Examples are: 'Can you tell me about vulnerabiilities?'
        All responses should be considered sourced from [source: "NVD API"]""",
    "urls": [
        {
            "base_url": "https://services.nvd.nist.gov",
            "method": "GET",
            "endpoints": [
                {
                    "path": "/rest/json/cves/2.0?cpeName=[value]",
                    "definition": 'This endpoint should retrieve all Common Vulnerabilities (CVE) associated with a specific CPE identified by its CPE name. A CPE Name is a string of characters comprised of 13 colon separated values that describe a product. In CPEv2.3 the first two values are always \“cpe\” and \“2.3\”. The 11 values that follow are referred to as the CPE components. When filtering by cpeName the part, vendor, product, and version components are REQUIRED to contain values other than "*".',
                    "arguments": {
                        "CPE Name": {
                            "examples": [
                                "cpe:2.3:o:microsoft:windows_10:1607:*:*:*:*:*:*:*",
                                "cpe:2.3:a:microsoft:internet_explorer:8.0.6001:beta:*:*:*:*:*:*",
                            ]
                        }
                    },
                },
                {
                    "path": "/rest/json/cves/2.0?cveId=[value]",
                    "definition": "This endpoint returns information about a specific vulnerability identified by its unique Common Vulnerabilities and Exposures identifier (the CVE ID).",
                    "arguments": {
                        "CVE ID": {"examples": ["CVE-2019-1010218", "CVE-2022-22954"]}
                    },
                },
                {
                    "path": "/rest/json/cves/2.0?cvssV2Metrics=[value]",
                    "definition": "This endpoint returns only the CVEs that match the provided CVSSv2 vector string. Either full or partial vector strings may be used.",
                    "arguments": {
                        "CVSS V2 Vector string": {
                            "examples": [
                                "AV:N/AC:H/Au:N/C:C/I:C/A:C",
                                "AV:L/AC:H/Au:M/C:N/I:N/A:N",
                            ]
                        }
                    },
                },
                {
                    "path": "/rest/json/cves/2.0?cvssV3Metrics=[value]",
                    "definition": "This endpoint returns only the CVEs that match the provided CVSS V3 vector string. Either full or partial vector strings may be used.",
                    "arguments": {
                        "CVSS V3 vector string": {
                            "examples": ["AV:L/AC:L/PR:L/UI:R/S:U/C:N/I:L/A:L"]
                        }
                    },
                },
                {
                    "path": "/rest/json/cves/2.0?cweId=[value]",
                    "definition": "This endpoint returns only the CVE that include a weakness identified by Common Weakness Enumeration using the provided CWE ID.",
                    "arguments": {"CWE ID": {"examples": ["CWE-287", "CWE-89"]}},
                },
                {
                    "path": "/rest/json/cves/2.0?keywordSearch=[value]",
                    "definition": "This endpoint returns only the CVEs where a word or phrase is found in the current description.",
                    "arguments": {"Keywords": {"examples": ["Microsoft", "Debian"]}},
                },
                {
                    "path": "/rest/json/cves/2.0/?pubStartDate=[value 1]&pubEndDate=[value 2]",
                    "definition": 'This endpoint return only the CVEs that were added to the NVD (i.e., published) during the specified period defaulting to GMT. If filtering by the published date, both pubStartDate and pubEndDate are REQUIRED. The maximum allowable range when using any date range parameters is 120 consecutive days. Values must be entered in the extended ISO-8061 date/time format: [YYYY][“-”][MM][“-”][DD][“T”][HH][“:”][MM][“:”][SS][Z]. The "T" is a literal to separate the date from the time. The Z indicates an optional offset-from-UTC. ',
                    "arguments": {
                        "Start Date": {
                            "examples": [
                                "2021-08-04T00:00:00.000",
                                "2021-10-22T00:00:00.000",
                            ]
                        },
                        "End Date": {
                            "examples": [
                                "2021-08-04T00:00:00.000",
                                "2021-10-22T00:00:00.000",
                            ]
                        },
                    },
                },
            ],
            "select_endpoint_prompt": """
                You are an api composing assistant that composes an API URL by following the steps below:

                Step 1) Analyze the input and identify the relevant entity. 
                Here are a list of options and examples- 
                {examples}
                Step 2) Pick the correct endpoint from the list of options below based on the intent of the user and the description of the end-point. Options:
                {options}
                \nStep 3) Compose the API by replacing [value] in the correct end-point with the actual value of the entity identified.

                Here are some examples:

                Example 1)
                [Input] Tell me all the vulnerabilities that were published between 1st Jan 2021 and 14th Jan 2021 end of day in Easter time (GMT -5 hours)? 
                [Output] /rest/json/cves/2.0/?pubStartDate=2020-01-01T00:00:00.000-05:00&pubEndDate=2020-01-14T23:59:59.999-05:00

                Example 2) 
                [Input] What vulnerabilities were published between the 4th August 2021 and 22nd October 2021 GMT? 
                [Output] /rest/json/cves/2.0/?pubStartDate=2021-08-04T00:00:00.000&pubEndDate=2021-10-22T00:00:00.000

                Example 3)
                [Input] What do we know about vulnerabilities associated with Microsoft Windows 10 Version 1607?
                [Output] /rest/json/cves/2.0?cpeName=2.3:o:microsoft:windows_10_1607:-:*:*:*:*:*:*
                
                Generate an [Output] for the submitted [Input] using the steps mentioned. MAKE SURE to include only the ENDPOINT in the [Output]
                """,
            "select_endpoint_validation_pattern": "\/rest\/json\/cves\/2.0\?[0-9a-zA-Z_\/\-:&=\.\*]*",
            "interpret_response_prompt": """
                I want you to act as a cybersecurity analyst that summarises an input json string 
                recieved from an endpoint {endpoint} of the National Vulnerability Database 
                and returns a detailed list of information including all links for further information.
                Do not include explanations of the JSON but rather focus on the inferences drawn from it. 
                The json string is the information on common vulnerabilities.
                Try answering questions such as what are the vulnerabilities, what is their description, what associated threat actors are,
                what devices are typically affected and its description.
                """,
        }
    ],
}
begin = time.perf_counter()
from llm_api.plugins.api_plugin import ApiPlugin

end = time.perf_counter()
print(end - begin)

import uuid
from llm_api.callbacks import LoggingCallbackHandler

lcb = LoggingCallbackHandler(message_id="1")


nvd = ApiPlugin(
    plan_id=uuid.uuid4(),
    **nvd_spec,
)

print(nvd.json(indent=2, exclude_none=True))
