from datetime import datetime, timedelta

blogs_spec = {
    "name": "Blogs Tool",
    "description": f"""useful for when you need to answer questions about security news.
        Input should be a STRING of a list with a fully formed question along with all the IMPORTANT keywords in the question AND any dates inferred from the question.
        These keywords are usually PROPER NOUNS. Use the date specified if explicitely mentioned, otherwise infer the required date
        from the question AND that today is {datetime.now().strftime("%Y_%m_%d %A")}.
        ACTION INPUT MUST be in the format below:
        "This is a question about yesterday's news on TopicA, TopicB, TopicC" --- "TopicA","TopicB","TopicC","{(datetime.now() - timedelta(days=1)).strftime('%Y_%m_%d')}"
        EXAMPLES:
        - "What was the cybersecurity news from a day before yesterday?" --- "cybersecurity, news, {(datetime.now() - timedelta(days=2)).strftime('%Y_%m_%d')}"
        - "What happened in cybersecurity for finance last week?" ---  "cybersecurity, finance, {(datetime.now() - timedelta(days=1)).strftime('%Y_%m_%d')}, {(datetime.now() - timedelta(days=2)).strftime('%Y_%m_%d')}, {(datetime.now() - timedelta(days=3)).strftime('%Y_%m_%d')}, {(datetime.now() - timedelta(days=4)).strftime('%Y_%m_%d')}, {(datetime.now() - timedelta(days=5)).strftime('%Y_%m_%d')}"
        - "Tell about any incidents that happened 3 days ago?" --- "incidents, {(datetime.now() - timedelta(days=3)).strftime('%Y_%m_%d')}"
        """,
    "chain_type": "stuff",
}
