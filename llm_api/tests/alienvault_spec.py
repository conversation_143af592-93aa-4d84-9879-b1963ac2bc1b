import dotenv
import os

dotenv.load_dotenv()

alienvault_spec = {
    "name": "AlienVault Analyzer",
    "description": """
        useful for analyzing specific domains, ip addresses, host-names or CVEs using the alienvault otx api.
        Input should be a fully formed question. 
        CORRECT Examples are: 'Can you analyze example.com?' or 'Is the IP Address 67.45.123.345 considered malicious?'
        WRONG Examples are: 'What is an Indicator of Compromise (IOC)?'
        All responses should be considered sourced from [source: "AlienVault API"]
    """,
    "urls": [
        {
            "base_url": "https://otx.alienvault.com",
            "method": "GET",
            "credentials": {
                "type": 1,
                "data": {
                    "header": "X-OTX-API-KEY",
                    "key": os.environ["ALIENVAULT_API_KEY"],
                },
            },
            "endpoints": [
                {
                    "path": "/api/v1/indicators/IPv4/[value]/general",
                    "definition": "This endpoint should retrieve information related to the IPv4 indicator specified by the IP address.",
                    "arguments": {
                        "IPv4 Address": {"examples": ["*******", "**********"]}
                    },
                },
                {
                    "path": "/api/v1/indicators/IPv6/[value]/general",
                    "definition": "This endpoint should retrieve information related to the IPv6 indicator specified by the IP address.",
                    "arguments": {
                        "IPv6 Address": {
                            "examples": [
                                "2001:db8:3333:4444:5555:6666:7777:8888",
                                "2001:db8::",
                                "2001:0db8:0001:0000:0000:0ab9:C0A8:0102",
                            ]
                        }
                    },
                },
                {
                    "path": "/api/v1/indicators/domain/[value]/general",
                    "definition": "This endpoint should retrieve information related to the domain indicator specified by the domain name.",
                    "arguments": {
                        "Domain Name": {"examples": ["example.com", "spywaresite.info"]}
                    },
                },
                {
                    "path": "/api/v1/indicators/hostname/[value]/general",
                    "definition": "This endpoint should retrieve information related to the hostname indicator specified by the hostname.",
                    "arguments": {
                        "Host Name": {
                            "examples": [
                                "otx.alienvault.com",
                                "bad-guys.no-ip.org",
                                "alpha.beta.google.co.uk",
                            ]
                        }
                    },
                },
                {
                    "path": "/api/v1/indicators/file/[value]/general",
                    "definition": "This endpoint should retrieve information related to the file indicator specified by the file hash.",
                    "arguments": {
                        "File Hash": {
                            "examples": ["6c5360d41bd2b14b1565f5b18e5c203cf512e493"]
                        }
                    },
                },
                {
                    "path": "/api/v1/indicators/url/[value]/general",
                    "definition": "This endpoint should retrieve information related to the URL indicator specified by the URL.",
                    "arguments": {
                        "URL": {
                            "examples": [
                                "http://www.fotoidea.com/sport/4x4_san_ponso/slides/IMG_0068.html"
                            ]
                        }
                    },
                },
                {
                    "path": "/api/v1/indicators/cve/[value]/general",
                    "definition": "This endpoint should retrieve information related to the Common Vulnerabilities and Exposures (CVE) indicator specified by the CVE identifier.",
                    "arguments": {"CVE": {"examples": ["CVE-2014-0160"]}},
                },
            ],
            "select_endpoint_prompt": """
                You are an api composing assistant that composes an API URL by following the steps below:

                Step 1) Analyze the input and identify the relevant entity. 
                Here are a list of options and examples- 
                {examples}
                Step 2) Pick the correct endpoint from the list of options below based on the intent of the user and the description of the end-point. Options:
                {options}
                \nStep 3) Compose the API by replacing [value] in the correct end-point with the actual value of the entity identified.

                Here are some examples:

                Example 1)
                [Input] Analyze bricklayer.ai
                [Output] /api/v1/indicators/domain/bricklayer.ai/general

                Example 2) 
                [Input] Is ************* malicious? 
                [Output] /api/v1/indicators/IPv4/*************/general

                Example 3)
                [Input] Does 2a03:2880:10:1f02:face:b00c::25 have any issues?
                [Output] /api/v1/indicators/IPv6/2a03:2880:10:1f02:face:b00c::25/general
                
                Generate an [Output] for the submitted [Input] using the steps mentioned. MAKE SURE to include only the ENDPOINT in the [Output]
            """,
            "select_endpoint_validation_pattern": "\/api\/[0-9a-zA-Z\/\-\.]*",
            "interpret_response_prompt": """
                I want you to act as a cybersecurity analyst that summarises an input json string 
                recieved from an endpoint {endpoint} of the AlienVault Open Threat Exchange Platform 
                and returns a detailed list of information including all links for further information.
                Do not include explanations of the JSON but rather focus on the inferences drawn from it. 
                The json string is the information on a specific Indicator of Compromise (IOC)
                Try answering questions such as whether the Indicator seems malicious, what associated threat actors are,
                what devices are typically affected and its description
            """,
        }
    ],
}
import time

begin = time.perf_counter()
from llm_api.plugins.api_plugin import ApiPlugin

end = time.perf_counter()
print(end - begin)

import uuid
from llm_api.callbacks import LoggingCallbackHandler

lcb = LoggingCallbackHandler(message_id="1")


alienvault = ApiPlugin(
    plan_id=uuid.uuid4(),
    **alienvault_spec,
)

print(alienvault.json(indent=2))
