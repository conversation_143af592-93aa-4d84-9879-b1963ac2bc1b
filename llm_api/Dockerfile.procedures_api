FROM bricklayerai/python:1.0

RUN apt-get update && apt-get upgrade -y

WORKDIR /code

COPY ./requirements/common.txt .
RUN python -m pip install --no-cache-dir -r common.txt

COPY ./requirements/procedures-api.txt .
RUN python -m pip install --no-cache-dir -r common.txt -r procedures-api.txt

COPY ./llm_api llm_api/
COPY ./README.md README.md

EXPOSE 9000

CMD ["python", "-c", "from llm_api.cli.procedures import procedure_engine; procedure_engine(['api']);"]
