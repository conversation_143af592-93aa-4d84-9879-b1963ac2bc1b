FROM python:3.11

WORKDIR /deps

COPY ./requirements/common.txt ./requirements.txt

RUN pip install -r requirements.txt

COPY ./llm_api /deps/llm_api

RUN rm -rf /deps/bricklayer_api

WORKDIR /code

RUN apt update && apt install -y libpq-dev gdal-bin libgdal-dev

ENV GDAL_LIBRARY_PATH=/usr/lib/libgdal.so


# Please remove below once pantheon is available in Python Private Index
COPY ./llm_api/bricklayer_api/blai_pantheon-1.0.1.dev1+g5c7b93c.d20250717-py3-none-any.whl ./blai_pantheon-1.0.1.dev1+g5c7b93c.d20250717-py3-none-any.whl


COPY ./llm_api/bricklayer_api/requirements/common.txt ./requirements/common.txt
COPY ./llm_api/bricklayer_api/requirements/dev.txt ./requirements/dev.txt



RUN pip install -r /deps/requirements.txt -r requirements/dev.txt
RUN pip install structlog

COPY ./llm_api/bricklayer_api .

ENV PYTHONPATH="/deps"

EXPOSE 8001

CMD ["python3", "./manage.py", "runserver", "0:8001"]
