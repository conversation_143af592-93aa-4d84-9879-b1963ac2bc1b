from setuptools import setup, find_packages

setup(
    name='llm-api',
    version='0.1.0',
    description='LLM project for Bricklayer AI',
    include_package_data=True,
    install_requires=[],  # Dependencies are handled via requirements.txt
    entry_points={
        'console_scripts': [
            'llm=llm_api.cli.server:cli',
            'procedure_engine=llm_api.cli.procedures:procedure_engine',
        ],
    },
)
