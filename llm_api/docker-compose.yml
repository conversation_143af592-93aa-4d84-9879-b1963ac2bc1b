networks:
  llm-network:
    driver: bridge

services:

  llm_api:
    build:
      context: .
      dockerfile: Dockerfile.llm_api
    networks:
      - llm-network
    ports:
      - "9000:9000"
    env_file:
      - .env.docker
    volumes:
      - .:/code
    environment:
      LLM__OPENSEARCH__HOST: opensearch-node1
      REDIS_URL: redis
      AUTOSTART_WEBSERVER: 1
      DOCKER_HOST: "tcp://dind:2376"
    restart: always
    depends_on:
      - opensearch-node1
      - redis
      - dind

  dind:
    build:
      context: .
      dockerfile: Dockerfile.dind
    privileged: true
    environment:
      DOCKER_TLS_CERTDIR: "dind-certs/"
    volumes:
      - dind-storage:/var/lib/docker
      - /sys/fs/cgroup:/sys/fs/cgroup
    expose:
      - "2376"
    networks:
      - llm-network
    restart: always
    healthcheck:
      test: [ "CMD", "docker", "info" ]
      interval: 10s
      timeout: 10s
      retries: 5

  procedures_api:
    build:
      context: .
      dockerfile: Dockerfile.procedures_api
    networks:
      - llm-network
    ports:
      - "9001:9001"
    env_file:
      - .env.docker
    environment:
      AUTOSTART_WEBSERVER: 1
    restart: always
    depends_on:
      - llm_api
      - redis
      - procedures_workers
    volumes:
      - ./:/code

  procedures_workers:
    build:
      context: .
      dockerfile: Dockerfile.procedures_workers
    networks:
      - llm-network
    env_file:
      - .env.docker
    environment:
      AUTOSTART_WORKERS: 1
    restart: always
    depends_on:
      - redis
    volumes:
      - ./:/code

  bricklayer_api:
    build:
      context: .
      dockerfile: Dockerfile.bricklayer_api.local
    networks:
      - llm-network
    ports:
      - 8001:8001
    env_file:
      - .env.docker
    environment:
      DJANGO_SETTINGS_MODULE: bricklayer_api.settings.local
      POSTGRES_HOST: db
      REDIS_HOST: redis
    depends_on:
      - db
      - redis
    volumes:
      - ./llm_api/bricklayer_api/:/code

  bricklayer_worker:
    build:
      context: .
      dockerfile: Dockerfile.bricklayer_api.local
    networks:
      - llm-network
    env_file:
      - .env.docker
    environment:
      DJANGO_SETTINGS_MODULE: bricklayer_api.settings.local
      POSTGRES_HOST: db
      REDIS_HOST: redis
    depends_on:
      - db
      - redis
    volumes:
      - ./llm_api/bricklayer_api/:/code
    command: ["celery", "-A", "bricklayer_api", "worker", "-l", "INFO"]

  db:
    image: pgvector/pgvector:pg17
    restart: always
    environment:
      POSTGRES_DB: prisma
      POSTGRES_PASSWORD: example
    ports:
      - 5432:5432
    volumes:
      - ./db-init:/docker-entrypoint-initdb.d
      - ./postgres_data:/var/lib/postgresql/data
    networks:
      - llm-network

  redis:
    image: redis
    command: [ "redis-server", "--save", "10", "1" ]
    volumes:
      - ./redis_volume:/data
    expose:
      - "6379"
    ports:
      - 6379:6379
    networks:
      - llm-network

  opensearch-node1:
    # This is also the hostname of the container within the Docker network (i.e. https://opensearch-node1/)
    image: opensearchproject/opensearch:latest # Specifying the latest available image - modify if you want a specific version
    container_name: opensearch-node1
    environment:
      bootstrap.system_call_filter: false
      cluster.initial_cluster_manager_nodes: opensearch-node1,opensearch-node2 # Nodes eligible to serve as cluster manager
      cluster.name: opensearch-cluster # Name the cluster
      discovery.seed_hosts: opensearch-node1,opensearch-node2 # Nodes to look for when discovering the cluster
      node.name: opensearch-node1 # Name the node that will run in this container
      OPENSEARCH_INITIAL_ADMIN_PASSWORD: "Str0ngOpenS3archP@ss"
      OPENSEARCH_JAVA_OPTS: "-Xms512m -Xmx512m" # Set min and max JVM heap sizes to at least 50% of system RAM
    volumes:
      - opensearch-data1:/usr/share/opensearch/data # Creates volume called opensearch-data1 and mounts it to the container
    ports:
      - 9200:9200 # REST API
      - 9600:9600 # Performance Analyzer
    networks:
      - llm-network # All of the containers will join the same Docker bridge network

  opensearch-node2:
    image: opensearchproject/opensearch:latest # This should be the same image used for opensearch-node1 to avoid issues
    container_name: opensearch-node2
    environment:
      bootstrap.system_call_filter: false
      cluster.initial_cluster_manager_nodes: "opensearch-node1,opensearch-node2"
      cluster.name: opensearch-cluster
      discovery.seed_hosts: "opensearch-node1,opensearch-node2"
      node.name: opensearch-node2
      OPENSEARCH_INITIAL_ADMIN_PASSWORD: "Str0ngOpenS3archP@ss"
      OPENSEARCH_JAVA_OPTS: "-Xms512m -Xmx512m"
    volumes:
      - opensearch-data2:/usr/share/opensearch/data
    networks:
      - llm-network

  opensearch-dashboards:
    image: opensearchproject/opensearch-dashboards:latest # Make sure the version of opensearch-dashboards matches the version of opensearch installed on other nodes
    container_name: opensearch-dashboards
    ports:
      - 5601:5601 # Map host port 5601 to container port 5601
    expose:
      - "5601" # Expose port 5601 for web access to OpenSearch Dashboards
    environment:
      OPENSEARCH_HOSTS: '["https://opensearch-node1:9200","https://opensearch-node2:9200"]' # Define the OpenSearch nodes that OpenSearch Dashboards will query
    # network_mode: host
    networks:
      - llm-network

volumes:
  index_data:
    driver: local
  backups:
    driver: local
  opensearch-data1:
  opensearch-data2:
  dind-storage:
    driver: local
