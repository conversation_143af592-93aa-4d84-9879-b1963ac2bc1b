{"schema": {"definitions": {"Parameter": {"type": "object", "properties": {"name": {"type": "string", "title": "Name"}, "type": {"type": "string", "enum": ["str", "int", "bool", "float", "list", "dict", "file", "object"], "default": "str", "title": "Type"}, "required": {"type": "boolean", "default": false, "title": "Required"}, "location": {"type": "string", "enum": ["query", "header", "body", "path", "json"], "default": "query", "title": "Location"}, "default": {"type": ["string", "number", "boolean", "array", "object", "null"], "title": "Default Value", "description": "Default value of the parameter if none is provided"}, "sub_parameters": {"type": "array", "items": {"$ref": "#/definitions/Parameter"}, "default": [], "title": "Sub Parameters", "description": "Nested parameters if any, useful for complex data structures"}, "prefix": {"type": ["string", "null"], "title": "Prefix"}, "suffix": {"type": ["string", "null"], "title": "Suffix"}, "definition": {"type": ["string", "null"], "title": "Definition", "description": "A brief definition or description of the parameter's purpose, used by the system when making requests."}, "fieldsList": {"type": ["array", "null"], "items": {"type": "string"}, "title": "Fields List"}}}}, "type": "object", "properties": {"name": {"type": "string", "title": "Name", "default": ""}, "description": {"type": "string", "title": "Description", "default": "", "description": "A brief description of the plugin's functionality, explaining why it is useful and what tasks it can perform. This information helps the system understand the plugin's purpose and how to utilize it effectively."}, "auth_type": {"enum": [null, "Basic", "Bearer", "api_key", "X-Api-Key", "OAuth", "token"], "enumNames": ["No auth", "Basic", "Bearer", "API Key", "X-Api-Key", "OAuth", "token"], "default": null, "title": "Authentication Type"}, "credentials": {"type": "object", "properties": {"data": {"type": "object", "properties": {}}}}, "spec": {"type": "object", "properties": {"base_url": {"type": "string", "title": "Base URL", "format": "uri", "default": "", "description": "The base URL is the root URL for the API. It will be concatenated with each endpoint path to create the full path of the endpoint request. For example, if the base URL is `https://bricklayer.ai` and the endpoint path is `/api/v1/resource`, the full request URL will be `https://bricklayer.ai/api/v1/resource`.\n"}, "endpoints": {"type": "array", "minItems": 1, "items": {"type": "object", "properties": {"description": {"type": ["string", "null"], "title": "Description", "description": "A brief description of what this endpoint does and when it should be used. This should answer questions like: What functionality does this endpoint provide? In what scenarios should this endpoint be called?"}, "path": {"type": "string", "title": "Path"}, "method": {"type": "string", "enum": ["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS"], "default": "GET", "title": "Method", "description": "HTTP method to use for the endpoint"}, "content_type": {"type": "string", "enum": ["application/json", "application/x-www-form-urlencoded", "multipart/form-data"], "default": "application/json", "title": "Content Type", "description": "The content type for the request body"}, "parameters": {"type": "array", "items": {"$ref": "#/definitions/Parameter"}, "default": [], "title": "Parameters"}, "headers": {"type": "object", "properties": {}, "default": {}, "title": "Headers", "description": "Custom headers for the request"}, "auth_required": {"type": "boolean", "default": false, "title": "Auth Required"}, "auth_location": {"type": "string", "enum": ["header", "query"], "default": "header", "title": "Auth Location", "description": "Specifies where the authentication information should be included in the request. It can be either in the headers or as a query parameter. If set to `header`, the authentication details will be sent in the request headers; if set to `query`, they will be included as query parameters in the URL.\n"}, "auth_header_name": {"type": ["string", "null"], "title": "Auth Header Name", "description": "The name of the header used for authentication, populated with the value of the API key or token, depending on the authentication type specified."}, "user_agent": {"type": ["string", "null"], "title": "User Agent", "description": "The user agent string to send with the request, identifying the client software making the request."}, "basic_auth_header_name": {"type": ["string", "null"], "title": "Basic Auth Header Name", "description": "The name of the header used for Basic Authentication, populated with a Base64 encoded string of `api_id:api_secret`.\n"}, "api_id_name": {"type": ["string", "null"], "title": "API ID Name", "description": "Specify the API ID parameter name."}, "api_secret_name": {"type": ["string", "null"], "title": "API Secret Name", "description": "Specify the API secret parameter name."}, "requires_follow_up": {"type": ["boolean", "null"], "default": false, "title": "Requires Follow Up", "description": "Indicates whether additional actions are needed to complete the intended action after initiating a request for this API endpoint. For example, if the initial response indicates that the request is still in progress, a \"true\" value for \"requires follow-up\" means Bricklayer will intelligently poll to check the completion status."}}, "title": "Endpoint", "required": ["path", "method", "description"]}, "title": "Endpoints", "description": "List of endpoints provided by the API"}, "oauth_spec": {"type": ["object", "null"], "properties": {"token_url": {"type": "string", "title": "Token URL"}, "request_format": {"type": "string", "enum": ["json", "x-www-form-urlencoded"], "title": "Request Format", "description": "Format in which token request parameters are sent."}, "token_method": {"type": "string", "enum": ["POST", "GET", "PUT", "DELETE"], "title": "Token Method", "description": "HTTP method used to request tokens."}, "parameters": {"type": "object", "additionalProperties": {"type": "object", "properties": {"value": {"type": "string", "title": "Value"}, "required": {"type": "boolean", "title": "Required"}, "location": {"type": "string", "enum": ["body", "query", "path", "header"], "title": "Location"}}, "required": ["value", "required", "location"]}, "title": "OAuth Parameters", "description": "Key-value pairs of OAuth parameters with metadata."}, "headers": {"type": "object", "additionalProperties": {"type": "string"}, "title": "Headers", "description": "Optional headers to include in the token request."}, "token_response_key": {"type": "string", "title": "Token Response Key", "description": "Key to extract the token from the response."}, "grant_type": {"type": "string", "enum": ["authorization_code", "client_credentials", "refresh_token", "urn:ietf:params:oauth:grant-type:jwt-bearer", "proof_key_for_code_exchange"], "title": "Grant Type", "description": "OAuth grant type used for authentication."}, "scope": {"type": ["string", "null"], "title": "<PERSON><PERSON>", "description": "OAuth scope, if applicable."}}, "title": "OAuth Specification", "description": "OAuth-specific parameters for authentication, replacing oauth_parameters."}}, "title": "Specification", "required": ["base_url"], "description": "Overall specification of the API, including base URL, endpoints, and authentication details"}}, "allOf": [{"if": {"properties": {"auth_type": {"const": "Basic"}}}, "then": {"properties": {"credentials": {"properties": {"data": {"type": "object", "properties": {"api_id": {"type": "string", "default": null, "title": "API ID"}, "api_secret": {"type": "string", "default": null, "title": "API Secret"}}, "required": ["api_id", "api_secret"]}}}, "auth_type": {"description": "When using Basic Authentication, the `api_id` and `api_secret` are combined and encoded in Base64 format. This encoded string is then sent in the `Authorization` header of the request. If either of these fields is missing, the request will fail due to authentication errors.\n- `api_id`: **Required**. This is your unique identifier for the API. It is used to identify the user making the request.\n- `api_secret`: **Required**. This is the secret key associated with your API ID. It is used to verify the identity of the user.\n"}}, "required": ["credentials"]}}, {"if": {"properties": {"auth_type": {"const": "Bearer"}}}, "then": {"properties": {"credentials": {"properties": {"data": {"type": "object", "properties": {"auth_provider_x509_cert_url": {"type": "string", "title": "Auth Provider X509 Cert URL"}, "auth_uri": {"type": "string", "title": "Auth URI"}, "client_email": {"type": ["string", "null"], "default": null, "title": "Client Email"}, "client_id": {"type": ["string", "null"], "default": null, "title": "Client ID"}, "client_x509_cert_url": {"type": ["string", "null"], "default": null, "title": "Client X509 Cert URL"}, "private_key": {"type": ["string", "null"], "default": null, "title": "Private Key"}, "private_key_id": {"type": ["string", "null"], "default": null, "title": "Private Key ID"}, "project_id": {"type": ["string", "null"], "default": null, "title": "Project ID"}, "scopes": {"type": "array", "items": {"type": "string"}, "title": "<PERSON><PERSON><PERSON>"}, "token_uri": {"type": "string", "title": "Token URI"}, "type": {"type": "string", "title": "Type"}, "user_to_impersonate": {"type": ["string", "null"], "default": null, "title": "User to Impersonate"}}, "required": ["auth_provider_x509_cert_url", "auth_uri", "scopes", "token_uri", "type"]}}}, "auth_type": ******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************}, "required": ["credentials"]}}, {"if": {"properties": {"auth_type": {"const": "api_key"}}}, "then": {"properties": {"credentials": {"properties": {"data": {"type": "object", "properties": {"api_key": {"type": ["string", "null"], "default": null, "title": "api_key", "description": ""}}}}}, "auth_type": {"description": "API Key Authentication involves sending an API key either in the request headers or as a query parameter. If the API key is not provided, the request will fail."}}, "required": ["credentials"]}}, {"if": {"properties": {"auth_type": {"const": "X-Api-Key"}}}, "then": {"properties": {"credentials": {"properties": {"data": {"type": "object", "properties": {"X-Api-Key": {"type": "string", "title": "X-Api-Key"}}}}}, "auth_type": {"description": "X-API-Key Authentication involves sending an API key either in the request headers or as a query parameter. If the API key is not provided, the request will fail. The API key can be taken from the `X-Api-Key` in the credentials."}}, "required": ["credentials"]}}, {"if": {"properties": {"auth_type": {"const": "OAuth"}}}, "then": {"properties": {"credentials": {"properties": {"data": {"type": "object", "properties": {"client_id": {"type": ["string", "null"], "default": null, "title": "Client ID"}, "client_secret": {"type": ["string", "null"], "default": null, "title": "Client Secret"}, "redirect_uri": {"type": ["string", "null"], "default": null, "title": "Redirect URI"}, "scope": {"type": ["string", "null"], "default": null, "title": "<PERSON><PERSON>"}}}}}, "auth_type": {"description": "OAuth Authentication requires several parameters to obtain an access token. This token is then used in the `Authorization` header for subsequent requests. If any required fields are missing, the authentication process will fail.\n- `client_id`: **Required**. This is the unique identifier for your application. It is used to identify the application making the request.\n- `client_secret`: **Required**. This is the secret key associated with your client ID. It is used to verify the identity of the application.\n- `redirect_uri`: **Required**. This is the URI to which the user will be redirected after authentication. It must match the URI registered with the API provider.\n- `scope`: **Optional**. This defines the level of access that the application is requesting. If not specified, default scopes may be used.\n"}}, "required": ["credentials"]}}, {"if": {"properties": {"auth_type": {"const": "token"}}}, "then": {"properties": {"credentials": {"properties": {"data": {"type": "object", "properties": {"token": {"type": "string", "title": "Token"}}, "required": ["token"]}}}, "auth_type": {"description": "Token Authentication uses a simple token that is sent in the `Authorization` header. If the token is not provided, the request will fail.\n- `token`: **Required**. This is the token that grants access to the API. It will be included in the `Authorization` header as `Bearer {token}`.\n"}}, "required": ["credentials"]}}, {"if": {"properties": {"auth_type": {"const": null}}}, "then": {"properties": {"credentials": {"type": "object", "default": {"data": {}}, "description": "This plugin doesn't use any authentication"}, "auth_type": {"description": "This plugin doesn't use any authentication machanism.\n"}}}}], "required": ["name", "description"]}, "uiSchema": {"ui:order": ["name", "description", "auth_type", "credentials", "*"], "auth_type": {"ui:enableMarkdownInDescription": true}, "credentials": {"ui:widget": "object", "ui:enableMarkdownInDescription": true, "data": {"ui:order": ["*"], "ui:field": "ObjectField", "X-Api-Key": {"ui:title": "X-Api-Key"}, "api_key": ***************************************************************************************************, "auth_uri": {"ui:title": "Auth URI"}, "client_email": {"ui:title": "Client Email"}, "client_id": {"ui:title": "Client ID"}, "client_x509_cert_url": {"ui:title": "Client X509 Cert URL"}, "private_key": {"ui:title": "Private Key"}, "private_key_id": {"ui:title": "Private Key ID"}, "project_id": {"ui:title": "Project ID"}, "scopes": {"ui:title": "<PERSON><PERSON><PERSON>"}, "token_uri": {"ui:title": "Token URI"}, "type": {"ui:title": "Type"}, "user_to_impersonate": {"ui:title": "User to Impersonate"}, "client_secret": {"ui:title": "Client Secret"}, "redirect_uri": {"ui:title": "Redirect URI"}, "scope": {"ui:title": "<PERSON><PERSON>"}, "tenant": {"ui:title": "Tenant"}, "token": {"ui:title": "Token"}}}, "spec": {"ui:order": ["base_url", "endpoints", "*"], "base_url": {"ui:enableMarkdownInDescription": true}, "endpoints": {"items": {"ui:order": ["path", "method", "description", "parameters", "headers", "*"], "parameters": {"items": {"ui:order": ["name", "type", "required", "location", "default", "sub_parameters", "prefix", "suffix", "definition", "fieldsList", "*"]}}, "auth_location": {"ui:enableMarkdownInDescription": true}, "basic_auth_header_name": {"ui:enableMarkdownInDescription": true}}}, "oauth_spec": {"ui:order": ["token_url", "request_format", "token_method", "parameters", "headers", "token_response_key", "grant_type", "scope", "*"]}}}}