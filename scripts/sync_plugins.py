import json

import psycopg2
import requests

env = "local"
env = "dev"
# env = "prod"

# Database connection parameters
db_params = {
    "dbname": "postgres",
    "user": "postgres",
    "password": "example",
    "host": "localhost",
    "port": "5432",
}

if env == "dev":
    db_params = {
        "dbname": "prisma",
        "user": "postgres",
        "password": "9Lp5pvaLb@hN^P",
        "host": "development.cfak6gy2myw2.us-east-1.rds.amazonaws.com",
        "port": "5432",
    }

# URL to fetch the plugins data
plugins_url = "http://localhost:5001/system_plugins"

# Read the ConfigurableSpecFields from spec.json
with open("spec.json", "r") as spec_file:
    configurable_spec_fields = json.load(spec_file)


# Function to check if a plugin template exists by name
def plugin_exists(cur, name, table_name="ToolTemplate"):
    print(f"Checking if plugin '{name}' exists...")
    query = f'SELECT "Id" FROM public."{table_name}" WHERE "DisplayName" = %s AND "ConfiguredBy" = %s'
    cur.execute(query, (name, "public"))
    result = cur.fetchone()
    if result:
        print(f"Plugin '{name}' found with ID: {result[0]}")
    else:
        print(f"Plugin '{name}' not found.")
    return result


# Function to insert a new plugin template into the database
import uuid


# Function to insert a new plugin template into the database
def insert_plugin_data(cur, plugin, table_name="ToolTemplate"):
    print(f"Inserting new plugin '{plugin['name']}'...")

    # Default SQL for non-"Tool" tables
    sql = f"""
  INSERT INTO public."{table_name}" (
    "Spec", "ConfigurableSpecFields", "Type", "LLMType", "ImgUrl", 
    "CreatedAt", "UpdatedAt", "DisplayDescription", 
    "DisplayName", "CategoryTemplateId", "ConfiguredBy"
  )
  VALUES (%s, %s, %s, %s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, %s, %s, NULL, %s)
  """

    # Modify SQL and generate UUID when table_name is "Tool"
    if table_name == "Tool":
        plugin_id = str(uuid.uuid4())
        sql = f"""
    INSERT INTO public."{table_name}" (
      "Id", "Spec", "ConfigurableSpecFields", "Type", "LLMType", "ImgUrl", 
      "CreatedAt", "UpdatedAt", "DisplayDescription", 
      "DisplayName", "ConfiguredBy"
    )
    VALUES (%s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, %s, %s, %s)
    """
        cur.execute(
            sql,
            (
                plugin_id,
                json.dumps(plugin),
                json.dumps(configurable_spec_fields),
                "plugin",
                "plugin",
                plugin.get("imgUrl", ""),
                plugin["spec"]["card_description"],
                plugin["spec"]["card_name"],
                "public",
            ),
        )
        print(f"Plugin '{plugin['name']}' inserted successfully with ID: {plugin_id}.")
    else:
        cur.execute(
            sql,
            (
                json.dumps(plugin),
                json.dumps(configurable_spec_fields),
                "plugin",
                "plugin",
                plugin.get("imgUrl", ""),
                plugin["spec"]["card_description"],
                plugin["spec"]["card_name"],
                "public",
            ),
        )
        print(f"Plugin '{plugin['name']}' inserted successfully.")


# Function to update an existing plugin template
def update_plugin_data(cur, plugin, plugin_id, table_name="ToolTemplate"):
    print(f"Updating plugin '{plugin['name']}' with ID: {plugin_id}...")
    sql = f"""
  UPDATE public."{table_name}"
  SET "Spec" = %s, "ConfigurableSpecFields" = %s, "ImgUrl" = %s, 
      "UpdatedAt" = CURRENT_TIMESTAMP, "DisplayDescription" = %s, 
      "DisplayName" = %s
  WHERE "Id" = %s
  """
    # encrypt credentials if Tool - make a copy
    if table_name == "Tool":
        print("Encrypting credentials...")
        plugin = plugin.copy()
        plugin["credentials"] = plugin["credentials"].copy()
        plugin["credentials"] = encrypt_data(plugin["credentials"])

    cur.execute(
        sql,
        (
            json.dumps(plugin),
            json.dumps(configurable_spec_fields),
            plugin.get("ImgUrl", ""),
            plugin["spec"]["card_description"],
            plugin["spec"]["card_name"],
            plugin_id,
        ),
    )
    print(f"Plugin '{plugin['name']}' updated successfully.")


# Connect to the database
print("Connecting to the database...")
conn = psycopg2.connect(**db_params)

try:
    # Fetch the plugins data
    print(f"Fetching plugins data from {plugins_url}...")
    response = requests.get(plugins_url)
    response.raise_for_status()
    plugins = response.json()
    print(f"Fetched {len(plugins)} plugins.")

    def encrypt_data(data):
        url = "http://localhost:8000/internal/api/llm/encrypt"
        headers = {"Content-Type": "application/json"}
        payload = {"data": data}

        try:
            response = requests.post(url, json=payload, headers=headers)
            response.raise_for_status()
            return {
                "data": response.json()["encryptedData"],
            }
        except requests.exceptions.RequestException as e:
            raise Exception(f"Failed to call encrypt endpoint: {e}")

    with conn.cursor() as cur:
        for plugin in plugins:
            plugin["version"] = 2
            plugin["description"] = plugin["spec"]["description"]
            plugin["name"] = plugin["spec"]["name"]
            plugin["spec"]["card_description"] = (
                "[V2 Final Test] " + plugin["spec"]["card_description"]
            )
            if "credentials" not in plugin:
                plugin["credentials"] = {"data": {}}
            if "data" not in plugin["credentials"]:
                plugin["credentials"]["data"] = {}
            print(plugin["spec"]["name"])
            if plugin["spec"]["is_tool"] == True:
                plugin_id = plugin_exists(cur, plugin["spec"]["card_name"], "Tool")
                if plugin_id:
                    update_plugin_data(cur, plugin, plugin_id[0], "Tool")
                else:
                    insert_plugin_data(cur, plugin, "Tool")
            else:
                plugin_id = plugin_exists(cur, plugin["spec"]["card_name"])
                if plugin_id:
                    update_plugin_data(cur, plugin, plugin_id[0])
                else:
                    insert_plugin_data(cur, plugin)

    # Commit the transaction
    print("Committing transaction...")
    conn.commit()
    print("Transaction committed successfully.")

finally:
    # Close the database connection
    print("Closing database connection...")
    conn.close()
    print("Database connection closed.")
